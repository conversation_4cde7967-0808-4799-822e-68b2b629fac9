{% extends "base.html" %}

{% block title %}实验室安全检查系统 - 安全检查活动{% endblock %}

{% block head %}
{{ super() }}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block breadcrumb %}
<li><a href="{{ url_for('admin.inspection_activities') }}">检查活动</a></li>
<li class="active">安全检查</li>
{% endblock %}

{% block scripts %}
<!-- 引入检查活动管理页面专用模块化JavaScript -->
{% for js_url in webpack_js('inspection-activities') %}
<script src="{{ js_url }}"></script>
{% endfor %}

<script>
// toggleActionButtons函数已移至JavaScript模块中

// 强化的页面初始化逻辑 - 解决首次加载模块缺失问题
function initInspectionActivitiesPage() {
    console.log('🚀 开始初始化检查活动页面');

    if (window.InspectionActivitiesPage) {
        console.log('✅ InspectionActivitiesPage模块已找到，开始初始化');
        window.InspectionActivitiesPage.init();

        // 验证关键函数是否已定义
        const requiredFunctions = ['toggleActionButtons', 'stopInspectionActivity', 'reactivateInspectionActivity'];
        const missingFunctions = requiredFunctions.filter(func => !window[func]);

        if (missingFunctions.length > 0) {
            console.warn('⚠️ 缺少函数:', missingFunctions);
            // 延迟重试
            setTimeout(() => {
                console.log('🔄 延迟重试初始化');
                window.InspectionActivitiesPage.init();
            }, 200);
        } else {
            console.log('✅ 所有必需函数已定义');
        }
    } else {
        console.error('❌ InspectionActivitiesPage模块未找到，延迟重试');
        // 延迟重试，最多重试3次
        if (!window.initRetryCount) window.initRetryCount = 0;
        if (window.initRetryCount < 3) {
            window.initRetryCount++;
            setTimeout(initInspectionActivitiesPage, 200);
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 检查活动页面DOM已加载');

    // 点击页面其他地方时关闭所有展开的按钮
    document.addEventListener('click', function(event) {
        // 检查点击的元素是否在操作按钮容器内
        if (!event.target.closest('.hover-action-container')) {
            // 关闭所有展开的按钮
            document.querySelectorAll('.hover-action-container.expanded').forEach(function(container) {
                container.classList.remove('expanded');
            });
        }
    });

    // 初始化页面模块
    initInspectionActivitiesPage();
});

// 如果DOM已经加载完成，立即初始化
if (document.readyState === 'loading') {
    console.log('📄 DOM正在加载，等待DOMContentLoaded事件');
} else {
    console.log('📄 DOM已加载完成，立即初始化');
    initInspectionActivitiesPage();
}

// AJAX加载时重新初始化
if (window.UnifiedAjaxMenu) {
    window.UnifiedAjaxMenu.registerPageInitializer('/admin/inspection_activities', function() {
        console.log('🔄 AJAX加载检查活动页面，重新初始化');
        window.initRetryCount = 0; // 重置重试计数
        initInspectionActivitiesPage();
    });
}
</script>

{% endblock %}

{% block styles %}
{{ super() }}

{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row">
        <div class="col-md-12">
            <h3 class="page-title">
                <i class="fas fa-clipboard-list"></i> 安全检查活动
                <small>管理</small>
            </h3>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="row">
        <div class="col-md-3 col-sm-6">
            <div class="stat-card">
                <div class="icon total-icon">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div class="number" id="total-count">{{ activities|length }}</div>
                <div class="title">活动总数</div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6">
            <div class="stat-card">
                <div class="icon active-icon">
                    <i class="fas fa-play-circle"></i>
                </div>
                <div class="number" id="active-count">{{ activities|selectattr('status', 'equalto',
                    'active')|list|length }}</div>
                <div class="title">进行中</div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6">
            <div class="stat-card">
                <div class="icon completed-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="number" id="completed-count">{{ activities|selectattr('status', 'equalto',
                    'completed')|list|length }}</div>
                <div class="title">已完成</div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6">
            <div class="stat-card">
                <div class="icon cancelled-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="number" id="cancelled-count">{{ activities|selectattr('status', 'equalto',
                    'cancelled')|list|length }}</div>
                <div class="title">已取消</div>
            </div>
        </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title"><i class="fas fa-filter"></i> 搜索与筛选</h3>
                    <div class="pull-right">
                        <a href="{{ url_for('admin.add_inspection_activity') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-plus"></i> 添加检查活动
                        </a>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="searchInput"><i class="fas fa-search"></i> 搜索活动</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="输入活动名称..." id="searchInput">
                                    <span class="input-group-btn">
                                        <button class="btn btn-primary" type="button" id="searchBtn">
                                            <i class="fas fa-search"></i> 搜索
                                        </button>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- 预留空间，保持布局平衡 -->
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><i class="fas fa-filter"></i> 按状态筛选</label>
                                <div>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-default filter-btn filter-active"
                                            data-filter-type="status" data-filter="all">全部状态</button>
                                        <button type="button" class="btn btn-default filter-btn"
                                            data-filter-type="status" data-filter="active">进行中</button>
                                        <button type="button" class="btn btn-default filter-btn"
                                            data-filter-type="status" data-filter="completed">已完成</button>
                                        <button type="button" class="btn btn-default filter-btn"
                                            data-filter-type="status" data-filter="cancelled">已取消</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><i class="fas fa-list-alt"></i> 按类型筛选</label>
                                <div>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-default filter-btn filter-active"
                                            data-filter-type="type" data-filter="all">全部类型</button>
                                        <button type="button" class="btn btn-default filter-btn" data-filter-type="type"
                                            data-filter="daily">日常巡查</button>
                                        <button type="button" class="btn btn-default filter-btn" data-filter-type="type"
                                            data-filter="special">专项检查</button>
                                        <button type="button" class="btn btn-default filter-btn" data-filter-type="type"
                                            data-filter="annual">年度检查</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 活动列表 -->
    <div class="row">
        <div class="col-md-12">
            <div class="table-responsive">
                <table class="table table-striped table-bordered table-hover inspection-activities-table auto-width-table">
                    <!-- 表格标题行 -->
                    <thead>
                        <tr class="table-title-row">
                            <th colspan="9">
                                <i class="fas fa-list"></i> 安全检查活动列表
                                <!-- 回收站入口 -->
                                {% if current_user.is_admin() %}
                                <div class="recycle-bin-entry">
                                    <a href="{{ url_for('admin.inspection_activities_recycle_bin') }}"
                                        class="btn btn-sm btn-warning" title="回收站">
                                        <i class="fa fa-trash"></i> 回收站
                                        <span class="badge" id="recycleCount">0</span>
                                    </a>
                                </div>
                                {% endif %}
                            </th>
                        </tr>
                        <!-- 表头行 -->
                        <tr>
                            <th>序号</th>
                            <th>活动名称</th>
                            <th>检查类型</th>
                            <th>巡查对象</th>
                            <th>检查时段</th>
                            <th>活动状态</th>
                            <th>通报状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if activities %}
                        {% for activity in activities %}
                        <tr class="activity-table-row" data-activity-id="{{ activity.id }}"
                            data-status="{{ activity.status }}" data-name="{{ activity.name }}">
                            <td>{{ loop.index }}</td>
                            <td>{{ activity.name }}</td>
                            <td>
                                {% if activity.inspection_type == 'daily' %}
                                <span class="label label-primary">日常巡查</span>
                                {% elif activity.inspection_type == 'special' %}
                                <span class="label label-warning">专项检查</span>
                                {% elif activity.inspection_type == 'annual' %}
                                <span class="label label-danger">年度检查</span>
                                {% else %}
                                <span class="label label-default">{{ activity.inspection_type }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if activity.inspection_target_type == 'department' %}
                                <span class="label label-info">部门</span>
                                {% elif activity.inspection_target_type == 'laboratory' %}
                                <span class="label label-success">实验室</span>
                                {% elif activity.inspection_target_type == 'school' %}
                                <span class="label label-primary">全校</span>
                                {% else %}
                                <span class="label label-default">{{ activity.inspection_target_type }}</span>
                                {% endif %}
                            </td>
                            <td>{{ activity.start_date }} 至 {{ activity.end_date }}</td>
                            <td>
                                {% if activity.status == 'active' %}
                                <span class="label label-success">进行中</span>
                                {% elif activity.status == 'completed' %}
                                <span class="label label-info">已完成</span>
                                {% elif activity.status == 'cancelled' %}
                                <span class="label label-danger">已取消</span>
                                {% endif %}
                            </td>
                            <td>
                                <!-- 通报状态显示 -->
                                {% if activity.report_content and not activity.report_content.is_deleted %}
                                {% if activity.report_content.is_published %}
                                <span class="label label-primary">
                                    <i class="fa fa-bullhorn"></i> 已发布通报
                                </span>
                                {% else %}
                                <span class="label label-warning">
                                    <i class="fa fa-file-text"></i> 有通报草稿
                                </span>
                                {% endif %}
                                {% else %}
                                <span class="text-muted">无通报</span>
                                {% endif %}
                            </td>
                            <td>{% if activity.created_at %}{{ activity.created_at.strftime('%Y-%m-%d %H:%M')
                                }}{% else %}-{% endif %}</td>
                            <td>
                                <!-- 检查按钮 -->
                                {% if activity.inspection_type == 'special' %}
                                <a href="{{ url_for('admin.special_check_inspection_activity', id=activity.id) }}"
                                    class="btn btn-sm btn-success" title="专项检查">
                                    <i class="fas fa-clipboard-check"></i> 检查
                                </a>
                                {% else %}
                                <a href="{{ url_for('admin.check_inspection_activity', id=activity.id) }}"
                                    class="btn btn-sm btn-success" title="检查">
                                    <i class="fas fa-clipboard-check"></i> 检查
                                </a>
                                {% endif %}

                                <!-- 隐患草稿按钮 -->
                                <a href="{{ url_for('admin.activity_hazard_drafts', activity_id=activity.id) }}"
                                    class="btn btn-sm btn-warning" title="隐患草稿">
                                    <i class="fas fa-file-alt"></i> 隐患草稿
                                    <span class="draft-badge" id="draft-count-{{ activity.id }}">
                                        {% if draft_counts and activity.id in draft_counts %}{{
                                        draft_counts[activity.id] }}{% else %}0{% endif %}
                                    </span>
                                </a>

                                <!-- 查看记录按钮 -->
                                <a href="{{ url_for('admin.inspection_records', activity_id=activity.id) }}"
                                    class="btn btn-sm btn-primary" title="查看记录">
                                    <i class="fas fa-clipboard-list"></i> 查看记录
                                </a>

                                <!-- 点击展开操作按钮 -->
                                <div class="hover-action-container" onclick="toggleActionButtons(this)">
                                    <!-- 默认显示的提示按钮 -->
                                    <div class="action-trigger">
                                        <span class="btn btn-sm btn-default">
                                            <i class="fas fa-ellipsis-h"></i> 更多操作
                                        </span>
                                    </div>

                                    <!-- 悬停时显示的具体按钮 -->
                                    <div class="action-buttons">
                                        <!-- 通报相关操作 -->
                                        {% if activity.report_content and not activity.report_content.is_deleted %}
                                            {% if activity.report_content.is_published %}
                                            <a href="{{ url_for('admin.activity_report', id=activity.id) }}"
                                               class="btn btn-sm btn-info" title="查看通报">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="javascript:void(0)" onclick="unpublishReport({{ activity.id }})"
                                               class="btn btn-sm btn-warning" title="取消发布通报">
                                                <i class="fas fa-times"></i>
                                            </a>
                                            {% else %}
                                            <a href="{{ url_for('admin.activity_report', id=activity.id) }}"
                                               class="btn btn-sm btn-info" title="编辑通报">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="javascript:void(0)" onclick="deleteReportDraft({{ activity.id }})"
                                               class="btn btn-sm btn-danger" title="删除通报草稿">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                            {% endif %}
                                        {% elif activity.status == 'completed' %}
                                            <a href="{{ url_for('admin.activity_report', id=activity.id) }}"
                                               class="btn btn-sm btn-success" title="创建通报">
                                                <i class="fas fa-file-alt"></i>
                                            </a>
                                        {% endif %}

                                        <!-- 基本操作 -->
                                        <a href="{{ url_for('admin.edit_inspection_activity', id=activity.id) }}"
                                           class="btn btn-sm btn-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('admin.copy_inspection_activity', id=activity.id) }}"
                                           class="btn btn-sm btn-default" title="复制">
                                            <i class="fas fa-copy"></i>
                                        </a>

                                        <!-- 状态操作 -->
                                        {% if activity.status == 'active' %}
                                        <a href="javascript:void(0)"
                                           onclick="stopInspectionActivity({{ activity.id }}, '{{ activity.name }}')"
                                           class="btn btn-sm btn-warning" title="停止检查">
                                            <i class="fas fa-stop"></i>
                                        </a>
                                        {% elif activity.status == 'completed' %}
                                        <a href="javascript:void(0)"
                                           onclick="reactivateInspectionActivity({{ activity.id }}, '{{ activity.name }}')"
                                           class="btn btn-sm btn-success" title="重新激活">
                                            <i class="fas fa-play"></i>
                                        </a>
                                        {% endif %}

                                        <!-- 删除操作 -->
                                        <a href="{{ url_for('admin.delete_inspection_activity', id=activity.id) }}"
                                           onclick="return confirmDeleteActivity({{ activity.id }}, '{{ activity.name }}', {{ 'true' if activity.report_content and not activity.report_content.is_deleted and activity.report_content.is_published else 'false' }})"
                                           class="btn btn-sm btn-danger" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                        {% else %}
                        <tr>
                            <td colspan="9" class="text-center">暂无检查活动</td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

{% endblock %}