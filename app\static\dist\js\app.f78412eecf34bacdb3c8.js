/*! For license information please see app.f78412eecf34bacdb3c8.js.LICENSE.txt */
!function(){"use strict";var e,t={165:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function c(n,o,r,a){var c=o&&o.prototype instanceof s?o:s,u=Object.create(c.prototype);return i(u,"_invoke",function(n,o,i){var r,a,c,s=0,u=i||[],d=!1,f={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return r=t,a=0,c=e,f.n=n,l}};function v(n,o){for(a=n,c=o,t=0;!d&&s&&!i&&t<u.length;t++){var i,r=u[t],v=f.p,p=r[2];n>3?(i=p===o)&&(a=r[4]||3,c=r[5]===e?r[3]:r[5],r[4]=3,r[5]=e):r[0]<=v&&((i=n<2&&v<r[1])?(a=0,f.v=o,f.n=r[1]):v<p&&(i=n<3||r[0]>o||o>p)&&(r[4]=n,r[5]=o,f.n=p,a=0))}if(i||n>1)return l;throw d=!0,o}return function(i,u,p){if(s>1)throw TypeError("Generator is already running");for(d&&1===u&&v(u,p),a=u,c=p;(t=a<2?e:c)||!d;){r||(a?a<3?(a>1&&(f.n=-1),v(a,c)):f.n=c:f.v=c);try{if(s=2,r){if(a||(i="next"),t=r[i]){if(!(t=t.call(r,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,a<2&&(a=0)}else 1===a&&(t=r.return)&&t.call(r),a<2&&(c=TypeError("The iterator does not provide a '"+i+"' method"),a=1);r=e}else if((t=(d=f.n<0)?c:n.call(o,f))!==l)break}catch(t){r=e,a=1,c=t}finally{s=1}}return{value:t,done:d}}}(n,r,a),!0),u}var l={};function s(){}function u(){}function d(){}t=Object.getPrototypeOf;var f=[][r]?t(t([][r]())):(i(t={},r,(function(){return this})),t),v=d.prototype=s.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,i(e,a,"GeneratorFunction")),e.prototype=Object.create(v),e}return u.prototype=d,i(v,"constructor",d),i(d,"constructor",u),u.displayName="GeneratorFunction",i(d,a,"GeneratorFunction"),i(v),i(v,a,"Generator"),i(v,r,(function(){return this})),i(v,"toString",(function(){return"[object Generator]"})),(o=function(){return{w:c,m:p}})()}function i(e,t,n,o){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}i=function(e,t,n,o){if(t)r?r(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{function a(t,n){i(e,t,(function(e){return this._invoke(t,n,e)}))}a("next",0),a("throw",1),a("return",2)}},i(e,t,n,o)}function r(e,t,n,o,i,r,a){try{var c=e[r](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(o,i)}function a(e){return function(){var t=this,n=arguments;return new Promise((function(o,i){var a=e.apply(t,n);function c(e){r(a,o,i,c,l,"next",e)}function l(e){r(a,o,i,c,l,"throw",e)}c(void 0)}))}}function c(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,l(o.key),o)}}function l(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var s=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.currentStatus="submitted",this.selectedHazards=[]},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("隐患审核页面已初始化，跳过重复初始化"):(console.log("📋 初始化隐患审核页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化隐患审核页面功能"),this.initDatePickers(),this.initSelects(),this.initTabSwitching(),this.initBatchOperations(),this.initFilters(),this.initModals(),this.loadHazards(this.currentStatus),this.defineGlobalFunctions(),console.log("✅ 隐患审核页面功能初始化完成")}},{key:"initDatePickers",value:function(){window.$&&$.fn.datepicker&&$(".datepicker").datepicker({format:"yyyy-mm-dd",language:"zh-CN",autoclose:!0,todayHighlight:!0,clearBtn:!0})}},{key:"initSelects",value:function(){var e=this;window.$&&$.fn.selectpicker&&$(".selectpicker").selectpicker();var t=document.getElementById("departmentFilter");t&&t.addEventListener("change",(function(){e.filterLaboratoriesByDepartment()}))}},{key:"filterLaboratoriesByDepartment",value:(d=a(o().m((function e(){var t,n,i,r;return o().w((function(e){for(;;)switch(e.n){case 0:return t=document.getElementById("departmentFilter").value,e.p=1,e.n=2,fetch("/admin/api/laboratories/by_department/".concat(t));case 2:return n=e.v,e.n=3,n.json();case 3:i=e.v,this.updateLaboratoryOptions(i),e.n=5;break;case 4:e.p=4,r=e.v,console.error("加载实验室列表失败:",r);case 5:return e.a(2)}}),e,this,[[1,4]])}))),function(){return d.apply(this,arguments)})},{key:"updateLaboratoryOptions",value:function(e){var t=document.getElementById("laboratoryFilter");t&&(t.innerHTML='<option value="">全部实验室</option>',e.forEach((function(e){var n=document.createElement("option");n.value=e.id,n.textContent=e.name,t.appendChild(n)})),window.$&&$.fn.selectpicker&&$(t).selectpicker("refresh"))}},{key:"initTabSwitching",value:function(){var e=this;document.querySelectorAll('[data-toggle="tab"]').forEach((function(t){t.addEventListener("click",(function(t){t.preventDefault();var n=t.target.getAttribute("data-status");e.switchTab(n)}))}))}},{key:"switchTab",value:function(e){console.log("切换到标签页:",e),this.currentStatus=e,this.selectedHazards=[],this.updateTabState(e),this.loadHazards(e),this.updateBatchOperationsVisibility()}},{key:"updateTabState",value:function(e){document.querySelectorAll('[data-toggle="tab"]').forEach((function(e){e.classList.remove("active")}));var t=document.querySelector('[data-status="'.concat(e,'"]'));t&&t.classList.add("active")}},{key:"initBatchOperations",value:function(){var e=this,t=document.getElementById("selectAll");t&&t.addEventListener("change",(function(t){e.toggleSelectAll(t.target.checked)}));var n=document.getElementById("batchPassBtn");n&&n.addEventListener("click",(function(){e.batchApprove()}));var o=document.getElementById("batchRejectBtn");o&&o.addEventListener("click",(function(){e.showBatchRejectModal()}));var i=document.getElementById("batchDeleteBtn");i&&i.addEventListener("click",(function(){e.batchDelete()}));var r=document.getElementById("pendingNotifyBtn");r&&r.addEventListener("click",(function(){e.showPendingNotifyModal()}))}},{key:"toggleSelectAll",value:function(e){document.querySelectorAll(".hazard-checkbox").forEach((function(t){t.checked=e})),this.updateSelectedHazards()}},{key:"updateSelectedHazards",value:function(){var e=document.querySelectorAll(".hazard-checkbox:checked");this.selectedHazards=Array.from(e).map((function(e){return e.value})),this.updateBatchOperationsVisibility()}},{key:"updateBatchOperationsVisibility",value:function(){var e=document.getElementById("batchOperations");e&&(this.selectedHazards.length>0?e.style.display="block":e.style.display="none")}},{key:"initFilters",value:function(){var e=this,t=document.getElementById("searchBtn");t&&t.addEventListener("click",(function(){e.applyFilters()}));var n=document.getElementById("resetBtn");n&&n.addEventListener("click",(function(){e.resetFilters()}))}},{key:"applyFilters",value:function(){this.loadHazards(this.currentStatus)}},{key:"resetFilters",value:function(){document.getElementById("departmentFilter").value="",document.getElementById("laboratoryFilter").value="",document.getElementById("levelFilter").value="",document.getElementById("startDate").value="",document.getElementById("endDate").value="",window.$&&$.fn.selectpicker&&$(".selectpicker").selectpicker("refresh"),this.loadHazards(this.currentStatus)}},{key:"initModals",value:function(){var e=this,t=document.getElementById("confirmBatchReject");t&&t.addEventListener("click",(function(){e.confirmBatchReject()}))}},{key:"loadHazards",value:(u=a(o().m((function e(t){var n,i,r,a;return o().w((function(e){for(;;)switch(e.n){case 0:return console.log("加载隐患数据:",t),e.p=1,this.showLoading(),n=this.buildQueryParams(t),e.n=2,fetch("/admin/api/hazards/review?".concat(n.toString()));case 2:return i=e.v,e.n=3,i.json();case 3:(r=e.v).success?(this.displayHazards(r.hazards),this.updatePagination(r.pagination)):(console.error("加载隐患数据失败:",r.message),this.showError(r.message||"加载数据失败")),e.n=5;break;case 4:e.p=4,a=e.v,console.error("加载隐患数据异常:",a),this.showError("网络错误，请稍后重试");case 5:return e.p=5,this.hideLoading(),e.f(5);case 6:return e.a(2)}}),e,this,[[1,4,5,6]])}))),function(e){return u.apply(this,arguments)})},{key:"buildQueryParams",value:function(e){var t,n,o,i,r;return new URLSearchParams({status:e,department_id:(null===(t=document.getElementById("departmentFilter"))||void 0===t?void 0:t.value)||"",laboratory_id:(null===(n=document.getElementById("laboratoryFilter"))||void 0===n?void 0:n.value)||"",level:(null===(o=document.getElementById("levelFilter"))||void 0===o?void 0:o.value)||"",start_date:(null===(i=document.getElementById("startDate"))||void 0===i?void 0:i.value)||"",end_date:(null===(r=document.getElementById("endDate"))||void 0===r?void 0:r.value)||"",page:1})}},{key:"displayHazards",value:function(e){var t=this,n=document.querySelector("#hazardsTable tbody");n&&(n.innerHTML="",e&&0!==e.length?(e.forEach((function(e){var o=t.createHazardRow(e);n.appendChild(o)})),this.bindRowEvents()):n.innerHTML='<tr><td colspan="8" class="text-center text-muted">暂无数据</td></tr>')}},{key:"createHazardRow",value:function(e){var t=document.createElement("tr");return t.innerHTML='\n      <td>\n        <input type="checkbox" class="hazard-checkbox" value="'.concat(e.id,'">\n      </td>\n      <td>').concat(e.laboratory_name||"-","</td>\n      <td>").concat(e.level||"-",'</td>\n      <td title="').concat(e.description||"",'">\n        ').concat(this.truncateText(e.description,50),"\n      </td>\n      <td>").concat(e.inspector_name||"-","</td>\n      <td>").concat(e.created_at||"-",'</td>\n      <td>\n        <span class="label label-').concat(this.getStatusClass(e.status),'">\n          ').concat(this.getStatusText(e.status),'\n        </span>\n      </td>\n      <td>\n        <div class="btn-group btn-group-xs">\n          <button type="button" class="btn btn-info view-btn" data-id="').concat(e.id,'">\n            查看\n          </button>\n          ').concat(this.getActionButtons(e),"\n        </div>\n      </td>\n    "),t}},{key:"truncateText",value:function(e,t){return e?e.length<=t?e:e.substring(0,t)+"...":"-"}},{key:"getStatusClass",value:function(e){return{submitted:"warning",approved:"success",rejected:"danger",rectified:"info"}[e]||"default"}},{key:"getStatusText",value:function(e){return{submitted:"待审核",approved:"已通过",rejected:"已拒绝",rectified:"已整改"}[e]||"未知"}},{key:"getActionButtons",value:function(e){return"submitted"===e.status?'\n        <button type="button" class="btn btn-success approve-btn" data-id="'.concat(e.id,'">\n          通过\n        </button>\n        <button type="button" class="btn btn-danger reject-btn" data-id="').concat(e.id,'">\n          不通过\n        </button>\n      '):""}},{key:"bindRowEvents",value:function(){var e=this;document.querySelectorAll(".hazard-checkbox").forEach((function(t){t.addEventListener("change",(function(){e.updateSelectedHazards()}))})),document.querySelectorAll(".view-btn").forEach((function(t){t.addEventListener("click",(function(t){var n=t.target.dataset.id;e.viewHazard(n)}))})),document.querySelectorAll(".approve-btn").forEach((function(t){t.addEventListener("click",(function(t){var n=t.target.dataset.id;e.approveHazard(n)}))})),document.querySelectorAll(".reject-btn").forEach((function(t){t.addEventListener("click",(function(t){var n=t.target.dataset.id;e.rejectHazard(n)}))}))}},{key:"viewHazard",value:function(e){window.open("/admin/hazards/".concat(e,"/detail"),"_blank")}},{key:"approveHazard",value:(s=a(o().m((function e(t){var n,i,r;return o().w((function(e){for(;;)switch(e.n){case 0:if(confirm("确定要通过这个隐患吗？")){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,fetch("/admin/api/hazards/".concat(t,"/approve"),{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":this.getCSRFToken()}});case 2:return n=e.v,e.n=3,n.json();case 3:(i=e.v).success?(alert("隐患已通过"),this.loadHazards(this.currentStatus)):alert("操作失败: "+i.message),e.n=5;break;case 4:e.p=4,r=e.v,console.error("通过隐患失败:",r),alert("操作失败，请重试");case 5:return e.a(2)}}),e,this,[[1,4]])}))),function(e){return s.apply(this,arguments)})},{key:"rejectHazard",value:(l=a(o().m((function e(t){var n,i,r,a;return o().w((function(e){for(;;)switch(e.n){case 0:if(n=prompt("请输入拒绝原因:")){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,fetch("/admin/api/hazards/".concat(t,"/reject"),{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":this.getCSRFToken()},body:JSON.stringify({reason:n})});case 2:return i=e.v,e.n=3,i.json();case 3:(r=e.v).success?(alert("隐患已拒绝"),this.loadHazards(this.currentStatus)):alert("操作失败: "+r.message),e.n=5;break;case 4:e.p=4,a=e.v,console.error("拒绝隐患失败:",a),alert("操作失败，请重试");case 5:return e.a(2)}}),e,this,[[1,4]])}))),function(e){return l.apply(this,arguments)})},{key:"batchApprove",value:(r=a(o().m((function e(){var t,n,i;return o().w((function(e){for(;;)switch(e.n){case 0:if(0!==this.selectedHazards.length){e.n=1;break}return alert("请选择要操作的隐患"),e.a(2);case 1:if(confirm("确定要批量通过 ".concat(this.selectedHazards.length," 个隐患吗？"))){e.n=2;break}return e.a(2);case 2:return e.p=2,e.n=3,fetch("/admin/api/hazards/batch_approve",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":this.getCSRFToken()},body:JSON.stringify({hazard_ids:this.selectedHazards})});case 3:return t=e.v,e.n=4,t.json();case 4:(n=e.v).success?(alert("成功通过 ".concat(n.count," 个隐患")),this.loadHazards(this.currentStatus),this.selectedHazards=[]):alert("批量操作失败: "+n.message),e.n=6;break;case 5:e.p=5,i=e.v,console.error("批量通过失败:",i),alert("操作失败，请重试");case 6:return e.a(2)}}),e,this,[[2,5]])}))),function(){return r.apply(this,arguments)})},{key:"showBatchRejectModal",value:function(){if(0!==this.selectedHazards.length){var e=document.getElementById("batchRejectModal");e&&window.$&&$(e).modal("show")}else alert("请选择要操作的隐患")}},{key:"confirmBatchReject",value:(i=a(o().m((function e(){var t,n,i,r,a;return o().w((function(e){for(;;)switch(e.n){case 0:if((t=document.getElementById("batchRejectReason").value).trim()){e.n=1;break}return alert("请输入拒绝原因"),e.a(2);case 1:return e.p=1,e.n=2,fetch("/admin/api/hazards/batch_reject",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":this.getCSRFToken()},body:JSON.stringify({hazard_ids:this.selectedHazards,reason:t})});case 2:return n=e.v,e.n=3,n.json();case 3:(i=e.v).success?(alert("成功拒绝 ".concat(i.count," 个隐患")),this.loadHazards(this.currentStatus),this.selectedHazards=[],(r=document.getElementById("batchRejectModal"))&&window.$&&$(r).modal("hide")):alert("批量操作失败: "+i.message),e.n=5;break;case 4:e.p=4,a=e.v,console.error("批量拒绝失败:",a),alert("操作失败，请重试");case 5:return e.a(2)}}),e,this,[[1,4]])}))),function(){return i.apply(this,arguments)})},{key:"batchDelete",value:(n=a(o().m((function e(){var t,n,i;return o().w((function(e){for(;;)switch(e.n){case 0:if(0!==this.selectedHazards.length){e.n=1;break}return alert("请选择要删除的隐患"),e.a(2);case 1:if(confirm("确定要删除 ".concat(this.selectedHazards.length," 个隐患吗？此操作不可恢复！"))){e.n=2;break}return e.a(2);case 2:return e.p=2,e.n=3,fetch("/admin/api/hazards/batch_delete",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":this.getCSRFToken()},body:JSON.stringify({hazard_ids:this.selectedHazards})});case 3:return t=e.v,e.n=4,t.json();case 4:(n=e.v).success?(alert("成功删除 ".concat(n.count," 个隐患")),this.loadHazards(this.currentStatus),this.selectedHazards=[]):alert("批量删除失败: "+n.message),e.n=6;break;case 5:e.p=5,i=e.v,console.error("批量删除失败:",i),alert("操作失败，请重试");case 6:return e.a(2)}}),e,this,[[2,5]])}))),function(){return n.apply(this,arguments)})},{key:"showPendingNotifyModal",value:function(){window.location.href="/admin/pending_notify_hazards"}},{key:"updatePagination",value:function(e){console.log("分页信息:",e)}},{key:"showLoading",value:function(){var e=document.querySelector("#hazardsTable tbody");e&&(e.innerHTML='<tr><td colspan="8" class="text-center"><i class="fas fa-spinner fa-spin"></i> 加载中...</td></tr>')}},{key:"hideLoading",value:function(){}},{key:"showError",value:function(e){var t=document.querySelector("#hazardsTable tbody");t&&(t.innerHTML='<tr><td colspan="8" class="text-center text-danger"><i class="fas fa-exclamation-triangle"></i> '.concat(e,"</td></tr>"))}},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"defineGlobalFunctions",value:function(){var e=this;window.loadHazards=function(t){return e.loadHazards(t)},window.approveHazard=function(t){return e.approveHazard(t)},window.rejectHazard=function(t){return e.rejectHazard(t)},window.viewHazard=function(t){return e.viewHazard(t)}}},{key:"destroy",value:function(){this.isInitialized=!1,this.selectedHazards=[],console.log("隐患审核页面模块已销毁")}}],t&&c(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,i,r,l,s,u,d}();window.IssueReviewPage=new s,t.A=s},370:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function c(n,o,r,a){var c=o&&o.prototype instanceof s?o:s,u=Object.create(c.prototype);return i(u,"_invoke",function(n,o,i){var r,a,c,s=0,u=i||[],d=!1,f={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return r=t,a=0,c=e,f.n=n,l}};function v(n,o){for(a=n,c=o,t=0;!d&&s&&!i&&t<u.length;t++){var i,r=u[t],v=f.p,p=r[2];n>3?(i=p===o)&&(a=r[4]||3,c=r[5]===e?r[3]:r[5],r[4]=3,r[5]=e):r[0]<=v&&((i=n<2&&v<r[1])?(a=0,f.v=o,f.n=r[1]):v<p&&(i=n<3||r[0]>o||o>p)&&(r[4]=n,r[5]=o,f.n=p,a=0))}if(i||n>1)return l;throw d=!0,o}return function(i,u,p){if(s>1)throw TypeError("Generator is already running");for(d&&1===u&&v(u,p),a=u,c=p;(t=a<2?e:c)||!d;){r||(a?a<3?(a>1&&(f.n=-1),v(a,c)):f.n=c:f.v=c);try{if(s=2,r){if(a||(i="next"),t=r[i]){if(!(t=t.call(r,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,a<2&&(a=0)}else 1===a&&(t=r.return)&&t.call(r),a<2&&(c=TypeError("The iterator does not provide a '"+i+"' method"),a=1);r=e}else if((t=(d=f.n<0)?c:n.call(o,f))!==l)break}catch(t){r=e,a=1,c=t}finally{s=1}}return{value:t,done:d}}}(n,r,a),!0),u}var l={};function s(){}function u(){}function d(){}t=Object.getPrototypeOf;var f=[][r]?t(t([][r]())):(i(t={},r,(function(){return this})),t),v=d.prototype=s.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,i(e,a,"GeneratorFunction")),e.prototype=Object.create(v),e}return u.prototype=d,i(v,"constructor",d),i(d,"constructor",u),u.displayName="GeneratorFunction",i(d,a,"GeneratorFunction"),i(v),i(v,a,"Generator"),i(v,r,(function(){return this})),i(v,"toString",(function(){return"[object Generator]"})),(o=function(){return{w:c,m:p}})()}function i(e,t,n,o){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}i=function(e,t,n,o){if(t)r?r(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{function a(t,n){i(e,t,(function(e){return this._invoke(t,n,e)}))}a("next",0),a("throw",1),a("return",2)}},i(e,t,n,o)}function r(e,t,n,o,i,r,a){try{var c=e[r](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(o,i)}function a(e){return function(){var t=this,n=arguments;return new Promise((function(o,i){var a=e.apply(t,n);function c(e){r(a,o,i,c,l,"next",e)}function l(e){r(a,o,i,c,l,"throw",e)}c(void 0)}))}}function c(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,l(o.key),o)}}function l(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var s=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("系统设置页面已初始化，跳过重复初始化"):(console.log("⚙️ 初始化系统设置页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化系统设置页面功能"),this.initFormHandling(),this.initTabSwitching(),this.initFileUpload(),this.initColorPicker(),this.initValidation(),this.defineGlobalFunctions(),console.log("✅ 系统设置页面功能初始化完成")}},{key:"initFormHandling",value:function(){var e=this;console.log("📝 初始化表单处理"),document.querySelectorAll("form").forEach((function(t){t.addEventListener("submit",(function(t){e.handleFormSubmit(t)}))})),document.querySelectorAll(".btn-reset").forEach((function(t){t.addEventListener("click",(function(t){e.handleReset(t)}))})),document.querySelectorAll(".btn-test").forEach((function(t){t.addEventListener("click",(function(t){e.handleTest(t)}))}))}},{key:"handleFormSubmit",value:(i=a(o().m((function e(t){var n,i,r,a,c;return o().w((function(e){for(;;)switch(e.n){case 0:if(t.preventDefault(),n=t.target,this.validateForm(n)){e.n=1;break}return e.a(2,!1);case 1:return this.showSubmitLoading(n),e.p=2,i=new FormData(n),e.n=3,fetch(n.action,{method:"POST",body:i,headers:{"X-CSRFToken":this.getCSRFToken()}});case 3:return r=e.v,e.n=4,r.json();case 4:(a=e.v).success?(this.showToast("设置保存成功","success"),a.reload&&setTimeout((function(){return window.location.reload()}),1e3)):this.showToast("保存失败: "+a.message,"error"),e.n=6;break;case 5:e.p=5,c=e.v,console.error("保存设置失败:",c),this.showToast("保存失败，请重试","error");case 6:return e.p=6,this.hideSubmitLoading(n),e.f(6);case 7:return e.a(2)}}),e,this,[[2,5,6,7]])}))),function(e){return i.apply(this,arguments)})},{key:"handleReset",value:function(e){var t=e.target.closest("form");t&&confirm("确定要重置所有设置吗？")&&(t.reset(),this.showToast("设置已重置","info"))}},{key:"handleTest",value:(n=a(o().m((function e(t){var n,i,r,a,c,l;return o().w((function(e){for(;;)switch(e.n){case 0:if(n=t.target,i=n.getAttribute("data-test-type")){e.n=1;break}return e.a(2);case 1:return r=n.innerHTML,n.disabled=!0,n.innerHTML='<i class="fas fa-spinner fa-spin"></i> 测试中...',e.p=2,e.n=3,fetch("/admin/api/test_connection/".concat(i),{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":this.getCSRFToken()},body:JSON.stringify(this.getTestData(i))});case 3:return a=e.v,e.n=4,a.json();case 4:(c=e.v).success?this.showToast("连接测试成功","success"):this.showToast("连接测试失败: "+c.message,"error"),e.n=6;break;case 5:e.p=5,l=e.v,console.error("测试连接失败:",l),this.showToast("测试失败，请重试","error");case 6:return e.p=6,n.disabled=!1,n.innerHTML=r,e.f(6);case 7:return e.a(2)}}),e,this,[[2,5,6,7]])}))),function(e){return n.apply(this,arguments)})},{key:"getTestData",value:function(e){var t,n,o,i,r,a,c,l,s,u,d,f,v,p={};switch(e){case"database":p.host=null===(t=document.getElementById("db_host"))||void 0===t?void 0:t.value,p.port=null===(n=document.getElementById("db_port"))||void 0===n?void 0:n.value,p.username=null===(o=document.getElementById("db_username"))||void 0===o?void 0:o.value,p.password=null===(i=document.getElementById("db_password"))||void 0===i?void 0:i.value,p.database=null===(r=document.getElementById("db_database"))||void 0===r?void 0:r.value;break;case"email":p.smtp_server=null===(a=document.getElementById("smtp_server"))||void 0===a?void 0:a.value,p.smtp_port=null===(c=document.getElementById("smtp_port"))||void 0===c?void 0:c.value,p.smtp_username=null===(l=document.getElementById("smtp_username"))||void 0===l?void 0:l.value,p.smtp_password=null===(s=document.getElementById("smtp_password"))||void 0===s?void 0:s.value;break;case"ldap":p.ldap_server=null===(u=document.getElementById("ldap_server"))||void 0===u?void 0:u.value,p.ldap_port=null===(d=document.getElementById("ldap_port"))||void 0===d?void 0:d.value,p.ldap_username=null===(f=document.getElementById("ldap_username"))||void 0===f?void 0:f.value,p.ldap_password=null===(v=document.getElementById("ldap_password"))||void 0===v?void 0:v.value}return p}},{key:"initTabSwitching",value:function(){var e=this;console.log("📑 初始化标签页切换"),document.querySelectorAll(".nav-tabs a").forEach((function(t){t.addEventListener("click",(function(n){n.preventDefault(),e.switchTab(t)}))}));var t=document.querySelector(".nav-tabs .active a");t&&this.switchTab(t)}},{key:"switchTab",value:function(e){var t=e.getAttribute("href");document.querySelectorAll(".nav-tabs li").forEach((function(e){e.classList.remove("active")})),document.querySelectorAll(".tab-pane").forEach((function(e){e.classList.remove("active")})),e.parentNode.classList.add("active");var n=document.querySelector(t);n&&n.classList.add("active")}},{key:"initFileUpload",value:function(){var e=this;console.log("📁 初始化文件上传"),document.querySelectorAll('input[type="file"]').forEach((function(t){t.addEventListener("change",(function(t){e.handleFileUpload(t)}))}))}},{key:"handleFileUpload",value:function(e){var t=e.target,n=t.files[0];n&&(this.validateFile(n,t)?this.showFilePreview(n,t):t.value="")}},{key:"validateFile",value:function(e,t){var n=t.getAttribute("data-allowed-types"),o=t.getAttribute("data-max-size");if(n){var i=n.split(","),r=e.type;if(!i.some((function(e){return r.includes(e)})))return this.showToast("文件类型不支持","error"),!1}if(o){var a=1024*parseInt(o)*1024;if(e.size>a)return this.showToast("文件大小不能超过".concat(o,"MB"),"error"),!1}return!0}},{key:"showFilePreview",value:function(e,t){var n=t.parentNode.querySelector(".file-preview");if(n)if(e.type.startsWith("image/")){var o=new FileReader;o.onload=function(t){n.innerHTML='\n          <img src="'.concat(t.target.result,'" alt="预览" style="max-width: 200px; max-height: 200px;">\n          <p>').concat(e.name,"</p>\n        ")},o.readAsDataURL(e)}else n.innerHTML='\n        <i class="fas fa-file fa-3x"></i>\n        <p>'.concat(e.name,"</p>\n      ")}},{key:"initColorPicker",value:function(){var e=this;console.log("🎨 初始化颜色选择器"),document.querySelectorAll('input[type="color"]').forEach((function(t){t.addEventListener("change",(function(t){e.handleColorChange(t)}))}))}},{key:"handleColorChange",value:function(e){var t=e.target,n=t.value,o=t.parentNode.querySelector(".color-preview");o&&(o.style.backgroundColor=n),"theme_color"===t.id&&this.previewThemeColor(n)}},{key:"previewThemeColor",value:function(e){var t=document.getElementById("theme-preview-style");t||((t=document.createElement("style")).id="theme-preview-style",document.head.appendChild(t)),t.textContent="\n      .navbar-brand { color: ".concat(e," !important; }\n      .btn-primary { background-color: ").concat(e," !important; border-color: ").concat(e," !important; }\n      .nav-tabs .active a { border-bottom-color: ").concat(e," !important; }\n    ")}},{key:"initValidation",value:function(){var e=this;console.log("✅ 初始化验证"),document.querySelectorAll("input, select, textarea").forEach((function(t){t.addEventListener("blur",(function(t){e.validateField(t.target)}))}))}},{key:"validateField",value:function(e){var t=e.value.trim(),n=e.type,o=e.hasAttribute("required");if(this.clearFieldError(e),o&&!t)return this.showFieldError(e,"此字段为必填项"),!1;switch(n){case"email":if(t&&!this.isValidEmail(t))return this.showFieldError(e,"请输入有效的邮箱地址"),!1;break;case"url":if(t&&!this.isValidUrl(t))return this.showFieldError(e,"请输入有效的URL地址"),!1;break;case"number":var i=e.getAttribute("min"),r=e.getAttribute("max"),a=parseFloat(t);if(t&&isNaN(a))return this.showFieldError(e,"请输入有效的数字"),!1;if(i&&a<parseFloat(i))return this.showFieldError(e,"值不能小于".concat(i)),!1;if(r&&a>parseFloat(r))return this.showFieldError(e,"值不能大于".concat(r)),!1}return!0}},{key:"validateForm",value:function(e){var t=this,n=e.querySelectorAll("input, select, textarea"),o=!0;return n.forEach((function(e){t.validateField(e)||(o=!1)})),o}},{key:"showFieldError",value:function(e,t){e.classList.add("is-invalid");var n=document.createElement("div");n.className="invalid-feedback",n.textContent=t,e.parentNode.appendChild(n)}},{key:"clearFieldError",value:function(e){e.classList.remove("is-invalid");var t=e.parentNode.querySelector(".invalid-feedback");t&&t.remove()}},{key:"isValidEmail",value:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}},{key:"isValidUrl",value:function(e){try{return new URL(e),!0}catch(e){return!1}}},{key:"showSubmitLoading",value:function(e){var t=e.querySelector('[type="submit"]');t&&(t.disabled=!0,t.innerHTML='<i class="fas fa-spinner fa-spin"></i> 保存中...')}},{key:"hideSubmitLoading",value:function(e){var t=e.querySelector('[type="submit"]');t&&(t.disabled=!1,t.innerHTML='<i class="fas fa-save"></i> 保存设置')}},{key:"showToast",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",n=document.createElement("div");n.className="alert alert-".concat("error"===t?"danger":t," toast-message"),n.style.position="fixed",n.style.top="20px",n.style.right="20px",n.style.zIndex="9999",n.style.minWidth="200px",n.textContent=e,document.body.appendChild(n),setTimeout((function(){n.parentNode&&n.parentNode.removeChild(n)}),3e3)}},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"defineGlobalFunctions",value:function(){var e=this;window.testConnection=function(t){return e.handleTest({target:{getAttribute:function(){return t}}})},window.resetSettings=function(){return e.handleReset({target:document.querySelector("form")})}}},{key:"destroy",value:function(){var e=document.getElementById("theme-preview-style");e&&e.remove(),this.isInitialized=!1,console.log("系统设置页面模块已销毁")}}],t&&c(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,i}();window.SystemSettingsPage=new s,t.A=s},508:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,i(o.key),o)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var r=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("departments.html页面已初始化，跳过重复初始化"):(console.log("🔧 初始化departments.html页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化departments.html页面功能"),this.initBasicFeatures(),this.initFormHandling(),this.initTableActions(),this.defineGlobalFunctions(),console.log("✅ departments.html页面功能初始化完成")}},{key:"initBasicFeatures",value:function(){this.initTooltips(),this.initModals()}},{key:"initTooltips",value:function(){window.$&&$.fn.tooltip&&$('[data-toggle="tooltip"]').tooltip()}},{key:"initModals",value:function(){window.$&&$.fn.modal&&$(".modal").modal({backdrop:"static",keyboard:!1})}},{key:"initFormHandling",value:function(){var e=this;document.querySelectorAll("form").forEach((function(t){t.addEventListener("submit",(function(t){e.handleFormSubmit(t)}))}))}},{key:"handleFormSubmit",value:function(e){var t=e.target;if(!this.validateForm(t))return e.preventDefault(),!1;this.showSubmitLoading(t)}},{key:"validateForm",value:function(e){var t=this,n=e.querySelectorAll("[required]"),o=!0;return n.forEach((function(e){e.value.trim()||(t.showFieldError(e,"此字段为必填项"),o=!1)})),o}},{key:"showFieldError",value:function(e,t){var n=e.parentNode.querySelector(".field-error");n&&n.remove(),e.classList.add("is-invalid");var o=document.createElement("div");o.className="field-error text-danger small",o.textContent=t,e.parentNode.appendChild(o)}},{key:"showSubmitLoading",value:function(e){var t=e.querySelector('[type="submit"]');t&&(t.disabled=!0,t.innerHTML='<i class="fas fa-spinner fa-spin"></i> 提交中...')}},{key:"initTableActions",value:function(){var e=this;document.addEventListener("click",(function(t){t.target.classList.contains("btn-edit")?e.handleEdit(t.target):t.target.classList.contains("btn-delete")?e.handleDelete(t.target):t.target.classList.contains("btn-view")&&e.handleView(t.target)}))}},{key:"handleEdit",value:function(e){var t=e.getAttribute("data-id");t&&console.log("编辑项目:",t)}},{key:"handleDelete",value:function(e){var t=e.getAttribute("data-id");t&&confirm("确定要删除这个项目吗？")&&console.log("删除项目:",t)}},{key:"handleView",value:function(e){var t=e.getAttribute("data-id");t&&console.log("查看项目:",t)}},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"defineGlobalFunctions",value:function(){}},{key:"destroy",value:function(){this.isInitialized=!1,console.log("departments.html页面模块已销毁")}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();window.DepartmentsPage=new r,t.A=r},594:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,i(o.key),o)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var r=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("风险等级页面已初始化，跳过重复初始化"):(console.log("⚠️ 初始化风险等级页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化风险等级页面功能"),this.initBasicFeatures(),this.initFormHandling(),this.initTableActions(),this.defineGlobalFunctions(),console.log("✅ 风险等级页面功能初始化完成")}},{key:"initBasicFeatures",value:function(){window.$&&$.fn.tooltip&&$('[data-toggle="tooltip"]').tooltip(),window.$&&$.fn.modal&&$(".modal").modal({backdrop:"static",keyboard:!1})}},{key:"initFormHandling",value:function(){var e=this;document.querySelectorAll("form").forEach((function(t){t.addEventListener("submit",(function(n){if(!e.validateForm(t))return n.preventDefault(),!1;e.showSubmitLoading(t)}))}))}},{key:"validateForm",value:function(e){var t=this,n=e.querySelectorAll("[required]"),o=!0;return n.forEach((function(e){e.value.trim()||(t.showFieldError(e,"此字段为必填项"),o=!1)})),o}},{key:"showFieldError",value:function(e,t){var n=e.parentNode.querySelector(".field-error");n&&n.remove(),e.classList.add("is-invalid");var o=document.createElement("div");o.className="field-error text-danger small",o.textContent=t,e.parentNode.appendChild(o)}},{key:"showSubmitLoading",value:function(e){var t=e.querySelector('[type="submit"]');t&&(t.disabled=!0,t.innerHTML='<i class="fas fa-spinner fa-spin"></i> 提交中...')}},{key:"initTableActions",value:function(){var e=this;document.addEventListener("click",(function(t){t.target.classList.contains("btn-edit")?e.handleEdit(t.target):t.target.classList.contains("btn-delete")&&e.handleDelete(t.target)}))}},{key:"handleEdit",value:function(e){var t=e.getAttribute("data-id");t&&console.log("编辑风险等级:",t)}},{key:"handleDelete",value:function(e){var t=e.getAttribute("data-id");t&&confirm("确定要删除这个风险等级吗？")&&console.log("删除风险等级:",t)}},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"defineGlobalFunctions",value:function(){}},{key:"destroy",value:function(){this.isInitialized=!1,console.log("风险等级页面模块已销毁")}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();window.RiskLevelsPage=new r,t.A=r},928:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,i(o.key),o)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var r=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("数据编辑页面已初始化，跳过重复初始化"):(console.log("📝 初始化数据编辑页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化数据编辑页面功能"),this.initBasicFeatures(),this.initFormHandling(),this.initTableActions(),this.defineGlobalFunctions(),console.log("✅ 数据编辑页面功能初始化完成")}},{key:"initBasicFeatures",value:function(){this.initTooltips(),this.initModals()}},{key:"initTooltips",value:function(){window.$&&$.fn.tooltip&&$('[data-toggle="tooltip"]').tooltip()}},{key:"initModals",value:function(){window.$&&$.fn.modal&&$(".modal").modal({backdrop:"static",keyboard:!1})}},{key:"initFormHandling",value:function(){var e=this;document.querySelectorAll("form").forEach((function(t){t.addEventListener("submit",(function(t){e.handleFormSubmit(t)}))}))}},{key:"handleFormSubmit",value:function(e){var t=e.target;if(!this.validateForm(t))return e.preventDefault(),!1;this.showSubmitLoading(t)}},{key:"validateForm",value:function(e){var t=this,n=e.querySelectorAll("[required]"),o=!0;return n.forEach((function(e){e.value.trim()||(t.showFieldError(e,"此字段为必填项"),o=!1)})),o}},{key:"showFieldError",value:function(e,t){var n=e.parentNode.querySelector(".field-error");n&&n.remove(),e.classList.add("is-invalid");var o=document.createElement("div");o.className="field-error text-danger small",o.textContent=t,e.parentNode.appendChild(o)}},{key:"showSubmitLoading",value:function(e){var t=e.querySelector('[type="submit"]');t&&(t.disabled=!0,t.innerHTML='<i class="fas fa-spinner fa-spin"></i> 提交中...')}},{key:"initTableActions",value:function(){var e=this;document.addEventListener("click",(function(t){t.target.classList.contains("btn-edit")?e.handleEdit(t.target):t.target.classList.contains("btn-delete")?e.handleDelete(t.target):t.target.classList.contains("btn-view")&&e.handleView(t.target)}))}},{key:"handleEdit",value:function(e){var t=e.getAttribute("data-id");t&&console.log("编辑项目:",t)}},{key:"handleDelete",value:function(e){var t=e.getAttribute("data-id");t&&confirm("确定要删除这个项目吗？")&&console.log("删除项目:",t)}},{key:"handleView",value:function(e){var t=e.getAttribute("data-id");t&&console.log("查看项目:",t)}},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"defineGlobalFunctions",value:function(){}},{key:"destroy",value:function(){this.isInitialized=!1,console.log("数据编辑页面模块已销毁")}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();window.DataEditPage=new r,t.A=r},987:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,r,a,c=[],l=!0,s=!1;try{if(r=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(o=r.call(n)).done)&&(c.push(o.value),c.length!==t);l=!0);}catch(e){s=!0,i=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(e,t)||i(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t){if(e){if("string"==typeof e)return r(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function a(){var e,t,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.toStringTag||"@@toStringTag";function r(n,o,i,r){var a=o&&o.prototype instanceof s?o:s,u=Object.create(a.prototype);return c(u,"_invoke",function(n,o,i){var r,a,c,s=0,u=i||[],d=!1,f={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return r=t,a=0,c=e,f.n=n,l}};function v(n,o){for(a=n,c=o,t=0;!d&&s&&!i&&t<u.length;t++){var i,r=u[t],v=f.p,p=r[2];n>3?(i=p===o)&&(a=r[4]||3,c=r[5]===e?r[3]:r[5],r[4]=3,r[5]=e):r[0]<=v&&((i=n<2&&v<r[1])?(a=0,f.v=o,f.n=r[1]):v<p&&(i=n<3||r[0]>o||o>p)&&(r[4]=n,r[5]=o,f.n=p,a=0))}if(i||n>1)return l;throw d=!0,o}return function(i,u,p){if(s>1)throw TypeError("Generator is already running");for(d&&1===u&&v(u,p),a=u,c=p;(t=a<2?e:c)||!d;){r||(a?a<3?(a>1&&(f.n=-1),v(a,c)):f.n=c:f.v=c);try{if(s=2,r){if(a||(i="next"),t=r[i]){if(!(t=t.call(r,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,a<2&&(a=0)}else 1===a&&(t=r.return)&&t.call(r),a<2&&(c=TypeError("The iterator does not provide a '"+i+"' method"),a=1);r=e}else if((t=(d=f.n<0)?c:n.call(o,f))!==l)break}catch(t){r=e,a=1,c=t}finally{s=1}}return{value:t,done:d}}}(n,i,r),!0),u}var l={};function s(){}function u(){}function d(){}t=Object.getPrototypeOf;var f=[][o]?t(t([][o]())):(c(t={},o,(function(){return this})),t),v=d.prototype=s.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,c(e,i,"GeneratorFunction")),e.prototype=Object.create(v),e}return u.prototype=d,c(v,"constructor",d),c(d,"constructor",u),u.displayName="GeneratorFunction",c(d,i,"GeneratorFunction"),c(v),c(v,i,"Generator"),c(v,o,(function(){return this})),c(v,"toString",(function(){return"[object Generator]"})),(a=function(){return{w:r,m:p}})()}function c(e,t,n,o){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}c=function(e,t,n,o){if(t)i?i(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{function r(t,n){c(e,t,(function(e){return this._invoke(t,n,e)}))}r("next",0),r("throw",1),r("return",2)}},c(e,t,n,o)}function l(e,t,n,o,i,r,a){try{var c=e[r](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(o,i)}function s(e){return function(){var t=this,n=arguments;return new Promise((function(o,i){var r=e.apply(t,n);function a(e){l(r,o,i,a,c,"next",e)}function c(e){l(r,o,i,a,c,"throw",e)}a(void 0)}))}}function u(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,d(o.key),o)}}function d(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var f=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.tinymceEditor=null,this.currentEditId=null},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("公告管理页面已初始化，跳过重复初始化"):(console.log("📢 初始化公告管理页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化公告管理页面功能"),this.initTinyMCE(),this.initModals(),this.initFilters(),this.initFormHandling(),this.initTableActions(),this.defineGlobalFunctions(),console.log("✅ 公告管理页面功能初始化完成")}},{key:"initTinyMCE",value:function(){if(console.log("📝 初始化TinyMCE编辑器"),window.onerror=function(e,t,n,o,i){return console.error("全局错误:",e),console.error("错误源:",t),console.error("行号:",n),!1},"undefined"==typeof tinymce)return console.error("TinyMCE未加载"),void alert("编辑器加载失败，请刷新页面重试");console.log("TinyMCE加载成功，版本:",tinymce.majorVersion+"."+tinymce.minorVersion),this.setupTinyMCE()}},{key:"setupTinyMCE",value:function(){var e=this,t={selector:"#content",height:400,language:"zh_CN",plugins:["advlist","autolink","lists","link","image","charmap","print","preview","anchor","searchreplace","visualblocks","code","fullscreen","insertdatetime","media","table","paste","code","help","wordcount"],toolbar:"undo redo | formatselect | bold italic backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help",content_style:"body { font-family:Helvetica,Arial,sans-serif; font-size:14px }",setup:function(t){t.on("init",(function(){console.log("TinyMCE编辑器初始化完成"),e.tinymceEditor=t}))}};tinymce.init(t)}},{key:"initModals",value:function(){console.log("🔲 初始化模态框");var e=document.querySelector("#addAnnouncementModal .modal-content");e&&e.addEventListener("click",(function(e){e.stopPropagation()})),this.bindModalEvents()}},{key:"bindModalEvents",value:function(){var e=this,t=document.getElementById("addAnnouncementModal");t&&window.$&&($(t).on("show.bs.modal",(function(){e.resetForm()})),$(t).on("shown.bs.modal",(function(){e.focusFirstInput()})));var n=document.getElementById("editAnnouncementModal");n&&window.$&&$(n).on("show.bs.modal",(function(){e.loadEditData()}))}},{key:"resetForm",value:function(){var e=document.getElementById("announcementForm");e&&e.reset(),document.getElementById("announcement_id").value="",document.getElementById("announcementModalTitle").textContent="发布新公告",this.currentEditId=null,this.tinymceEditor&&this.tinymceEditor.setContent("")}},{key:"focusFirstInput",value:function(){var e=document.querySelector('#addAnnouncementModal input[type="text"]');e&&e.focus()}},{key:"loadEditData",value:function(){this.currentEditId?this.fetchAnnouncementData(this.currentEditId):console.warn("没有设置编辑ID")}},{key:"fetchAnnouncementData",value:(f=s(a().m((function e(t){var n,o,i;return a().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,fetch("/admin/api/announcements/".concat(t));case 1:return n=e.v,e.n=2,n.json();case 2:(o=e.v).success?this.populateEditForm(o.announcement):(console.error("获取公告数据失败:",o.message),alert("获取公告数据失败")),e.n=4;break;case 3:e.p=3,i=e.v,console.error("获取公告数据异常:",i),alert("网络错误，请重试");case 4:return e.a(2)}}),e,this,[[0,3]])}))),function(e){return f.apply(this,arguments)})},{key:"populateEditForm",value:function(e){["title","priority","status","publish_date","expire_date"].forEach((function(t){var n=document.getElementById("edit_".concat(t));n&&e[t]&&(n.value=e[t])})),this.tinymceEditor&&e.content&&this.tinymceEditor.setContent(e.content)}},{key:"initFilters",value:function(){console.log("🔍 初始化过滤器"),this.bindFilterEvents()}},{key:"bindFilterEvents",value:function(){var e=this,t=document.getElementById("searchBtn");t&&t.addEventListener("click",(function(){e.applyFilters()}));var n=document.getElementById("resetBtn");n&&n.addEventListener("click",(function(){e.resetFilters()}));var o=document.getElementById("status");o&&o.addEventListener("change",(function(){e.applyFilters()}));var i=document.getElementById("priority");i&&i.addEventListener("change",(function(){e.applyFilters()}))}},{key:"applyFilters",value:function(){var e=document.getElementById("filterForm");e&&e.submit()}},{key:"resetFilters",value:function(){document.getElementById("keyword").value="",document.getElementById("status").value="all",document.getElementById("priority").value="all",this.applyFilters()}},{key:"initFormHandling",value:function(){console.log("📝 初始化表单处理"),this.bindFormSubmitEvents()}},{key:"bindFormSubmitEvents",value:function(){var e=this;console.log("🔗 开始绑定表单提交事件");var t=document.getElementById("saveAnnouncement");console.log("🔍 查找保存按钮:",t),t?(console.log("✅ 找到保存按钮，绑定点击事件"),t.addEventListener("click",(function(t){console.log("🖱️ 保存按钮被点击"),t.preventDefault(),e.handleSaveClick()}))):console.error("❌ 未找到保存按钮 #saveAnnouncement");var n=document.getElementById("cancelAnnouncement");console.log("🔍 查找取消按钮:",n),n?(console.log("✅ 找到取消按钮，绑定点击事件"),n.addEventListener("click",(function(t){console.log("🖱️ 取消按钮被点击"),t.preventDefault(),e.closeModal("addAnnouncementModal")}))):console.error("❌ 未找到取消按钮 #cancelAnnouncement");var o=document.getElementById("closeAnnouncementModal");console.log("🔍 查找关闭按钮:",o),o?(console.log("✅ 找到关闭按钮，绑定点击事件"),o.addEventListener("click",(function(t){console.log("🖱️ 关闭按钮被点击"),t.preventDefault(),e.closeModal("addAnnouncementModal")}))):console.error("❌ 未找到关闭按钮 #closeAnnouncementModal");var i=document.getElementById("announcementForm");console.log("🔍 查找表单:",i),i?(console.log("✅ 找到表单，绑定提交事件"),i.addEventListener("submit",(function(t){console.log("📝 表单提交事件触发"),t.preventDefault(),e.handleSaveClick()}))):console.error("❌ 未找到表单 #announcementForm"),console.log("✅ 表单提交事件绑定完成")}},{key:"handleSaveClick",value:function(){console.log("🚀 handleSaveClick 被调用");var e=document.getElementById("announcement_id");console.log("🔍 查找announcement_id元素:",e);var t=e?e.value:"";console.log("📝 当前announcement_id值:",t),t&&""!==t.trim()?(console.log("✏️ 进入编辑模式"),this.submitEditForm()):(console.log("➕ 进入添加模式"),this.submitAddForm())}},{key:"submitAddForm",value:(d=s(a().m((function e(){var t,n,o,i,r;return a().w((function(e){for(;;)switch(e.n){case 0:if(console.log("📤 开始提交添加表单"),t=this.collectFormData("announcementForm"),console.log("📋 收集到的表单数据:",t),this.validateFormData(t)){e.n=1;break}return console.log("❌ 表单验证失败"),e.a(2);case 1:return console.log("✅ 表单验证通过，开始发送请求"),e.p=2,n=this.getCSRFToken(),console.log("🔐 CSRF令牌:",n),e.n=3,fetch("/admin/api/announcements",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":n},body:JSON.stringify(t)});case 3:return o=e.v,console.log("📡 收到响应:",o),console.log("📊 响应状态:",o.status),e.n=4,o.json();case 4:i=e.v,console.log("📄 响应数据:",i),i.success?(console.log("✅ 公告添加成功"),alert("公告添加成功"),this.closeModal("addAnnouncementModal"),window.location.reload()):(console.log("❌ 添加失败:",i.message),alert("添加失败: "+i.message)),e.n=6;break;case 5:e.p=5,r=e.v,console.error("💥 添加公告异常:",r),alert("添加失败，请重试");case 6:return e.a(2)}}),e,this,[[2,5]])}))),function(){return d.apply(this,arguments)})},{key:"submitEditForm",value:(l=s(a().m((function e(){var t,n,o,i;return a().w((function(e){for(;;)switch(e.n){case 0:if(t=this.collectFormData("announcementForm"),this.validateFormData(t)){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,fetch("/admin/api/announcements/".concat(this.currentEditId),{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":this.getCSRFToken()},body:JSON.stringify(t)});case 2:return n=e.v,e.n=3,n.json();case 3:(o=e.v).success?(alert("公告更新成功"),this.closeModal("addAnnouncementModal"),window.location.reload()):alert("更新失败: "+o.message),e.n=5;break;case 4:e.p=4,i=e.v,console.error("更新公告失败:",i),alert("更新失败，请重试");case 5:return e.a(2)}}),e,this,[[1,4]])}))),function(){return l.apply(this,arguments)})},{key:"collectFormData",value:function(e){var t,n=document.getElementById(e),r={},a=function(e){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=i(e))){t&&(e=t);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,a=!0,c=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return a=e.done,e},e:function(e){c=!0,r=e},f:function(){try{a||null==t.return||t.return()}finally{if(c)throw r}}}}(new FormData(n).entries());try{for(a.s();!(t=a.n()).done;){var c=o(t.value,2),l=c[0],s=c[1];r[l]=s}}catch(e){a.e(e)}finally{a.f()}return this.tinymceEditor&&(r.content=this.tinymceEditor.getContent()),r}},{key:"validateFormData",value:function(e){return e.title&&""!==e.title.trim()?!(!e.content||""===e.content.trim())||(alert("请输入公告内容"),!1):(alert("请输入公告标题"),!1)}},{key:"closeModal",value:function(e){var t=document.getElementById(e);t&&window.$&&$(t).modal("hide")}},{key:"initTableActions",value:function(){console.log("📊 初始化表格操作"),this.bindTableActionEvents()}},{key:"bindTableActionEvents",value:function(){var e=this;window.$?($(document).off("click",".delete-announcement").on("click",".delete-announcement",(function(t){t.preventDefault();var n=$(t.target).closest(".delete-announcement").data("id");e.deleteAnnouncement(n)})),$(document).off("click",".edit-announcement").on("click",".edit-announcement",(function(t){t.preventDefault();var n=$(t.target).closest(".edit-announcement").data("id");e.editAnnouncement(n)})),$(document).off("click",".toggle-status").on("click",".toggle-status",(function(t){t.preventDefault();var n=$(t.target).closest(".toggle-status").data("id");e.toggleAnnouncementStatus(n)}))):document.addEventListener("click",(function(t){if(t.target.classList.contains("delete-announcement")||t.target.closest(".delete-announcement")){var n=(t.target.classList.contains("delete-announcement")?t.target:t.target.closest(".delete-announcement")).getAttribute("data-id");e.deleteAnnouncement(n)}else if(t.target.classList.contains("edit-announcement")||t.target.closest(".edit-announcement")){var o=(t.target.classList.contains("edit-announcement")?t.target:t.target.closest(".edit-announcement")).getAttribute("data-id");e.editAnnouncement(o)}else if(t.target.classList.contains("toggle-status")||t.target.closest(".toggle-status")){var i=(t.target.classList.contains("toggle-status")?t.target:t.target.closest(".toggle-status")).getAttribute("data-id");e.toggleAnnouncementStatus(i)}}))}},{key:"editAnnouncement",value:(c=s(a().m((function e(t){var n,o,i,r;return a().w((function(e){for(;;)switch(e.n){case 0:return this.currentEditId=t,e.p=1,e.n=2,fetch("/admin/api/announcements/".concat(t));case 2:return n=e.v,e.n=3,n.json();case 3:(o=e.v).success?(this.fillEditForm(o.announcement),(i=document.getElementById("addAnnouncementModal"))&&window.$&&(document.getElementById("announcementModalTitle").textContent="编辑公告",$(i).modal("show"))):alert("加载公告数据失败: "+o.message),e.n=5;break;case 4:e.p=4,r=e.v,console.error("加载公告数据失败:",r),alert("加载失败，请重试");case 5:return e.a(2)}}),e,this,[[1,4]])}))),function(e){return c.apply(this,arguments)})},{key:"fillEditForm",value:function(e){document.getElementById("announcement_id").value=e.id,document.getElementById("title").value=e.title,document.getElementById("priority").value=e.priority,document.getElementById("is_active").value=e.is_active?"1":"0",e.start_date&&(document.getElementById("start_date").value=e.start_date),e.end_date&&(document.getElementById("end_date").value=e.end_date),this.tinymceEditor?this.tinymceEditor.setContent(e.content||""):document.getElementById("content").value=e.content||""}},{key:"deleteAnnouncement",value:(r=s(a().m((function e(t){var n,o,i;return a().w((function(e){for(;;)switch(e.n){case 0:if(confirm("确定要删除这个公告吗？")){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,fetch("/admin/api/announcements/".concat(t),{method:"DELETE",headers:{"X-CSRFToken":this.getCSRFToken()}});case 2:return n=e.v,e.n=3,n.json();case 3:(o=e.v).success?(alert("公告删除成功"),window.location.reload()):alert("删除失败: "+o.message),e.n=5;break;case 4:e.p=4,i=e.v,console.error("删除公告失败:",i),alert("删除失败，请重试");case 5:return e.a(2)}}),e,this,[[1,4]])}))),function(e){return r.apply(this,arguments)})},{key:"toggleAnnouncementStatus",value:(n=s(a().m((function e(t){var n,o,i;return a().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,fetch("/admin/api/announcements/".concat(t,"/toggle_status"),{method:"POST",headers:{"X-CSRFToken":this.getCSRFToken()}});case 1:return n=e.v,e.n=2,n.json();case 2:(o=e.v).success?window.location.reload():alert("状态切换失败: "+o.message),e.n=4;break;case 3:e.p=3,i=e.v,console.error("切换状态失败:",i),alert("操作失败，请重试");case 4:return e.a(2)}}),e,this,[[0,3]])}))),function(e){return n.apply(this,arguments)})},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"defineGlobalFunctions",value:function(){var e=this;window.editAnnouncement=function(t){return e.editAnnouncement(t)},window.deleteAnnouncement=function(t){return e.deleteAnnouncement(t)},window.toggleAnnouncementStatus=function(t){return e.toggleAnnouncementStatus(t)}}},{key:"destroy",value:function(){this.tinymceEditor&&(this.tinymceEditor.destroy(),this.tinymceEditor=null),this.isInitialized=!1,this.currentEditId=null,console.log("公告管理页面模块已销毁")}}],t&&u(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,r,c,l,d,f}();window.AnnouncementsPage=new f,t.A=f},1270:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,i(o.key),o)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var r=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("campuses.html页面已初始化，跳过重复初始化"):(console.log("🔧 初始化campuses.html页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化campuses.html页面功能"),this.initBasicFeatures(),this.initFormHandling(),this.initTableActions(),this.defineGlobalFunctions(),console.log("✅ campuses.html页面功能初始化完成")}},{key:"initBasicFeatures",value:function(){this.initTooltips(),this.initModals()}},{key:"initTooltips",value:function(){window.$&&$.fn.tooltip&&$('[data-toggle="tooltip"]').tooltip()}},{key:"initModals",value:function(){window.$&&$.fn.modal&&$(".modal").modal({backdrop:"static",keyboard:!1})}},{key:"initFormHandling",value:function(){var e=this;document.querySelectorAll("form").forEach((function(t){t.addEventListener("submit",(function(t){e.handleFormSubmit(t)}))}))}},{key:"handleFormSubmit",value:function(e){var t=e.target;if(!this.validateForm(t))return e.preventDefault(),!1;this.showSubmitLoading(t)}},{key:"validateForm",value:function(e){var t=this,n=e.querySelectorAll("[required]"),o=!0;return n.forEach((function(e){e.value.trim()||(t.showFieldError(e,"此字段为必填项"),o=!1)})),o}},{key:"showFieldError",value:function(e,t){var n=e.parentNode.querySelector(".field-error");n&&n.remove(),e.classList.add("is-invalid");var o=document.createElement("div");o.className="field-error text-danger small",o.textContent=t,e.parentNode.appendChild(o)}},{key:"showSubmitLoading",value:function(e){var t=e.querySelector('[type="submit"]');t&&(t.disabled=!0,t.innerHTML='<i class="fas fa-spinner fa-spin"></i> 提交中...')}},{key:"initTableActions",value:function(){var e=this;document.addEventListener("click",(function(t){t.target.classList.contains("btn-edit")?e.handleEdit(t.target):t.target.classList.contains("btn-delete")?e.handleDelete(t.target):t.target.classList.contains("btn-view")&&e.handleView(t.target)}))}},{key:"handleEdit",value:function(e){var t=e.getAttribute("data-id");t&&console.log("编辑项目:",t)}},{key:"handleDelete",value:function(e){var t=e.getAttribute("data-id");t&&confirm("确定要删除这个项目吗？")&&console.log("删除项目:",t)}},{key:"handleView",value:function(e){var t=e.getAttribute("data-id");t&&console.log("查看项目:",t)}},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"defineGlobalFunctions",value:function(){}},{key:"destroy",value:function(){this.isInitialized=!1,console.log("campuses.html页面模块已销毁")}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();window.CampusesPage=new r,t.A=r},1505:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,i(o.key),o)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var r=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1},(t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("backup页面已初始化，跳过重复初始化"):(console.log("🔧 初始化backup页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化backup页面功能"),this.initOriginalScripts(),this.defineGlobalFunctions(),console.log("✅ backup页面功能初始化完成")}},{key:"initOriginalScripts",value:function(){}},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"defineGlobalFunctions",value:function(){}},{key:"destroy",value:function(){this.isInitialized=!1,console.log("backup页面模块已销毁")}}])&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();window.BackupPage=new r,t.A=r},1559:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,i(o.key),o)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var r=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.dataLoaded=!1},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("用户管理页面已初始化，跳过重复初始化"):(console.log("👥 初始化用户管理页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化用户管理页面功能"),this.initDataLoading(),this.initVisibilityChange(),this.initUserActions(),this.defineGlobalFunctions(),console.log("✅ 用户管理页面功能初始化完成")}},{key:"initDataLoading",value:function(){this.dataLoaded=!0,console.log("用户管理数据已标记为加载完成")}},{key:"initVisibilityChange",value:function(){var e=this;document.addEventListener("visibilitychange",(function(){"visible"===document.visibilityState&&(console.log("页面变为可见，检查数据加载状态"),e.checkAndReloadData())})),window.addEventListener("focus",(function(){console.log("窗口重新获得焦点，检查数据加载状态"),e.checkAndReloadData()})),window.addEventListener("pageshow",(function(t){t.persisted&&(console.log("从缓存恢复页面，检查数据加载状态"),e.checkAndReloadData())}))}},{key:"initUserActions",value:function(){this.initSortingFeature(),this.initSearchFeature(),this.initTableStyles(),console.log("用户操作功能已初始化")}},{key:"initSortingFeature",value:function(){var e=this;this.currentSort={column:"",direction:"asc"};var t=document.querySelectorAll(".sort-icon");t.forEach((function(n){n.addEventListener("click",(function(n){var o=n.target.dataset.sort;e.currentSort.column===o?e.currentSort.direction="asc"===e.currentSort.direction?"desc":"asc":(e.currentSort.column=o,e.currentSort.direction="asc"),t.forEach((function(e){return e.textContent="⇅"})),n.target.textContent="asc"===e.currentSort.direction?"↑":"↓",e.sortTable(o,e.currentSort.direction)}))}))}},{key:"sortTable",value:function(e,t){var n=document.querySelector("#usersTable tbody");if(n){var o=Array.from(n.querySelectorAll("tr"));o.sort((function(n,o){var i,r;return"id"===e?(i=parseInt(n.children[0].textContent.trim()),r=parseInt(o.children[0].textContent.trim())):"username"===e?(i=n.children[1].textContent.trim().toLowerCase(),r=o.children[1].textContent.trim().toLowerCase()):"name"===e?(i=n.children[2].textContent.trim().toLowerCase(),r=o.children[2].textContent.trim().toLowerCase()):"email"===e&&(i=n.children[3].textContent.trim().toLowerCase(),r=o.children[3].textContent.trim().toLowerCase()),i<r?"asc"===t?-1:1:i>r?"asc"===t?1:-1:0})),o.forEach((function(e){return n.appendChild(e)}))}}},{key:"initSearchFeature",value:function(){var e=this,t=document.getElementById("searchBtn");t&&t.addEventListener("click",(function(){return e.performSearch()}));var n=document.getElementById("resetBtn");n&&n.addEventListener("click",(function(){return e.resetSearch()}))}},{key:"performSearch",value:function(){var e,t,n,o,i=(null===(e=document.getElementById("searchUsername"))||void 0===e?void 0:e.value.toLowerCase())||"",r=(null===(t=document.getElementById("searchName"))||void 0===t?void 0:t.value.toLowerCase())||"",a=(null===(n=document.getElementById("searchEmail"))||void 0===n?void 0:n.value.toLowerCase())||"",c=(null===(o=document.getElementById("searchRole"))||void 0===o?void 0:o.value)||"";document.querySelectorAll("#usersTable tbody tr").forEach((function(e){var t,n=e.children[1].textContent.toLowerCase(),o=e.children[2].textContent.toLowerCase(),l=e.children[3].textContent.toLowerCase(),s=e.children[4],u=n.includes(i),d=o.includes(r),f=l.includes(a),v=!0;c&&(v=((null===(t=s.querySelector(".role-data"))||void 0===t?void 0:t.textContent.trim())||"").includes(c)),e.style.display=u&&d&&f&&v?"":"none"}))}},{key:"resetSearch",value:function(){["searchUsername","searchName","searchEmail","searchRole"].forEach((function(e){var t=document.getElementById(e);t&&(t.value="")})),document.querySelectorAll("#usersTable tbody tr").forEach((function(e){return e.style.display=""})),document.querySelectorAll(".sort-icon").forEach((function(e){return e.textContent="⇅"})),this.currentSort={column:"",direction:"asc"}}},{key:"initTableStyles",value:function(){document.querySelectorAll(".sort-icon").forEach((function(e){e.style.cursor="pointer",e.style.userSelect="none"}))}},{key:"checkAndReloadData",value:function(){console.log("检查是否需要重新加载数据"),this.dataLoaded?console.log("数据已加载，无需重新加载"):(console.log("数据未加载，开始加载"),this.dataLoaded=!0)}},{key:"defineGlobalFunctions",value:function(){var e=this;window.loadUsersData=function(){console.log("全局函数loadUsersData被调用"),e.initDataLoading()},window.checkAndReloadData=function(){e.checkAndReloadData()},window.dataLoaded=this.dataLoaded}},{key:"destroy",value:function(){this.isInitialized=!1,this.dataLoaded=!1,console.log("用户管理页面模块已销毁")}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();window.UsersPage=new r,t.A=r},2183:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function c(n,o,r,a){var c=o&&o.prototype instanceof s?o:s,u=Object.create(c.prototype);return i(u,"_invoke",function(n,o,i){var r,a,c,s=0,u=i||[],d=!1,f={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return r=t,a=0,c=e,f.n=n,l}};function v(n,o){for(a=n,c=o,t=0;!d&&s&&!i&&t<u.length;t++){var i,r=u[t],v=f.p,p=r[2];n>3?(i=p===o)&&(a=r[4]||3,c=r[5]===e?r[3]:r[5],r[4]=3,r[5]=e):r[0]<=v&&((i=n<2&&v<r[1])?(a=0,f.v=o,f.n=r[1]):v<p&&(i=n<3||r[0]>o||o>p)&&(r[4]=n,r[5]=o,f.n=p,a=0))}if(i||n>1)return l;throw d=!0,o}return function(i,u,p){if(s>1)throw TypeError("Generator is already running");for(d&&1===u&&v(u,p),a=u,c=p;(t=a<2?e:c)||!d;){r||(a?a<3?(a>1&&(f.n=-1),v(a,c)):f.n=c:f.v=c);try{if(s=2,r){if(a||(i="next"),t=r[i]){if(!(t=t.call(r,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,a<2&&(a=0)}else 1===a&&(t=r.return)&&t.call(r),a<2&&(c=TypeError("The iterator does not provide a '"+i+"' method"),a=1);r=e}else if((t=(d=f.n<0)?c:n.call(o,f))!==l)break}catch(t){r=e,a=1,c=t}finally{s=1}}return{value:t,done:d}}}(n,r,a),!0),u}var l={};function s(){}function u(){}function d(){}t=Object.getPrototypeOf;var f=[][r]?t(t([][r]())):(i(t={},r,(function(){return this})),t),v=d.prototype=s.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,i(e,a,"GeneratorFunction")),e.prototype=Object.create(v),e}return u.prototype=d,i(v,"constructor",d),i(d,"constructor",u),u.displayName="GeneratorFunction",i(d,a,"GeneratorFunction"),i(v),i(v,a,"Generator"),i(v,r,(function(){return this})),i(v,"toString",(function(){return"[object Generator]"})),(o=function(){return{w:c,m:p}})()}function i(e,t,n,o){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}i=function(e,t,n,o){if(t)r?r(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{function a(t,n){i(e,t,(function(e){return this._invoke(t,n,e)}))}a("next",0),a("throw",1),a("return",2)}},i(e,t,n,o)}function r(e,t,n,o,i,r,a){try{var c=e[r](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(o,i)}function a(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,c(o.key),o)}}function c(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var l=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.dataLoaded=!1},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("检查活动管理页面已初始化，跳过重复初始化"):(console.log("🔍 初始化检查活动管理页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化检查活动管理页面功能"),this.initActivityActions(),this.initReportActions(),this.initRecycleBin(),this.initDataLoading(),this.initActionButtons(),this.defineGlobalFunctions(),console.log("✅ 检查活动管理页面功能初始化完成")}},{key:"initActivityActions",value:function(){window.stopInspectionActivity=function(e){confirm("确定要停止这个检查活动吗？")&&fetch("/admin/inspection_activities/".concat(e,"/stop"),{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":document.querySelector("meta[name=csrf-token]").getAttribute("content")}}).then((function(e){return e.json()})).then((function(e){e.success?(alert("检查活动已停止"),location.reload()):alert("停止失败："+e.message)})).catch((function(e){console.error("Error:",e),alert("停止失败")}))},window.reactivateInspectionActivity=function(e){confirm("确定要重新激活这个检查活动吗？")&&fetch("/admin/inspection_activities/".concat(e,"/reactivate"),{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":document.querySelector("meta[name=csrf-token]").getAttribute("content")}}).then((function(e){return e.json()})).then((function(e){e.success?(alert("检查活动已重新激活"),location.reload()):alert("重新激活失败："+e.message)})).catch((function(e){console.error("Error:",e),alert("重新激活失败")}))},console.log("✅ 停止检查活动函数已定义"),console.log("✅ 重新激活检查活动函数已定义")}},{key:"initReportActions",value:function(){window.publishReport=function(e){if(confirm("确定要发布这个通报吗？发布后将无法修改。")){var t,n=document.createElement("form");n.method="POST",n.action="/admin/publish_report/".concat(e);var o=null===(t=document.querySelector('meta[name="csrf-token"]'))||void 0===t?void 0:t.getAttribute("content");if(o){var i=document.createElement("input");i.type="hidden",i.name="csrf_token",i.value=o,n.appendChild(i)}document.body.appendChild(n),n.submit()}},window.unpublishReport=function(e){if(confirm("确定要取消发布这个通报吗？")){var t,n=document.createElement("form");n.method="POST",n.action="/admin/unpublish_report/".concat(e);var o=null===(t=document.querySelector('meta[name="csrf-token"]'))||void 0===t?void 0:t.getAttribute("content");if(o){var i=document.createElement("input");i.type="hidden",i.name="csrf_token",i.value=o,n.appendChild(i)}document.body.appendChild(n),n.submit()}},window.deleteReportDraft=function(e){if(confirm("确定要删除这个通报草稿吗？此操作不可恢复。")){var t,n=document.createElement("form");n.method="POST",n.action="/admin/delete_report_draft/".concat(e);var o=null===(t=document.querySelector('meta[name="csrf-token"]'))||void 0===t?void 0:t.getAttribute("content");if(o){var i=document.createElement("input");i.type="hidden",i.name="csrf_token",i.value=o,n.appendChild(i)}document.body.appendChild(n),n.submit()}}}},{key:"initRecycleBin",value:function(){this.loadRecycleCount()}},{key:"loadRecycleCount",value:(n=o().m((function e(){var t,n,i,r,a;return o().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,fetch("/admin/api/recycle_bin/count");case 1:return t=e.v,e.n=2,t.json();case 2:n=e.v,(i=document.getElementById("recycleCount"))&&n.count>0?(i.textContent=n.count,i.style.display="inline"):i&&(i.style.display="none"),e.n=4;break;case 3:e.p=3,a=e.v,console.error("加载回收站计数失败:",a),(r=document.getElementById("recycleCount"))&&(r.style.display="none");case 4:return e.a(2)}}),e,null,[[0,3]])})),i=function(){var e=this,t=arguments;return new Promise((function(o,i){var a=n.apply(e,t);function c(e){r(a,o,i,c,l,"next",e)}function l(e){r(a,o,i,c,l,"throw",e)}c(void 0)}))},function(){return i.apply(this,arguments)})},{key:"initDataLoading",value:function(){this.dataLoaded=!0,console.log("检查活动数据已标记为加载完成")}},{key:"initActionButtons",value:function(){window.toggleActionButtons=function(e){console.log("🔄 toggleActionButtons被调用"),event&&event.stopPropagation(),document.querySelectorAll(".hover-action-container.expanded").forEach((function(t){t!==e&&t.classList.remove("expanded")})),e.classList.toggle("expanded"),console.log("✅ 操作按钮状态已切换")},console.log("✅ toggleActionButtons函数已定义")}},{key:"confirmDeleteActivity",value:function(e,t,n){if(n)return alert("该活动已发布通报，无法直接删除。\n\n请先取消发布通报，然后再删除活动。"),!1;var o='确定要删除检查活动"'.concat(t,'"吗？\n\n删除后该活动将移入回收站，可以在回收站中恢复。');if(confirm(o)){var i,r=document.createElement("form");r.method="POST",r.action="/admin/inspection_activities/".concat(e,"/delete");var a=null===(i=document.querySelector('meta[name="csrf-token"]'))||void 0===i?void 0:i.getAttribute("content");if(a){var c=document.createElement("input");c.type="hidden",c.name="csrf_token",c.value=a,r.appendChild(c)}return document.body.appendChild(r),r.submit(),!0}return!1}},{key:"defineGlobalFunctions",value:function(){var e=this;console.log("🔧 定义检查活动页面全局函数"),window.confirmDeleteActivity=this.confirmDeleteActivity.bind(this),window.publishReport=window.publishReport||function(){return console.warn("publishReport not defined")},window.unpublishReport=window.unpublishReport||function(){return console.warn("unpublishReport not defined")},window.deleteReportDraft=window.deleteReportDraft||function(){return console.warn("deleteReportDraft not defined")},window.loadInspectionActivitiesData=function(){console.log("全局函数loadInspectionActivitiesData被调用"),e.initDataLoading()},window.toggleActionButtons||(console.warn("⚠️ toggleActionButtons未定义，重新初始化"),this.initActionButtons()),console.log("✅ 检查活动页面全局函数定义完成")}},{key:"destroy",value:function(){this.isInitialized=!1,this.dataLoaded=!1,console.log("检查活动管理页面模块已销毁")}}],t&&a(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,i}();window.InspectionActivitiesPage=new l,t.A=l},2304:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,i(o.key),o)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var r=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("用户角色页面已初始化，跳过重复初始化"):(console.log("👥 初始化用户角色页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化用户角色页面功能"),this.initBasicFeatures(),this.initFormHandling(),this.initTableActions(),this.defineGlobalFunctions(),console.log("✅ 用户角色页面功能初始化完成")}},{key:"initBasicFeatures",value:function(){window.$&&$.fn.tooltip&&$('[data-toggle="tooltip"]').tooltip(),window.$&&$.fn.modal&&$(".modal").modal({backdrop:"static",keyboard:!1})}},{key:"initFormHandling",value:function(){var e=this;document.querySelectorAll("form").forEach((function(t){t.addEventListener("submit",(function(n){if(!e.validateForm(t))return n.preventDefault(),!1;e.showSubmitLoading(t)}))}))}},{key:"validateForm",value:function(e){var t=this,n=e.querySelectorAll("[required]"),o=!0;return n.forEach((function(e){e.value.trim()||(t.showFieldError(e,"此字段为必填项"),o=!1)})),o}},{key:"showFieldError",value:function(e,t){var n=e.parentNode.querySelector(".field-error");n&&n.remove(),e.classList.add("is-invalid");var o=document.createElement("div");o.className="field-error text-danger small",o.textContent=t,e.parentNode.appendChild(o)}},{key:"showSubmitLoading",value:function(e){var t=e.querySelector('[type="submit"]');t&&(t.disabled=!0,t.innerHTML='<i class="fas fa-spinner fa-spin"></i> 提交中...')}},{key:"initTableActions",value:function(){var e=this;document.addEventListener("click",(function(t){t.target.classList.contains("btn-edit")?e.handleEdit(t.target):t.target.classList.contains("btn-delete")&&e.handleDelete(t.target)}))}},{key:"handleEdit",value:function(e){var t=e.getAttribute("data-id");t&&console.log("编辑角色:",t)}},{key:"handleDelete",value:function(e){var t=e.getAttribute("data-id");t&&confirm("确定要删除这个角色吗？")&&console.log("删除角色:",t)}},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"defineGlobalFunctions",value:function(){}},{key:"destroy",value:function(){this.isInitialized=!1,console.log("用户角色页面模块已销毁")}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();window.UserRolesPage=new r,t.A=r},2650:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,i(o.key),o)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var r=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("检查条款页面已初始化，跳过重复初始化"):(console.log("📋 初始化检查条款页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化检查条款页面功能"),this.initBasicFeatures(),this.initFormHandling(),this.initTableActions(),this.initSorting(),this.defineGlobalFunctions(),console.log("✅ 检查条款页面功能初始化完成")}},{key:"initBasicFeatures",value:function(){window.$&&$.fn.tooltip&&$('[data-toggle="tooltip"]').tooltip(),window.$&&$.fn.modal&&$(".modal").modal({backdrop:"static",keyboard:!1})}},{key:"initFormHandling",value:function(){var e=this;document.querySelectorAll("form").forEach((function(t){t.addEventListener("submit",(function(n){if(!e.validateForm(t))return n.preventDefault(),!1;e.showSubmitLoading(t)}))}))}},{key:"initSorting",value:function(){var e=this;if("undefined"!=typeof Sortable){var t=document.getElementById("clauses-list");t&&Sortable.create(t,{animation:150,onEnd:function(t){e.updateClauseOrder()}})}}},{key:"updateClauseOrder",value:function(){var e=document.querySelectorAll("#clauses-list .clause-item"),t=[];e.forEach((function(e,n){var o=e.getAttribute("data-id");o&&t.push({id:o,order:n+1})})),console.log("更新条款顺序:",t)}},{key:"validateForm",value:function(e){var t=this,n=e.querySelectorAll("[required]"),o=!0;return n.forEach((function(e){e.value.trim()||(t.showFieldError(e,"此字段为必填项"),o=!1)})),o}},{key:"showFieldError",value:function(e,t){var n=e.parentNode.querySelector(".field-error");n&&n.remove(),e.classList.add("is-invalid");var o=document.createElement("div");o.className="field-error text-danger small",o.textContent=t,e.parentNode.appendChild(o)}},{key:"showSubmitLoading",value:function(e){var t=e.querySelector('[type="submit"]');t&&(t.disabled=!0,t.innerHTML='<i class="fas fa-spinner fa-spin"></i> 提交中...')}},{key:"initTableActions",value:function(){var e=this;document.addEventListener("click",(function(t){t.target.classList.contains("btn-edit")?e.handleEdit(t.target):t.target.classList.contains("btn-delete")&&e.handleDelete(t.target)}))}},{key:"handleEdit",value:function(e){var t=e.getAttribute("data-id");t&&console.log("编辑检查条款:",t)}},{key:"handleDelete",value:function(e){var t=e.getAttribute("data-id");t&&confirm("确定要删除这个检查条款吗？")&&console.log("删除检查条款:",t)}},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"defineGlobalFunctions",value:function(){}},{key:"destroy",value:function(){this.isInitialized=!1,console.log("检查条款页面模块已销毁")}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();window.InspectionClausesPage=new r,t.A=r},2952:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,i(o.key),o)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var r=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("模板页面已初始化，跳过重复初始化"):(console.log("📄 初始化模板页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化模板页面功能"),this.initBasicFeatures(),this.initFormHandling(),this.initTableActions(),this.defineGlobalFunctions(),console.log("✅ 模板页面功能初始化完成")}},{key:"initBasicFeatures",value:function(){window.$&&$.fn.tooltip&&$('[data-toggle="tooltip"]').tooltip(),window.$&&$.fn.modal&&$(".modal").modal({backdrop:"static",keyboard:!1})}},{key:"initFormHandling",value:function(){var e=this;document.querySelectorAll("form").forEach((function(t){t.addEventListener("submit",(function(n){if(!e.validateForm(t))return n.preventDefault(),!1;e.showSubmitLoading(t)}))}))}},{key:"validateForm",value:function(e){var t=this,n=e.querySelectorAll("[required]"),o=!0;return n.forEach((function(e){e.value.trim()||(t.showFieldError(e,"此字段为必填项"),o=!1)})),o}},{key:"showFieldError",value:function(e,t){var n=e.parentNode.querySelector(".field-error");n&&n.remove(),e.classList.add("is-invalid");var o=document.createElement("div");o.className="field-error text-danger small",o.textContent=t,e.parentNode.appendChild(o)}},{key:"showSubmitLoading",value:function(e){var t=e.querySelector('[type="submit"]');t&&(t.disabled=!0,t.innerHTML='<i class="fas fa-spinner fa-spin"></i> 提交中...')}},{key:"initTableActions",value:function(){var e=this;document.addEventListener("click",(function(t){t.target.classList.contains("btn-edit")?e.handleEdit(t.target):t.target.classList.contains("btn-delete")&&e.handleDelete(t.target)}))}},{key:"handleEdit",value:function(e){var t=e.getAttribute("data-id");t&&console.log("编辑模板:",t)}},{key:"handleDelete",value:function(e){var t=e.getAttribute("data-id");t&&confirm("确定要删除这个模板吗？")&&console.log("删除模板:",t)}},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"defineGlobalFunctions",value:function(){}},{key:"destroy",value:function(){this.isInitialized=!1,console.log("模板页面模块已销毁")}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();window.TemplatesPage=new r,t.A=r},5066:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,i(o.key),o)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var r=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("安全台账页面已初始化，跳过重复初始化"):(console.log("📊 初始化安全台账页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化安全台账页面功能"),this.initBasicFeatures(),this.initFormHandling(),this.initTableActions(),this.defineGlobalFunctions(),console.log("✅ 安全台账页面功能初始化完成")}},{key:"initBasicFeatures",value:function(){window.$&&$.fn.tooltip&&$('[data-toggle="tooltip"]').tooltip(),window.$&&$.fn.modal&&$(".modal").modal({backdrop:"static",keyboard:!1})}},{key:"initFormHandling",value:function(){var e=this;document.querySelectorAll("form").forEach((function(t){t.addEventListener("submit",(function(n){if(!e.validateForm(t))return n.preventDefault(),!1;e.showSubmitLoading(t)}))}))}},{key:"validateForm",value:function(e){var t=this,n=e.querySelectorAll("[required]"),o=!0;return n.forEach((function(e){e.value.trim()||(t.showFieldError(e,"此字段为必填项"),o=!1)})),o}},{key:"showFieldError",value:function(e,t){var n=e.parentNode.querySelector(".field-error");n&&n.remove(),e.classList.add("is-invalid");var o=document.createElement("div");o.className="field-error text-danger small",o.textContent=t,e.parentNode.appendChild(o)}},{key:"showSubmitLoading",value:function(e){var t=e.querySelector('[type="submit"]');t&&(t.disabled=!0,t.innerHTML='<i class="fas fa-spinner fa-spin"></i> 提交中...')}},{key:"initTableActions",value:function(){var e=this;document.addEventListener("click",(function(t){t.target.classList.contains("btn-edit")?e.handleEdit(t.target):t.target.classList.contains("btn-delete")&&e.handleDelete(t.target)}))}},{key:"handleEdit",value:function(e){var t=e.getAttribute("data-id");t&&console.log("编辑安全台账:",t)}},{key:"handleDelete",value:function(e){var t=e.getAttribute("data-id");t&&confirm("确定要删除这个安全台账吗？")&&console.log("删除安全台账:",t)}},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"defineGlobalFunctions",value:function(){}},{key:"destroy",value:function(){this.isInitialized=!1,console.log("安全台账页面模块已销毁")}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();window.SafetyLedgersPage=new r,t.A=r},5276:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function c(n,o,r,a){var c=o&&o.prototype instanceof s?o:s,u=Object.create(c.prototype);return i(u,"_invoke",function(n,o,i){var r,a,c,s=0,u=i||[],d=!1,f={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return r=t,a=0,c=e,f.n=n,l}};function v(n,o){for(a=n,c=o,t=0;!d&&s&&!i&&t<u.length;t++){var i,r=u[t],v=f.p,p=r[2];n>3?(i=p===o)&&(a=r[4]||3,c=r[5]===e?r[3]:r[5],r[4]=3,r[5]=e):r[0]<=v&&((i=n<2&&v<r[1])?(a=0,f.v=o,f.n=r[1]):v<p&&(i=n<3||r[0]>o||o>p)&&(r[4]=n,r[5]=o,f.n=p,a=0))}if(i||n>1)return l;throw d=!0,o}return function(i,u,p){if(s>1)throw TypeError("Generator is already running");for(d&&1===u&&v(u,p),a=u,c=p;(t=a<2?e:c)||!d;){r||(a?a<3?(a>1&&(f.n=-1),v(a,c)):f.n=c:f.v=c);try{if(s=2,r){if(a||(i="next"),t=r[i]){if(!(t=t.call(r,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,a<2&&(a=0)}else 1===a&&(t=r.return)&&t.call(r),a<2&&(c=TypeError("The iterator does not provide a '"+i+"' method"),a=1);r=e}else if((t=(d=f.n<0)?c:n.call(o,f))!==l)break}catch(t){r=e,a=1,c=t}finally{s=1}}return{value:t,done:d}}}(n,r,a),!0),u}var l={};function s(){}function u(){}function d(){}t=Object.getPrototypeOf;var f=[][r]?t(t([][r]())):(i(t={},r,(function(){return this})),t),v=d.prototype=s.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,i(e,a,"GeneratorFunction")),e.prototype=Object.create(v),e}return u.prototype=d,i(v,"constructor",d),i(d,"constructor",u),u.displayName="GeneratorFunction",i(d,a,"GeneratorFunction"),i(v),i(v,a,"Generator"),i(v,r,(function(){return this})),i(v,"toString",(function(){return"[object Generator]"})),(o=function(){return{w:c,m:p}})()}function i(e,t,n,o){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}i=function(e,t,n,o){if(t)r?r(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{function a(t,n){i(e,t,(function(e){return this._invoke(t,n,e)}))}a("next",0),a("throw",1),a("return",2)}},i(e,t,n,o)}function r(e,t,n,o,i,r,a){try{var c=e[r](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(o,i)}function a(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,c(o.key),o)}}function c(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var l=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.currentTab="pending"},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("隐患整改页面已初始化，跳过重复初始化"):(console.log("🔧 初始化隐患整改页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化隐患整改页面功能"),this.initTabSwitching(),this.initTableSorting(),this.initFilters(),this.initExportFunction(),this.defineGlobalFunctions(),console.log("✅ 隐患整改页面功能初始化完成")}},{key:"initTabSwitching",value:function(){var e=this;console.log("📑 初始化标签页切换"),document.querySelectorAll('[data-toggle="tab"]').forEach((function(t){t.addEventListener("click",(function(t){t.preventDefault();var n=t.target.getAttribute("href").substring(1);e.switchTab(n)}))})),this.detectCurrentTab()}},{key:"detectCurrentTab",value:function(){var e=document.querySelector(".nav-tabs .active");if(e){var t=e.getAttribute("href");t&&(this.currentTab=t.substring(1))}}},{key:"switchTab",value:function(e){console.log("切换到标签页:",e),this.currentTab=e,this.updateTabState(e),this.updateTabContent(e)}},{key:"updateTabState",value:function(e){document.querySelectorAll(".nav-tabs li").forEach((function(e){e.classList.remove("active")})),document.querySelectorAll(".tab-pane").forEach((function(e){e.classList.remove("active","in")}));var t=document.querySelector('[href="#'.concat(e,'"]'));t&&t.parentElement.classList.add("active");var n=document.getElementById(e);n&&n.classList.add("active","in")}},{key:"updateTabContent",value:function(e){console.log("更新标签页内容:",e)}},{key:"initTableSorting",value:function(){var e=this;console.log("📊 初始化表格排序"),document.querySelectorAll("th[data-sort]").forEach((function(t){t.addEventListener("click",(function(t){e.handleSort(t.target)})),t.style.cursor="pointer",t.innerHTML+=' <i class="fas fa-sort sort-icon"></i>'}))}},{key:"handleSort",value:function(e){var t=e.getAttribute("data-sort"),n="asc"===(e.getAttribute("data-order")||"asc")?"desc":"asc";console.log("排序字段:",t,"排序方向:",n),document.querySelectorAll("th[data-sort]").forEach((function(e){e.classList.remove("sort-asc","sort-desc");var t=e.querySelector(".sort-icon");t&&(t.className="fas fa-sort sort-icon")})),e.setAttribute("data-order",n),e.classList.add("sort-".concat(n));var o=e.querySelector(".sort-icon");o&&(o.className="fas fa-sort-".concat("asc"===n?"up":"down"," sort-icon")),this.sortTable(t,n)}},{key:"sortTable",value:function(e,t){var n=this.updateUrlParameter(window.location.href,"sort_field",e),o=this.updateUrlParameter(n,"sort_order",t);window.location.href=o}},{key:"updateUrlParameter",value:function(e,t,n){var o=new RegExp("([?&])"+t+"=.*?(&|$)","i"),i=-1!==e.indexOf("?")?"&":"?";return e.match(o)?e.replace(o,"$1"+t+"="+n+"$2"):e+i+t+"="+n}},{key:"initFilters",value:function(){console.log("🔍 初始化过滤器"),this.bindFilterEvents(),this.initDatePickers()}},{key:"bindFilterEvents",value:function(){var e=this,t=document.getElementById("searchBtn");t&&t.addEventListener("click",(function(){e.applyFilters()}));var n=document.getElementById("resetBtn");n&&n.addEventListener("click",(function(){e.resetFilters()}));var o,i=document.getElementById("searchInput");i&&i.addEventListener("input",(function(t){clearTimeout(o),o=setTimeout((function(){e.performSearch(t.target.value)}),500)}))}},{key:"initDatePickers",value:function(){window.$&&$.fn.datepicker&&$(".datepicker").datepicker({format:"yyyy-mm-dd",language:"zh-CN",autoclose:!0,todayHighlight:!0,clearBtn:!0})}},{key:"applyFilters",value:function(){console.log("应用过滤器");var e=this.collectFilterData(),t=new URLSearchParams(e),n="".concat(window.location.pathname,"?").concat(t.toString());window.location.href=n}},{key:"collectFilterData",value:function(){var e={};return document.querySelectorAll("[data-filter]").forEach((function(t){var n=t.getAttribute("data-filter"),o=t.value;o&&(e[n]=o)})),e}},{key:"resetFilters",value:function(){console.log("重置过滤器"),document.querySelectorAll("[data-filter]").forEach((function(e){e.value=""})),window.location.href=window.location.pathname}},{key:"performSearch",value:function(e){e.length<2||console.log("执行搜索:",e)}},{key:"initExportFunction",value:function(){var e=this;console.log("📤 初始化导出功能");var t=document.getElementById("exportBtn");t&&t.addEventListener("click",(function(){e.showExportOptions()})),this.bindExportButtons()}},{key:"bindExportButtons",value:function(){var e=this,t=document.getElementById("exportExcel");t&&t.addEventListener("click",(function(){e.exportToExcel()}));var n=document.getElementById("exportPdf");n&&n.addEventListener("click",(function(){e.exportToPDF()}));var o=document.getElementById("exportCsv");o&&o.addEventListener("click",(function(){e.exportToCSV()}))}},{key:"showExportOptions",value:function(){var e=this,t=[{label:"导出为Excel",action:function(){return e.exportToExcel()}},{label:"导出为PDF",action:function(){return e.exportToPDF()}},{label:"导出为CSV",action:function(){return e.exportToCSV()}}];console.log("显示导出选项:",t)}},{key:"exportToExcel",value:function(){console.log("导出为Excel");var e=new URLSearchParams(window.location.search);e.set("export","excel"),window.open("".concat(window.location.pathname,"?").concat(e.toString()),"_blank")}},{key:"exportToPDF",value:function(){console.log("导出为PDF");var e=new URLSearchParams(window.location.search);e.set("export","pdf"),window.open("".concat(window.location.pathname,"?").concat(e.toString()),"_blank")}},{key:"exportToCSV",value:function(){console.log("导出为CSV");var e=new URLSearchParams(window.location.search);e.set("export","csv"),window.open("".concat(window.location.pathname,"?").concat(e.toString()),"_blank")}},{key:"initOtherFeatures",value:function(){this.initTooltips(),this.initBatchOperations(),this.initStatusUpdates()}},{key:"initTooltips",value:function(){window.$&&$.fn.tooltip&&$('[data-toggle="tooltip"]').tooltip()}},{key:"initBatchOperations",value:function(){var e=this,t=document.getElementById("selectAll");t&&t.addEventListener("change",(function(t){e.toggleSelectAll(t.target.checked)})),document.querySelectorAll(".batch-operation").forEach((function(t){t.addEventListener("click",(function(t){e.handleBatchOperation(t.target)}))}))}},{key:"toggleSelectAll",value:function(e){document.querySelectorAll(".item-checkbox").forEach((function(t){t.checked=e})),this.updateBatchOperationsVisibility()}},{key:"handleBatchOperation",value:function(e){var t=e.getAttribute("data-operation"),n=this.getSelectedItems();0!==n.length?console.log("批量操作:",t,"选中项目:",n):alert("请选择要操作的项目")}},{key:"getSelectedItems",value:function(){var e=document.querySelectorAll(".item-checkbox:checked");return Array.from(e).map((function(e){return e.value}))}},{key:"updateBatchOperationsVisibility",value:function(){var e=this.getSelectedItems().length,t=document.getElementById("batchOperations");t&&(t.style.display=e>0?"block":"none")}},{key:"initStatusUpdates",value:function(){var e=this;document.querySelectorAll(".status-update-btn").forEach((function(t){t.addEventListener("click",(function(t){e.handleStatusUpdate(t.target)}))}))}},{key:"handleStatusUpdate",value:(n=o().m((function e(t){var n,i,r,a,c;return o().w((function(e){for(;;)switch(e.n){case 0:if(n=t.getAttribute("data-hazard-id"),i=t.getAttribute("data-status"),n&&i){e.n=1;break}return console.error("缺少必要的状态更新参数"),e.a(2);case 1:return e.p=1,e.n=2,fetch("/admin/api/hazards/".concat(n,"/status"),{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":this.getCSRFToken()},body:JSON.stringify({status:i})});case 2:return r=e.v,e.n=3,r.json();case 3:(a=e.v).success?(this.showToast("状态更新成功","success"),setTimeout((function(){window.location.reload()}),1e3)):this.showToast("状态更新失败: "+a.message,"error"),e.n=5;break;case 4:e.p=4,c=e.v,console.error("状态更新异常:",c),this.showToast("状态更新失败，请重试","error");case 5:return e.a(2)}}),e,this,[[1,4]])})),i=function(){var e=this,t=arguments;return new Promise((function(o,i){var a=n.apply(e,t);function c(e){r(a,o,i,c,l,"next",e)}function l(e){r(a,o,i,c,l,"throw",e)}c(void 0)}))},function(e){return i.apply(this,arguments)})},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"showToast",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",n=document.createElement("div");n.className="alert alert-".concat("error"===t?"danger":t," toast-message"),n.style.position="fixed",n.style.top="20px",n.style.right="20px",n.style.zIndex="9999",n.style.minWidth="200px",n.textContent=e,document.body.appendChild(n),setTimeout((function(){n.parentNode&&n.parentNode.removeChild(n)}),3e3)}},{key:"defineGlobalFunctions",value:function(){var e=this;window.loadHazardRectificationData=function(){console.log("全局函数loadHazardRectificationData被调用"),console.log("数据已经在服务器端渲染，无需刷新")},window.exportHazardRectification=function(t){switch(t){case"excel":e.exportToExcel();break;case"pdf":e.exportToPDF();break;case"csv":e.exportToCSV();break;default:e.showExportOptions()}}}},{key:"destroy",value:function(){this.isInitialized=!1,this.currentTab="pending",console.log("隐患整改页面模块已销毁")}}],t&&a(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,i}();window.HazardRectificationPage=new l,t.A=l},5906:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function c(n,o,r,a){var c=o&&o.prototype instanceof s?o:s,u=Object.create(c.prototype);return i(u,"_invoke",function(n,o,i){var r,a,c,s=0,u=i||[],d=!1,f={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return r=t,a=0,c=e,f.n=n,l}};function v(n,o){for(a=n,c=o,t=0;!d&&s&&!i&&t<u.length;t++){var i,r=u[t],v=f.p,p=r[2];n>3?(i=p===o)&&(a=r[4]||3,c=r[5]===e?r[3]:r[5],r[4]=3,r[5]=e):r[0]<=v&&((i=n<2&&v<r[1])?(a=0,f.v=o,f.n=r[1]):v<p&&(i=n<3||r[0]>o||o>p)&&(r[4]=n,r[5]=o,f.n=p,a=0))}if(i||n>1)return l;throw d=!0,o}return function(i,u,p){if(s>1)throw TypeError("Generator is already running");for(d&&1===u&&v(u,p),a=u,c=p;(t=a<2?e:c)||!d;){r||(a?a<3?(a>1&&(f.n=-1),v(a,c)):f.n=c:f.v=c);try{if(s=2,r){if(a||(i="next"),t=r[i]){if(!(t=t.call(r,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,a<2&&(a=0)}else 1===a&&(t=r.return)&&t.call(r),a<2&&(c=TypeError("The iterator does not provide a '"+i+"' method"),a=1);r=e}else if((t=(d=f.n<0)?c:n.call(o,f))!==l)break}catch(t){r=e,a=1,c=t}finally{s=1}}return{value:t,done:d}}}(n,r,a),!0),u}var l={};function s(){}function u(){}function d(){}t=Object.getPrototypeOf;var f=[][r]?t(t([][r]())):(i(t={},r,(function(){return this})),t),v=d.prototype=s.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,i(e,a,"GeneratorFunction")),e.prototype=Object.create(v),e}return u.prototype=d,i(v,"constructor",d),i(d,"constructor",u),u.displayName="GeneratorFunction",i(d,a,"GeneratorFunction"),i(v),i(v,a,"Generator"),i(v,r,(function(){return this})),i(v,"toString",(function(){return"[object Generator]"})),(o=function(){return{w:c,m:p}})()}function i(e,t,n,o){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}i=function(e,t,n,o){if(t)r?r(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{function a(t,n){i(e,t,(function(e){return this._invoke(t,n,e)}))}a("next",0),a("throw",1),a("return",2)}},i(e,t,n,o)}function r(e,t,n,o,i,r,a){try{var c=e[r](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(o,i)}function a(e){return function(){var t=this,n=arguments;return new Promise((function(o,i){var a=e.apply(t,n);function c(e){r(a,o,i,c,l,"next",e)}function l(e){r(a,o,i,c,l,"throw",e)}c(void 0)}))}}function c(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,l(o.key),o)}}function l(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var s=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("报告管理页面已初始化，跳过重复初始化"):(console.log("📋 初始化报告管理页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化报告管理页面功能"),this.initBasicFeatures(),this.initTableActions(),this.initFilters(),this.initGeneration(),this.defineGlobalFunctions(),console.log("✅ 报告管理页面功能初始化完成")}},{key:"initBasicFeatures",value:function(){window.$&&$.fn.tooltip&&$('[data-toggle="tooltip"]').tooltip(),window.$&&$.fn.modal&&$(".modal").modal({backdrop:"static",keyboard:!1})}},{key:"initTableActions",value:function(){var e=this;document.addEventListener("click",(function(t){t.target.classList.contains("btn-view")?e.handleView(t.target):t.target.classList.contains("btn-download")?e.handleDownload(t.target):t.target.classList.contains("btn-delete")?e.handleDelete(t.target):t.target.classList.contains("btn-regenerate")&&e.handleRegenerate(t.target)}))}},{key:"handleView",value:function(e){var t=e.getAttribute("data-id");t&&window.open("/admin/reports/".concat(t,"/view"),"_blank")}},{key:"handleDownload",value:function(e){var t=e.getAttribute("data-id"),n=e.getAttribute("data-format")||"pdf";t&&(window.location.href="/admin/reports/".concat(t,"/download?format=").concat(n))}},{key:"handleDelete",value:(r=a(o().m((function e(t){var n,i,r,a;return o().w((function(e){for(;;)switch(e.n){case 0:if(n=t.getAttribute("data-id")){e.n=1;break}return e.a(2);case 1:if(!confirm("确定要删除这个报告吗？")){e.n=6;break}return e.p=2,e.n=3,fetch("/admin/api/reports/".concat(n),{method:"DELETE",headers:{"X-CSRFToken":this.getCSRFToken()}});case 3:return i=e.v,e.n=4,i.json();case 4:(r=e.v).success?(alert("报告删除成功"),window.location.reload()):alert("删除失败: "+r.message),e.n=6;break;case 5:e.p=5,a=e.v,console.error("删除报告失败:",a),alert("删除失败，请重试");case 6:return e.a(2)}}),e,this,[[2,5]])}))),function(e){return r.apply(this,arguments)})},{key:"handleRegenerate",value:(i=a(o().m((function e(t){var n,i,r,a;return o().w((function(e){for(;;)switch(e.n){case 0:if(n=t.getAttribute("data-id")){e.n=1;break}return e.a(2);case 1:if(!confirm("确定要重新生成这个报告吗？")){e.n=7;break}return e.p=2,t.disabled=!0,t.innerHTML='<i class="fas fa-spinner fa-spin"></i> 生成中...',e.n=3,fetch("/admin/api/reports/".concat(n,"/regenerate"),{method:"POST",headers:{"X-CSRFToken":this.getCSRFToken()}});case 3:return i=e.v,e.n=4,i.json();case 4:(r=e.v).success?(alert("报告重新生成成功"),window.location.reload()):alert("生成失败: "+r.message),e.n=6;break;case 5:e.p=5,a=e.v,console.error("重新生成报告失败:",a),alert("生成失败，请重试");case 6:return e.p=6,t.disabled=!1,t.innerHTML='<i class="fas fa-redo"></i> 重新生成',e.f(6);case 7:return e.a(2)}}),e,this,[[2,5,6,7]])}))),function(e){return i.apply(this,arguments)})},{key:"initFilters",value:function(){var e=this,t=document.getElementById("reportType");t&&t.addEventListener("change",(function(){e.applyFilters()}));var n=document.getElementById("reportStatus");n&&n.addEventListener("change",(function(){e.applyFilters()})),window.$&&$.fn.daterangepicker&&$("#dateRange").daterangepicker({locale:{format:"YYYY-MM-DD"},autoApply:!0},(function(t,n){e.applyFilters()}))}},{key:"applyFilters",value:function(){var e,t,n,o={type:null===(e=document.getElementById("reportType"))||void 0===e?void 0:e.value,status:null===(t=document.getElementById("reportStatus"))||void 0===t?void 0:t.value,dateRange:null===(n=document.getElementById("dateRange"))||void 0===n?void 0:n.value};console.log("应用过滤条件:",o)}},{key:"initGeneration",value:function(){var e=this,t=document.getElementById("generateReport");t&&t.addEventListener("click",(function(){e.showGenerateModal()}));var n=document.getElementById("confirmGenerate");n&&n.addEventListener("click",(function(){e.generateReport()}))}},{key:"showGenerateModal",value:function(){var e=document.getElementById("generateReportModal");e&&window.$&&$(e).modal("show")}},{key:"generateReport",value:(n=a(o().m((function e(){var t,n,i,r,a,c,l,s,u,d,f,v;return o().w((function(e){for(;;)switch(e.n){case 0:if(r=null===(t=document.getElementById("newReportType"))||void 0===t?void 0:t.value,a=null===(n=document.getElementById("newReportDateRange"))||void 0===n?void 0:n.value,c=null===(i=document.getElementById("includeCharts"))||void 0===i?void 0:i.checked,r){e.n=1;break}return alert("请选择报告类型"),e.a(2);case 1:return e.p=1,(l=document.getElementById("confirmGenerate")).disabled=!0,l.innerHTML='<i class="fas fa-spinner fa-spin"></i> 生成中...',e.n=2,fetch("/admin/api/reports/generate",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":this.getCSRFToken()},body:JSON.stringify({type:r,date_range:a,include_charts:c})});case 2:return s=e.v,e.n=3,s.json();case 3:(u=e.v).success?(alert("报告生成成功"),(d=document.getElementById("generateReportModal"))&&window.$&&$(d).modal("hide"),window.location.reload()):alert("生成失败: "+u.message),e.n=5;break;case 4:e.p=4,v=e.v,console.error("生成报告失败:",v),alert("生成失败，请重试");case 5:return e.p=5,(f=document.getElementById("confirmGenerate")).disabled=!1,f.innerHTML='<i class="fas fa-plus"></i> 生成报告',e.f(5);case 6:return e.a(2)}}),e,this,[[1,4,5,6]])}))),function(){return n.apply(this,arguments)})},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"defineGlobalFunctions",value:function(){}},{key:"destroy",value:function(){this.isInitialized=!1,console.log("报告管理页面模块已销毁")}}],t&&c(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,i,r}();window.ReportsManagementPage=new s,t.A=s},6080:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,i(o.key),o)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var r=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("实验室属性页面已初始化，跳过重复初始化"):(console.log("🔬 初始化实验室属性页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化实验室属性页面功能"),this.initBasicFeatures(),this.initFormHandling(),this.initTableActions(),this.defineGlobalFunctions(),console.log("✅ 实验室属性页面功能初始化完成")}},{key:"initBasicFeatures",value:function(){window.$&&$.fn.tooltip&&$('[data-toggle="tooltip"]').tooltip(),window.$&&$.fn.modal&&$(".modal").modal({backdrop:"static",keyboard:!1})}},{key:"initFormHandling",value:function(){var e=this;document.querySelectorAll("form").forEach((function(t){t.addEventListener("submit",(function(n){if(!e.validateForm(t))return n.preventDefault(),!1;e.showSubmitLoading(t)}))}))}},{key:"validateForm",value:function(e){var t=this,n=e.querySelectorAll("[required]"),o=!0;return n.forEach((function(e){e.value.trim()||(t.showFieldError(e,"此字段为必填项"),o=!1)})),o}},{key:"showFieldError",value:function(e,t){var n=e.parentNode.querySelector(".field-error");n&&n.remove(),e.classList.add("is-invalid");var o=document.createElement("div");o.className="field-error text-danger small",o.textContent=t,e.parentNode.appendChild(o)}},{key:"showSubmitLoading",value:function(e){var t=e.querySelector('[type="submit"]');t&&(t.disabled=!0,t.innerHTML='<i class="fas fa-spinner fa-spin"></i> 提交中...')}},{key:"initTableActions",value:function(){var e=this;document.addEventListener("click",(function(t){t.target.classList.contains("btn-edit")?e.handleEdit(t.target):t.target.classList.contains("btn-delete")&&e.handleDelete(t.target)}))}},{key:"handleEdit",value:function(e){var t=e.getAttribute("data-id");t&&console.log("编辑实验室属性:",t)}},{key:"handleDelete",value:function(e){var t=e.getAttribute("data-id");t&&confirm("确定要删除这个实验室属性吗？")&&console.log("删除实验室属性:",t)}},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"defineGlobalFunctions",value:function(){}},{key:"destroy",value:function(){this.isInitialized=!1,console.log("实验室属性页面模块已销毁")}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();window.LabPropertiesPage=new r,t.A=r},6346:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,i(o.key),o)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var r=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("buildings.html页面已初始化，跳过重复初始化"):(console.log("🔧 初始化buildings.html页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化buildings.html页面功能"),this.initBasicFeatures(),this.initFormHandling(),this.initTableActions(),this.defineGlobalFunctions(),console.log("✅ buildings.html页面功能初始化完成")}},{key:"initBasicFeatures",value:function(){this.initTooltips(),this.initModals()}},{key:"initTooltips",value:function(){window.$&&$.fn.tooltip&&$('[data-toggle="tooltip"]').tooltip()}},{key:"initModals",value:function(){window.$&&$.fn.modal&&$(".modal").modal({backdrop:"static",keyboard:!1})}},{key:"initFormHandling",value:function(){var e=this;document.querySelectorAll("form").forEach((function(t){t.addEventListener("submit",(function(t){e.handleFormSubmit(t)}))}))}},{key:"handleFormSubmit",value:function(e){var t=e.target;if(!this.validateForm(t))return e.preventDefault(),!1;this.showSubmitLoading(t)}},{key:"validateForm",value:function(e){var t=this,n=e.querySelectorAll("[required]"),o=!0;return n.forEach((function(e){e.value.trim()||(t.showFieldError(e,"此字段为必填项"),o=!1)})),o}},{key:"showFieldError",value:function(e,t){var n=e.parentNode.querySelector(".field-error");n&&n.remove(),e.classList.add("is-invalid");var o=document.createElement("div");o.className="field-error text-danger small",o.textContent=t,e.parentNode.appendChild(o)}},{key:"showSubmitLoading",value:function(e){var t=e.querySelector('[type="submit"]');t&&(t.disabled=!0,t.innerHTML='<i class="fas fa-spinner fa-spin"></i> 提交中...')}},{key:"initTableActions",value:function(){var e=this;document.addEventListener("click",(function(t){t.target.classList.contains("btn-edit")?e.handleEdit(t.target):t.target.classList.contains("btn-delete")?e.handleDelete(t.target):t.target.classList.contains("btn-view")&&e.handleView(t.target)}))}},{key:"handleEdit",value:function(e){var t=e.getAttribute("data-id");t&&console.log("编辑项目:",t)}},{key:"handleDelete",value:function(e){var t=e.getAttribute("data-id");t&&confirm("确定要删除这个项目吗？")&&console.log("删除项目:",t)}},{key:"handleView",value:function(e){var t=e.getAttribute("data-id");t&&console.log("查看项目:",t)}},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"defineGlobalFunctions",value:function(){}},{key:"destroy",value:function(){this.isInitialized=!1,console.log("buildings.html页面模块已销毁")}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();window.BuildingsPage=new r,t.A=r},6751:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function c(n,o,r,a){var c=o&&o.prototype instanceof s?o:s,u=Object.create(c.prototype);return i(u,"_invoke",function(n,o,i){var r,a,c,s=0,u=i||[],d=!1,f={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return r=t,a=0,c=e,f.n=n,l}};function v(n,o){for(a=n,c=o,t=0;!d&&s&&!i&&t<u.length;t++){var i,r=u[t],v=f.p,p=r[2];n>3?(i=p===o)&&(a=r[4]||3,c=r[5]===e?r[3]:r[5],r[4]=3,r[5]=e):r[0]<=v&&((i=n<2&&v<r[1])?(a=0,f.v=o,f.n=r[1]):v<p&&(i=n<3||r[0]>o||o>p)&&(r[4]=n,r[5]=o,f.n=p,a=0))}if(i||n>1)return l;throw d=!0,o}return function(i,u,p){if(s>1)throw TypeError("Generator is already running");for(d&&1===u&&v(u,p),a=u,c=p;(t=a<2?e:c)||!d;){r||(a?a<3?(a>1&&(f.n=-1),v(a,c)):f.n=c:f.v=c);try{if(s=2,r){if(a||(i="next"),t=r[i]){if(!(t=t.call(r,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,a<2&&(a=0)}else 1===a&&(t=r.return)&&t.call(r),a<2&&(c=TypeError("The iterator does not provide a '"+i+"' method"),a=1);r=e}else if((t=(d=f.n<0)?c:n.call(o,f))!==l)break}catch(t){r=e,a=1,c=t}finally{s=1}}return{value:t,done:d}}}(n,r,a),!0),u}var l={};function s(){}function u(){}function d(){}t=Object.getPrototypeOf;var f=[][r]?t(t([][r]())):(i(t={},r,(function(){return this})),t),v=d.prototype=s.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,i(e,a,"GeneratorFunction")),e.prototype=Object.create(v),e}return u.prototype=d,i(v,"constructor",d),i(d,"constructor",u),u.displayName="GeneratorFunction",i(d,a,"GeneratorFunction"),i(v),i(v,a,"Generator"),i(v,r,(function(){return this})),i(v,"toString",(function(){return"[object Generator]"})),(o=function(){return{w:c,m:p}})()}function i(e,t,n,o){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}i=function(e,t,n,o){if(t)r?r(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{function a(t,n){i(e,t,(function(e){return this._invoke(t,n,e)}))}a("next",0),a("throw",1),a("return",2)}},i(e,t,n,o)}function r(e,t,n,o,i,r,a){try{var c=e[r](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(o,i)}function a(e){return function(){var t=this,n=arguments;return new Promise((function(o,i){var a=e.apply(t,n);function c(e){r(a,o,i,c,l,"next",e)}function l(e){r(a,o,i,c,l,"throw",e)}c(void 0)}))}}function c(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,l(o.key),o)}}function l(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var s=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("检查模板页面已初始化，跳过重复初始化"):(console.log("📋 初始化检查模板页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化检查模板页面功能"),this.initTableSorting(),this.initFilters(),this.initTableActions(),this.defineGlobalFunctions(),console.log("✅ 检查模板页面功能初始化完成")}},{key:"initTableSorting",value:function(){var e=this;console.log("📊 初始化表格排序"),document.querySelectorAll("th[data-sort]").forEach((function(t){t.addEventListener("click",(function(t){e.handleSort(t.target)})),t.style.cursor="pointer",t.querySelector(".sort-icon")||(t.innerHTML+=' <i class="fas fa-sort sort-icon"></i>')}))}},{key:"handleSort",value:function(e){var t=e.getAttribute("data-sort"),n="asc"===(e.getAttribute("data-order")||"asc")?"desc":"asc";console.log("排序字段:",t,"排序方向:",n),document.querySelectorAll("th[data-sort]").forEach((function(e){e.classList.remove("sort-asc","sort-desc");var t=e.querySelector(".sort-icon");t&&(t.className="fas fa-sort sort-icon")})),e.setAttribute("data-order",n),e.classList.add("sort-".concat(n));var o=e.querySelector(".sort-icon");o&&(o.className="fas fa-sort-".concat("asc"===n?"up":"down"," sort-icon")),this.sortTable(t,n)}},{key:"sortTable",value:function(e,t){var n=this.updateUrlParameter(window.location.href,"sort_field",e),o=this.updateUrlParameter(n,"sort_order",t);window.location.href=o}},{key:"updateUrlParameter",value:function(e,t,n){var o=new RegExp("([?&])"+t+"=.*?(&|$)","i"),i=-1!==e.indexOf("?")?"&":"?";return e.match(o)?e.replace(o,"$1"+t+"="+n+"$2"):e+i+t+"="+n}},{key:"initFilters",value:function(){console.log("🔍 初始化过滤器"),this.bindFilterEvents()}},{key:"bindFilterEvents",value:function(){var e=this,t=document.getElementById("searchBtn");t&&t.addEventListener("click",(function(){e.applyFilters()}));var n=document.getElementById("resetBtn");n&&n.addEventListener("click",(function(){e.resetFilters()}));var o,i=document.getElementById("searchInput");i&&i.addEventListener("input",(function(t){clearTimeout(o),o=setTimeout((function(){e.performSearch(t.target.value)}),500)}))}},{key:"applyFilters",value:function(){console.log("应用过滤器");var e=this.collectFilterData(),t=new URLSearchParams(e),n="".concat(window.location.pathname,"?").concat(t.toString());window.location.href=n}},{key:"collectFilterData",value:function(){var e={};return document.querySelectorAll("[data-filter]").forEach((function(t){var n=t.getAttribute("data-filter"),o=t.value;o&&(e[n]=o)})),e}},{key:"resetFilters",value:function(){console.log("重置过滤器"),document.querySelectorAll("[data-filter]").forEach((function(e){e.value=""})),window.location.href=window.location.pathname}},{key:"performSearch",value:function(e){e.length<2||console.log("执行搜索:",e)}},{key:"initTableActions",value:function(){console.log("📊 初始化表格操作"),this.bindTableActionEvents()}},{key:"bindTableActionEvents",value:function(){var e=this;document.addEventListener("click",(function(t){if(t.target.classList.contains("edit-template-btn")){var n=t.target.getAttribute("data-id");e.editTemplate(n)}else if(t.target.classList.contains("delete-template-btn")){var o=t.target.getAttribute("data-id");e.deleteTemplate(o)}else if(t.target.classList.contains("copy-template-btn")){var i=t.target.getAttribute("data-id");e.copyTemplate(i)}else if(t.target.classList.contains("view-template-btn")){var r=t.target.getAttribute("data-id");e.viewTemplate(r)}}))}},{key:"editTemplate",value:function(e){console.log("编辑模板:",e),window.location.href="/admin/inspection_templates/".concat(e,"/edit")}},{key:"deleteTemplate",value:(i=a(o().m((function e(t){var n,i,r;return o().w((function(e){for(;;)switch(e.n){case 0:if(confirm("确定要删除这个检查模板吗？")){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,fetch("/admin/api/inspection_templates/".concat(t),{method:"DELETE",headers:{"X-CSRFToken":this.getCSRFToken()}});case 2:return n=e.v,e.n=3,n.json();case 3:(i=e.v).success?(alert("模板删除成功"),window.location.reload()):alert("删除失败: "+i.message),e.n=5;break;case 4:e.p=4,r=e.v,console.error("删除模板失败:",r),alert("删除失败，请重试");case 5:return e.a(2)}}),e,this,[[1,4]])}))),function(e){return i.apply(this,arguments)})},{key:"copyTemplate",value:(n=a(o().m((function e(t){var n,i,r;return o().w((function(e){for(;;)switch(e.n){case 0:if(confirm("确定要复制这个检查模板吗？")){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,fetch("/admin/api/inspection_templates/".concat(t,"/copy"),{method:"POST",headers:{"X-CSRFToken":this.getCSRFToken()}});case 2:return n=e.v,e.n=3,n.json();case 3:(i=e.v).success?(alert("模板复制成功"),window.location.reload()):alert("复制失败: "+i.message),e.n=5;break;case 4:e.p=4,r=e.v,console.error("复制模板失败:",r),alert("复制失败，请重试");case 5:return e.a(2)}}),e,this,[[1,4]])}))),function(e){return n.apply(this,arguments)})},{key:"viewTemplate",value:function(e){console.log("查看模板:",e),window.open("/admin/inspection_templates/".concat(e,"/view"),"_blank")}},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"defineGlobalFunctions",value:function(){var e=this;window.editTemplate=function(t){return e.editTemplate(t)},window.deleteTemplate=function(t){return e.deleteTemplate(t)},window.copyTemplate=function(t){return e.copyTemplate(t)},window.viewTemplate=function(t){return e.viewTemplate(t)}}},{key:"destroy",value:function(){this.isInitialized=!1,console.log("检查模板页面模块已销毁")}}],t&&c(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,i}();window.InspectionTemplatesPage=new s,t.A=s},7367:function(e,t,n){var o=n(312),i=n(9336),r=n(8554);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function c(){var e,t,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.toStringTag||"@@toStringTag";function r(n,o,i,r){var c=o&&o.prototype instanceof s?o:s,u=Object.create(c.prototype);return l(u,"_invoke",function(n,o,i){var r,c,l,s=0,u=i||[],d=!1,f={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return r=t,c=0,l=e,f.n=n,a}};function v(n,o){for(c=n,l=o,t=0;!d&&s&&!i&&t<u.length;t++){var i,r=u[t],v=f.p,p=r[2];n>3?(i=p===o)&&(c=r[4]||3,l=r[5]===e?r[3]:r[5],r[4]=3,r[5]=e):r[0]<=v&&((i=n<2&&v<r[1])?(c=0,f.v=o,f.n=r[1]):v<p&&(i=n<3||r[0]>o||o>p)&&(r[4]=n,r[5]=o,f.n=p,c=0))}if(i||n>1)return a;throw d=!0,o}return function(i,u,p){if(s>1)throw TypeError("Generator is already running");for(d&&1===u&&v(u,p),c=u,l=p;(t=c<2?e:l)||!d;){r||(c?c<3?(c>1&&(f.n=-1),v(c,l)):f.n=l:f.v=l);try{if(s=2,r){if(c||(i="next"),t=r[i]){if(!(t=t.call(r,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,c<2&&(c=0)}else 1===c&&(t=r.return)&&t.call(r),c<2&&(l=TypeError("The iterator does not provide a '"+i+"' method"),c=1);r=e}else if((t=(d=f.n<0)?l:n.call(o,f))!==a)break}catch(t){r=e,c=1,l=t}finally{s=1}}return{value:t,done:d}}}(n,i,r),!0),u}var a={};function s(){}function u(){}function d(){}t=Object.getPrototypeOf;var f=[][o]?t(t([][o]())):(l(t={},o,(function(){return this})),t),v=d.prototype=s.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,l(e,i,"GeneratorFunction")),e.prototype=Object.create(v),e}return u.prototype=d,l(v,"constructor",d),l(d,"constructor",u),u.displayName="GeneratorFunction",l(d,i,"GeneratorFunction"),l(v),l(v,i,"Generator"),l(v,o,(function(){return this})),l(v,"toString",(function(){return"[object Generator]"})),(c=function(){return{w:r,m:p}})()}function l(e,t,n,o){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}l=function(e,t,n,o){if(t)i?i(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{function r(t,n){l(e,t,(function(e){return this._invoke(t,n,e)}))}r("next",0),r("throw",1),r("return",2)}},l(e,t,n,o)}function s(e,t,n,o,i,r,a){try{var c=e[r](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(o,i)}function u(e){return function(){var t=this,n=arguments;return new Promise((function(o,i){var r=e.apply(t,n);function a(e){s(r,o,i,a,c,"next",e)}function c(e){s(r,o,i,a,c,"throw",e)}a(void 0)}))}}function d(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,f(o.key),o)}}function f(e){var t=function(e){if("object"!=a(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==a(t)?t:t+""}var v=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.updateInterval=null,this.updateFrequency=3e4,this.isInitialized=!1,this.init=this.init.bind(this),this.updateOnlineUsers=this.updateOnlineUsers.bind(this),this.showUsersList=this.showUsersList.bind(this)},t=[{key:"init",value:function(){var e=this;this.isInitialized||(0,r.Qc)((function(){e.bindEvents(),e.startAutoUpdate(),e.isInitialized=!0,console.log("在线用户模块初始化完成")}))}},{key:"bindEvents",value:function(){var e=this;document.querySelectorAll('.online-users-count, [data-action="show-online-users"]').forEach((function(t){t.addEventListener("click",e.showUsersList),t.style.cursor="pointer",t.title="点击查看在线用户列表"}))}},{key:"startAutoUpdate",value:function(){this.updateOnlineUsers(),this.updateInterval=setInterval(this.updateOnlineUsers,this.updateFrequency)}},{key:"stopAutoUpdate",value:function(){this.updateInterval&&(clearInterval(this.updateInterval),this.updateInterval=null)}},{key:"updateOnlineUsers",value:(a=u(c().m((function e(){var t,n,i;return c().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,o.A.get("/api/online-users/count");case 1:t=e.v,n=t.count||0,document.querySelectorAll(".online-users-count").forEach((function(e){e.textContent=n})),document.querySelectorAll('[data-stat="online-users"]').forEach((function(e){e.textContent=n})),console.log("在线用户数已更新:",n),e.n=3;break;case 2:e.p=2,i=e.v,console.error("更新在线用户数失败:",i);case 3:return e.a(2)}}),e,null,[[0,2]])}))),function(){return a.apply(this,arguments)})},{key:"showUsersList",value:(n=u(c().m((function e(){var t,n,r,a,l,s=this;return c().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,o.A.get("/api/online-users/list");case 1:t=e.v,n=t.users||[],"",r=0===n.length?'<p class="text-center text-muted">当前没有在线用户</p>':'\n          <div class="table-responsive">\n            <table class="table table-striped">\n              <thead>\n                <tr>\n                  <th>用户名</th>\n                  <th>姓名</th>\n                  <th>角色</th>\n                  <th>登录时间</th>\n                  <th>最后活动</th>\n                </tr>\n              </thead>\n              <tbody>\n                '.concat(n.map((function(e){return"\n                  <tr>\n                    <td>".concat(e.username,"</td>\n                    <td>").concat(e.name||"-","</td>\n                    <td>").concat(e.role||"-","</td>\n                    <td>").concat(s.formatDateTime(e.login_time),"</td>\n                    <td>").concat(s.formatDateTime(e.last_activity),"</td>\n                  </tr>\n                ")})).join(""),"\n              </tbody>\n            </table>\n          </div>\n        "),a=i.A.create({title:"在线用户列表 (".concat(n.length,"人)"),body:r,buttons:[{text:"刷新",class:"btn-info",action:"refresh"},{text:"关闭",class:"btn-default",dismiss:!0}],onAction:function(){var e=u(c().m((function e(t,n){var r,a,l,u;return c().w((function(e){for(;;)switch(e.n){case 0:if("refresh"!==t){e.n=4;break}return e.p=1,e.n=2,o.A.get("/api/online-users/list");case 2:r=e.v,a=r.users||[],"",l=0===a.length?'<p class="text-center text-muted">当前没有在线用户</p>':'\n                  <div class="table-responsive">\n                    <table class="table table-striped">\n                      <thead>\n                        <tr>\n                          <th>用户名</th>\n                          <th>姓名</th>\n                          <th>角色</th>\n                          <th>登录时间</th>\n                          <th>最后活动</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        '.concat(a.map((function(e){return"\n                          <tr>\n                            <td>".concat(e.username,"</td>\n                            <td>").concat(e.name||"-","</td>\n                            <td>").concat(e.role||"-","</td>\n                            <td>").concat(s.formatDateTime(e.login_time),"</td>\n                            <td>").concat(s.formatDateTime(e.last_activity),"</td>\n                          </tr>\n                        ")})).join(""),"\n                      </tbody>\n                    </table>\n                  </div>\n                "),n.updateContent({title:"在线用户列表 (".concat(a.length,"人)"),body:l}),e.n=4;break;case 3:e.p=3,u=e.v,console.error("刷新用户列表失败:",u),i.A.alert("刷新失败，请稍后重试");case 4:return e.a(2)}}),e,null,[[1,3]])})));return function(t,n){return e.apply(this,arguments)}}()}),a.show(),e.n=3;break;case 2:e.p=2,l=e.v,console.error("获取在线用户列表失败:",l),i.A.alert("获取在线用户列表失败，请稍后重试");case 3:return e.a(2)}}),e,null,[[0,2]])}))),function(){return n.apply(this,arguments)})},{key:"formatDateTime",value:function(e){if(!e)return"-";try{return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch(t){return e}}},{key:"setUpdateFrequency",value:function(e){this.updateFrequency=e,this.updateInterval&&(this.stopAutoUpdate(),this.startAutoUpdate())}},{key:"refresh",value:function(){this.updateOnlineUsers()}},{key:"destroy",value:function(){this.stopAutoUpdate(),this.isInitialized=!1,console.log("在线用户模块已销毁")}}],t&&d(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,a}(),p=new v;function h(e){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h(e)}function y(){var e,t,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.toStringTag||"@@toStringTag";function r(n,o,i,r){var l=o&&o.prototype instanceof c?o:c,s=Object.create(l.prototype);return m(s,"_invoke",function(n,o,i){var r,c,l,s=0,u=i||[],d=!1,f={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return r=t,c=0,l=e,f.n=n,a}};function v(n,o){for(c=n,l=o,t=0;!d&&s&&!i&&t<u.length;t++){var i,r=u[t],v=f.p,p=r[2];n>3?(i=p===o)&&(c=r[4]||3,l=r[5]===e?r[3]:r[5],r[4]=3,r[5]=e):r[0]<=v&&((i=n<2&&v<r[1])?(c=0,f.v=o,f.n=r[1]):v<p&&(i=n<3||r[0]>o||o>p)&&(r[4]=n,r[5]=o,f.n=p,c=0))}if(i||n>1)return a;throw d=!0,o}return function(i,u,p){if(s>1)throw TypeError("Generator is already running");for(d&&1===u&&v(u,p),c=u,l=p;(t=c<2?e:l)||!d;){r||(c?c<3?(c>1&&(f.n=-1),v(c,l)):f.n=l:f.v=l);try{if(s=2,r){if(c||(i="next"),t=r[i]){if(!(t=t.call(r,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,c<2&&(c=0)}else 1===c&&(t=r.return)&&t.call(r),c<2&&(l=TypeError("The iterator does not provide a '"+i+"' method"),c=1);r=e}else if((t=(d=f.n<0)?l:n.call(o,f))!==a)break}catch(t){r=e,c=1,l=t}finally{s=1}}return{value:t,done:d}}}(n,i,r),!0),s}var a={};function c(){}function l(){}function s(){}t=Object.getPrototypeOf;var u=[][o]?t(t([][o]())):(m(t={},o,(function(){return this})),t),d=s.prototype=c.prototype=Object.create(u);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,m(e,i,"GeneratorFunction")),e.prototype=Object.create(d),e}return l.prototype=s,m(d,"constructor",s),m(s,"constructor",l),l.displayName="GeneratorFunction",m(s,i,"GeneratorFunction"),m(d),m(d,i,"Generator"),m(d,o,(function(){return this})),m(d,"toString",(function(){return"[object Generator]"})),(y=function(){return{w:r,m:f}})()}function m(e,t,n,o){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}m=function(e,t,n,o){if(t)i?i(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{function r(t,n){m(e,t,(function(e){return this._invoke(t,n,e)}))}r("next",0),r("throw",1),r("return",2)}},m(e,t,n,o)}function g(e,t,n,o,i,r,a){try{var c=e[r](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(o,i)}function b(e){return function(){var t=this,n=arguments;return new Promise((function(o,i){var r=e.apply(t,n);function a(e){g(r,o,i,a,c,"next",e)}function c(e){g(r,o,i,a,c,"throw",e)}a(void 0)}))}}function w(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,k(o.key),o)}}function k(e){var t=function(e){if("object"!=h(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=h(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==h(t)?t:t+""}var S=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.init=this.init.bind(this),this.publishReport=this.publishReport.bind(this),this.unpublishReport=this.unpublishReport.bind(this),this.publishReportFromPreview=this.publishReportFromPreview.bind(this),this.unpublishReportFromPreview=this.unpublishReportFromPreview.bind(this)},t=[{key:"init",value:function(){var e=this;this.isInitialized||(0,r.Qc)((function(){e.bindEvents(),e.isInitialized=!0,console.log("报告管理器初始化完成")}))}},{key:"bindEvents",value:function(){var e=this;document.addEventListener("click",(function(t){if(t.target.matches('[data-action="publish-report"]')){t.preventDefault();var n=t.target.dataset.activityId;n?e.publishReport(parseInt(n)):e.publishReportFromPreview()}})),document.addEventListener("click",(function(t){if(t.target.matches('[data-action="unpublish-report"]')){t.preventDefault();var n=t.target.dataset.activityId;n?e.unpublishReport(parseInt(n)):e.unpublishReportFromPreview()}}))}},{key:"publishReport",value:(l=b(y().m((function e(t){var n,r,a,c,l,s=arguments;return y().w((function(e){for(;;)switch(e.n){case 0:if(n=s.length>1&&void 0!==s[1]&&s[1],e.p=1,n){e.n=3;break}return e.n=2,i.A.confirm("确定要发布此通报到系统公告吗？","确认发布");case 2:if(e.v){e.n=3;break}return e.a(2);case 3:return e.n=4,o.A.post("/admin/activities/".concat(t,"/report/publish"),{force_republish:n});case 4:if(!(r=e.v).success){e.n=6;break}return e.n=5,i.A.alert("通报已成功发布到系统公告！","发布成功");case 5:window.location.reload(),e.n=7;break;case 6:return e.n=7,i.A.alert("发布失败：".concat(r.message),"发布失败");case 7:e.n=15;break;case 8:if(e.p=8,l=e.v,console.error("发布失败：",l),!l.message.includes("need_confirm")){e.n=14;break}if(e.p=9,!(a=JSON.parse(l.message)).need_confirm){e.n=12;break}return c=a.message,a.published_at&&(c+="\n\n原发布时间：".concat(a.published_at)),a.announcement_title&&(c+="\n原公告标题：".concat(a.announcement_title)),c+="\n\n重新发布将创建新的公告，原公告仍然保留。",e.n=10,i.A.confirm(c,"确认重新发布");case 10:if(!e.v){e.n=11;break}return e.n=11,this.publishReport(t,!0);case 11:return e.a(2);case 12:e.n=14;break;case 13:e.p=13,e.v;case 14:return e.n=15,i.A.alert("发布失败：".concat(l.message||"网络错误"),"发布失败");case 15:return e.a(2)}}),e,this,[[9,13],[1,8]])}))),function(e){return l.apply(this,arguments)})},{key:"unpublishReport",value:(c=b(y().m((function e(t){var n,r;return y().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,i.A.confirm("确定要取消发布此通报吗？这将从系统公告中删除相关内容。","确认取消发布");case 1:if(e.v){e.n=2;break}return e.a(2);case 2:return e.n=3,o.A.post("/admin/activities/".concat(t,"/report/unpublish"));case 3:if(!(n=e.v).success){e.n=5;break}return e.n=4,i.A.alert("通报已取消发布！","取消发布成功");case 4:window.location.reload(),e.n=6;break;case 5:return e.n=6,i.A.alert("取消发布失败：".concat(n.message),"取消发布失败");case 6:e.n=8;break;case 7:return e.p=7,r=e.v,console.error("取消发布失败：",r),e.n=8,i.A.alert("取消发布失败：".concat(r.message||"网络错误"),"取消发布失败");case 8:return e.a(2)}}),e,null,[[0,7]])}))),function(e){return c.apply(this,arguments)})},{key:"publishReportFromPreview",value:(a=b(y().m((function e(){var t,n,o=arguments;return y().w((function(e){for(;;)switch(e.n){case 0:if(t=o.length>0&&void 0!==o[0]&&o[0],n=this.getActivityIdFromUrl()){e.n=2;break}return e.n=1,i.A.alert("无法获取活动ID","错误");case 1:case 3:return e.a(2);case 2:return e.n=3,this.publishReport(n,t)}}),e,this)}))),function(){return a.apply(this,arguments)})},{key:"unpublishReportFromPreview",value:(n=b(y().m((function e(){var t;return y().w((function(e){for(;;)switch(e.n){case 0:if(t=this.getActivityIdFromUrl()){e.n=2;break}return e.n=1,i.A.alert("无法获取活动ID","错误");case 1:case 3:return e.a(2);case 2:return e.n=3,this.unpublishReport(t)}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"getActivityIdFromUrl",value:function(){for(var e=window.location.pathname.split("/"),t=0;t<e.length;t++)if("activities"===e[t]&&t+1<e.length){var n=parseInt(e[t+1]);if(!isNaN(n))return n}return null}},{key:"exposeGlobalFunctions",value:function(){window.publishReport=this.publishReport,window.unpublishReport=this.unpublishReport,window.publishReportFromPreview=this.publishReportFromPreview,window.unpublishReportFromPreview=this.unpublishReportFromPreview}},{key:"destroy",value:function(){delete window.publishReport,delete window.unpublishReport,delete window.publishReportFromPreview,delete window.unpublishReportFromPreview,this.isInitialized=!1,console.log("报告管理器已销毁")}}],t&&w(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,a,c,l}(),E=new S;function A(e){return A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},A(e)}function C(){var e,t,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.toStringTag||"@@toStringTag";function r(n,o,i,r){var l=o&&o.prototype instanceof c?o:c,s=Object.create(l.prototype);return P(s,"_invoke",function(n,o,i){var r,c,l,s=0,u=i||[],d=!1,f={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return r=t,c=0,l=e,f.n=n,a}};function v(n,o){for(c=n,l=o,t=0;!d&&s&&!i&&t<u.length;t++){var i,r=u[t],v=f.p,p=r[2];n>3?(i=p===o)&&(c=r[4]||3,l=r[5]===e?r[3]:r[5],r[4]=3,r[5]=e):r[0]<=v&&((i=n<2&&v<r[1])?(c=0,f.v=o,f.n=r[1]):v<p&&(i=n<3||r[0]>o||o>p)&&(r[4]=n,r[5]=o,f.n=p,c=0))}if(i||n>1)return a;throw d=!0,o}return function(i,u,p){if(s>1)throw TypeError("Generator is already running");for(d&&1===u&&v(u,p),c=u,l=p;(t=c<2?e:l)||!d;){r||(c?c<3?(c>1&&(f.n=-1),v(c,l)):f.n=l:f.v=l);try{if(s=2,r){if(c||(i="next"),t=r[i]){if(!(t=t.call(r,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,c<2&&(c=0)}else 1===c&&(t=r.return)&&t.call(r),c<2&&(l=TypeError("The iterator does not provide a '"+i+"' method"),c=1);r=e}else if((t=(d=f.n<0)?l:n.call(o,f))!==a)break}catch(t){r=e,c=1,l=t}finally{s=1}}return{value:t,done:d}}}(n,i,r),!0),s}var a={};function c(){}function l(){}function s(){}t=Object.getPrototypeOf;var u=[][o]?t(t([][o]())):(P(t={},o,(function(){return this})),t),d=s.prototype=c.prototype=Object.create(u);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,P(e,i,"GeneratorFunction")),e.prototype=Object.create(d),e}return l.prototype=s,P(d,"constructor",s),P(s,"constructor",l),l.displayName="GeneratorFunction",P(s,i,"GeneratorFunction"),P(d),P(d,i,"Generator"),P(d,o,(function(){return this})),P(d,"toString",(function(){return"[object Generator]"})),(C=function(){return{w:r,m:f}})()}function P(e,t,n,o){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}P=function(e,t,n,o){if(t)i?i(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{function r(t,n){P(e,t,(function(e){return this._invoke(t,n,e)}))}r("next",0),r("throw",1),r("return",2)}},P(e,t,n,o)}function T(e,t,n,o,i,r,a){try{var c=e[r](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(o,i)}function L(e){return function(){var t=this,n=arguments;return new Promise((function(o,i){var r=e.apply(t,n);function a(e){T(r,o,i,a,c,"next",e)}function c(e){T(r,o,i,a,c,"throw",e)}a(void 0)}))}}function F(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,x(o.key),o)}}function x(e){var t=function(e){if("object"!=A(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=A(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==A(t)?t:t+""}var I=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.init=this.init.bind(this),this.stopActivity=this.stopActivity.bind(this),this.reactivateActivity=this.reactivateActivity.bind(this),this.deleteActivity=this.deleteActivity.bind(this),this.editActivity=this.editActivity.bind(this),this.viewActivity=this.viewActivity.bind(this),this.exportActivity=this.exportActivity.bind(this)},t=[{key:"init",value:function(){var e=this;this.isInitialized||(0,r.Qc)((function(){e.bindEvents(),e.exposeGlobalFunctions(),e.isInitialized=!0,console.log("检查活动管理器初始化完成")}))}},{key:"bindEvents",value:function(){var e=this;document.addEventListener("click",(function(t){if(t.target.matches('[data-action="stop-activity"]')){t.preventDefault();var n=parseInt(t.target.dataset.activityId),o=t.target.dataset.activityName||"未知活动";e.stopActivity(n,o)}})),document.addEventListener("click",(function(t){if(t.target.matches('[data-action="reactivate-activity"]')){t.preventDefault();var n=parseInt(t.target.dataset.activityId),o=t.target.dataset.activityName||"未知活动";e.reactivateActivity(n,o)}})),document.addEventListener("click",(function(t){if(t.target.matches('[data-action="delete-activity"]')){t.preventDefault();var n=parseInt(t.target.dataset.activityId),o=t.target.dataset.activityName||"未知活动";e.deleteActivity(n,o)}})),document.addEventListener("click",(function(t){if(t.target.matches('[data-action="edit-activity"]')){t.preventDefault();var n=parseInt(t.target.dataset.activityId);e.editActivity(n)}})),document.addEventListener("click",(function(t){if(t.target.matches('[data-action="view-activity"]')){t.preventDefault();var n=parseInt(t.target.dataset.activityId);e.viewActivity(n)}})),document.addEventListener("click",(function(t){if(t.target.matches('[data-action="export-activity"]')){t.preventDefault();var n=parseInt(t.target.dataset.activityId),o=t.target.dataset.format||"excel";e.exportActivity(n,o)}}))}},{key:"stopActivity",value:(c=L(C().m((function e(t,n){var r,a;return C().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,console.log("停止检查活动:",t,n),e.n=1,i.A.confirm('确定要停止检查活动 "'.concat(n,'" 吗？\n\n停止后将无法继续进行检查操作，但可以重新激活。'),"确认停止活动");case 1:if(e.v){e.n=2;break}return e.a(2);case 2:return e.n=3,o.A.post("/admin/inspection_activities/".concat(t,"/complete"));case 3:if(!(r=e.v).success){e.n=5;break}return e.n=4,i.A.alert("检查活动已停止：".concat(r.message),"停止成功");case 4:window.location.reload(),e.n=6;break;case 5:return e.n=6,i.A.alert("停止失败：".concat(r.message),"停止失败");case 6:e.n=8;break;case 7:return e.p=7,a=e.v,console.error("停止检查活动失败:",a),e.n=8,i.A.alert("停止检查活动失败，请稍后重试","停止失败");case 8:return e.a(2)}}),e,null,[[0,7]])}))),function(e,t){return c.apply(this,arguments)})},{key:"reactivateActivity",value:(a=L(C().m((function e(t,n){var r,a;return C().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,console.log("重新激活检查活动:",t,n),e.n=1,i.A.confirm('确定要重新激活检查活动 "'.concat(n,'" 吗？\n\n重新激活后可以继续进行检查操作。'),"确认重新激活");case 1:if(e.v){e.n=2;break}return e.a(2);case 2:return e.n=3,o.A.post("/admin/inspection_activities/".concat(t,"/reactivate"));case 3:if(!(r=e.v).success){e.n=5;break}return e.n=4,i.A.alert("检查活动已重新激活：".concat(r.message),"激活成功");case 4:window.location.reload(),e.n=6;break;case 5:return e.n=6,i.A.alert("重新激活失败：".concat(r.message),"激活失败");case 6:e.n=8;break;case 7:return e.p=7,a=e.v,console.error("重新激活检查活动失败:",a),e.n=8,i.A.alert("重新激活检查活动失败，请稍后重试","激活失败");case 8:return e.a(2)}}),e,null,[[0,7]])}))),function(e,t){return a.apply(this,arguments)})},{key:"deleteActivity",value:(n=L(C().m((function e(t,n){var r,a;return C().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,console.log("删除检查活动:",t,n),e.n=1,i.A.confirm('确定要删除检查活动 "'.concat(n,'" 吗？\n\n删除后将无法恢复，相关的检查记录也会被删除。'),"确认删除活动");case 1:if(e.v){e.n=2;break}return e.a(2);case 2:return e.n=3,o.A.post("/admin/inspection_activities/".concat(t,"/delete"));case 3:if(!(r=e.v).success){e.n=5;break}return e.n=4,i.A.alert("检查活动已删除：".concat(r.message),"删除成功");case 4:window.location.reload(),e.n=6;break;case 5:return e.n=6,i.A.alert("删除失败：".concat(r.message),"删除失败");case 6:e.n=8;break;case 7:return e.p=7,a=e.v,console.error("删除检查活动失败:",a),e.n=8,i.A.alert("删除检查活动失败，请稍后重试","删除失败");case 8:return e.a(2)}}),e,null,[[0,7]])}))),function(e,t){return n.apply(this,arguments)})},{key:"editActivity",value:function(e){console.log("编辑检查活动:",e),window.location.href="/admin/inspection_activities/".concat(e,"/edit")}},{key:"viewActivity",value:function(e){console.log("查看检查活动:",e),window.location.href="/admin/inspection_activities/".concat(e)}},{key:"exportActivity",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"excel";console.log("导出检查活动:",e,t);var n="/admin/inspection_activities/".concat(e,"/export?format=").concat(t),o=document.createElement("a");o.href=n,o.download="",document.body.appendChild(o),o.click(),document.body.removeChild(o)}},{key:"exposeGlobalFunctions",value:function(){window.stopInspectionActivity=this.stopActivity,window.reactivateInspectionActivity=this.reactivateActivity,window.deleteInspectionActivity=this.deleteActivity,window.editInspectionActivity=this.editActivity,window.viewInspectionActivity=this.viewActivity,window.exportInspectionActivity=this.exportActivity}},{key:"destroy",value:function(){delete window.stopInspectionActivity,delete window.reactivateInspectionActivity,delete window.deleteInspectionActivity,delete window.editInspectionActivity,delete window.viewInspectionActivity,delete window.exportInspectionActivity,this.isInitialized=!1,console.log("检查活动管理器已销毁")}}],t&&F(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,a,c}(),z=new I;function j(e){return j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},j(e)}function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function M(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){D(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function D(e,t,n){return(t=B(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,B(o.key),o)}}function B(e){var t=function(e){if("object"!=j(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=j(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==j(t)?t:t+""}var R=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.charts=new Map,this.init=this.init.bind(this),this.initActivityChart=this.initActivityChart.bind(this),this.initDistributionChart=this.initDistributionChart.bind(this),this.createChart=this.createChart.bind(this)},t=[{key:"init",value:function(){var e=this;this.isInitialized||(0,r.Qc)((function(){e.initCharts(),e.isInitialized=!0,console.log("图表管理器初始化完成")}))}},{key:"initCharts",value:function(){var e=document.getElementById("activity-chart"),t=document.getElementById("distribution-chart");e&&"undefined"!=typeof Chart&&this.initActivityChart(e),t&&"undefined"!=typeof Chart&&this.initDistributionChart(t),this.autoDiscoverCharts()}},{key:"initActivityChart",value:function(e){try{var t=new Chart(e,{type:"line",data:{labels:["1月","2月","3月","4月","5月","6月"],datasets:[{label:"检查活动",data:[12,19,3,5,2,3],borderColor:"#2e6da4",backgroundColor:"rgba(46, 109, 164, 0.1)",borderWidth:2,tension:.3,fill:!0}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!0,position:"top"}},scales:{y:{beginAtZero:!0,grid:{color:"rgba(0,0,0,0.1)"}},x:{grid:{color:"rgba(0,0,0,0.1)"}}}}});this.charts.set("activity-chart",t),console.log("活动图表初始化完成")}catch(e){console.error("活动图表初始化失败:",e)}}},{key:"initDistributionChart",value:function(e){try{var t=new Chart(e,{type:"doughnut",data:{labels:["正常","轻微问题","严重问题"],datasets:[{data:[70,20,10],backgroundColor:["#28a745","#ffc107","#dc3545"],borderWidth:2,borderColor:"#fff"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!0,position:"bottom"}}}});this.charts.set("distribution-chart",t),console.log("分布图表初始化完成")}catch(e){console.error("分布图表初始化失败:",e)}}},{key:"autoDiscoverCharts",value:function(){var e=this;document.querySelectorAll("[data-chart-type]").forEach((function(t){var n=t.dataset.chartType,o=t.id||"chart-".concat(Date.now());t.id||(t.id=o);try{e.createChart(t,n)}catch(e){console.error("创建图表失败 (".concat(o,"):"),e)}}))}},{key:"createChart",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("undefined"==typeof Chart)return console.warn("Chart.js未加载，无法创建图表"),null;var o={type:t,data:n.data||this.getDefaultData(t),options:M({responsive:!0,maintainAspectRatio:!1},n.options)};try{var i=new Chart(e,o);return this.charts.set(e.id,i),console.log("图表创建成功: ".concat(e.id," (").concat(t,")")),i}catch(t){return console.error("图表创建失败: ".concat(e.id),t),null}}},{key:"getDefaultData",value:function(e){var t={line:{labels:["1月","2月","3月","4月","5月","6月"],datasets:[{label:"数据",data:[12,19,3,5,2,3],borderColor:"#007bff",backgroundColor:"rgba(0, 123, 255, 0.1)",borderWidth:2,tension:.3}]},bar:{labels:["类别1","类别2","类别3","类别4"],datasets:[{label:"数据",data:[12,19,3,5],backgroundColor:["rgba(255, 99, 132, 0.8)","rgba(54, 162, 235, 0.8)","rgba(255, 205, 86, 0.8)","rgba(75, 192, 192, 0.8)"]}]},pie:{labels:["类别1","类别2","类别3"],datasets:[{data:[30,50,20],backgroundColor:["#FF6384","#36A2EB","#FFCE56"]}]},doughnut:{labels:["类别1","类别2","类别3"],datasets:[{data:[30,50,20],backgroundColor:["#FF6384","#36A2EB","#FFCE56"]}]}};return t[e]||t.line}},{key:"updateChart",value:function(e,t){var n=this.charts.get(e);n?(n.data=t,n.update(),console.log("图表数据已更新: ".concat(e))):console.warn("图表不存在: ".concat(e))}},{key:"destroyChart",value:function(e){var t=this.charts.get(e);t&&(t.destroy(),this.charts.delete(e),console.log("图表已销毁: ".concat(e)))}},{key:"getChart",value:function(e){return this.charts.get(e)||null}},{key:"destroy",value:function(){this.charts.forEach((function(e,t){e.destroy()})),this.charts.clear(),this.isInitialized=!1,console.log("图表管理器已销毁")}}],t&&_(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}(),q=new R;function H(e){return H="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},H(e)}function N(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,G(o.key),o)}}function G(e){var t=function(e){if("object"!=H(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=H(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==H(t)?t:t+""}var U=new(function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.tables=new Map,this.init=this.init.bind(this),this.sortTable=this.sortTable.bind(this),this.searchTable=this.searchTable.bind(this),this.goToPage=this.goToPage.bind(this),this.updatePagination=this.updatePagination.bind(this)},t=[{key:"init",value:function(){var e=this;this.isInitialized||(0,r.Qc)((function(){e.initTables(),e.bindEvents(),e.isInitialized=!0,console.log("表格管理器初始化完成")}))}},{key:"initTables",value:function(){var e=this;document.querySelectorAll("table[data-sortable], .data-table").forEach((function(t){e.enhanceTable(t)}))}},{key:"enhanceTable",value:function(e){var t=e.id||"table-".concat(Date.now());e.id||(e.id=t);var n={sortable:e.hasAttribute("data-sortable")||e.classList.contains("sortable"),searchable:e.hasAttribute("data-searchable")||e.classList.contains("searchable"),paginated:e.hasAttribute("data-paginated")||e.classList.contains("paginated"),pageSize:parseInt(e.dataset.pageSize)||10};this.tables.set(t,{element:e,config:n,currentPage:1,sortColumn:-1,sortDirection:"asc",searchTerm:""}),n.sortable&&this.addSortableHeaders(e),n.searchable&&this.addSearchBox(e),n.paginated&&this.addPagination(e),console.log("表格增强完成: ".concat(t))}},{key:"addSortableHeaders",value:function(e){var t=this;e.querySelectorAll("thead th").forEach((function(n,o){if(n.hasAttribute("data-sortable")||!n.hasAttribute("data-no-sort")){n.style.cursor="pointer",n.classList.add("sortable-header");var i=document.createElement("span");i.className="sort-icon",i.innerHTML=' <i class="fa fa-sort"></i>',n.appendChild(i),n.addEventListener("click",(function(){t.sortTable(e.id,o)}))}}))}},{key:"addSearchBox",value:function(e){var t=this,n=document.createElement("div");n.className="table-search-container mb-3",n.innerHTML='\n      <div class="row">\n        <div class="col-md-6">\n          <div class="input-group">\n            <input type="text" class="form-control table-search" placeholder="搜索表格内容...">\n            <span class="input-group-btn">\n              <button class="btn btn-default" type="button">\n                <i class="fa fa-search"></i>\n              </button>\n            </span>\n          </div>\n        </div>\n      </div>\n    ',e.parentNode.insertBefore(n,e),n.querySelector(".table-search").addEventListener("input",(function(n){t.searchTable(e.id,n.target.value)}))}},{key:"addPagination",value:function(e){var t=document.createElement("div");t.className="table-pagination-container mt-3",e.parentNode.appendChild(t),this.updatePagination(e.id)}},{key:"sortTable",value:function(e,t){var n=this.tables.get(e);if(n){var o=n.element,i=o.querySelector("tbody");if(i){var r=Array.from(i.querySelectorAll("tr")),a="asc";n.sortColumn===t&&"asc"===n.sortDirection&&(a="desc"),n.sortColumn=t,n.sortDirection=a,r.sort((function(e,n){var o,i,r=(null===(o=e.cells[t])||void 0===o?void 0:o.textContent.trim())||"",c=(null===(i=n.cells[t])||void 0===i?void 0:i.textContent.trim())||"",l=parseFloat(r),s=parseFloat(c);if(isNaN(l)||isNaN(s)){var u=r.localeCompare(c,"zh-CN");return"asc"===a?u:-u}return"asc"===a?l-s:s-l})),r.forEach((function(e){return i.appendChild(e)})),this.updateSortIcons(o,t,a),n.config.paginated&&this.updatePagination(e),console.log("表格排序完成: ".concat(e,", 列: ").concat(t,", 方向: ").concat(a))}}}},{key:"updateSortIcons",value:function(e,t,n){e.querySelectorAll("thead th").forEach((function(e,o){var i=e.querySelector(".sort-icon i");i&&(i.className=o===t?"asc"===n?"fa fa-sort-up":"fa fa-sort-down":"fa fa-sort")}))}},{key:"searchTable",value:function(e,t){var n=this.tables.get(e);if(n){var o=n.element.querySelector("tbody");o&&(n.searchTerm=t.toLowerCase(),o.querySelectorAll("tr").forEach((function(e){var t=e.textContent.toLowerCase().includes(n.searchTerm);e.style.display=t?"":"none"})),n.config.paginated&&(n.currentPage=1,this.updatePagination(e)),console.log("表格搜索完成: ".concat(e,", 搜索词: ").concat(t)))}}},{key:"updatePagination",value:function(e){var t=this.tables.get(e);if(t){var n=t.element,o=n.querySelector("tbody"),i=n.parentNode.querySelector(".table-pagination-container");if(o&&i){var r=Array.from(o.querySelectorAll("tr")).filter((function(e){return"none"!==e.style.display})),a=r.length,c=t.config.pageSize,l=Math.ceil(a/c),s=Math.min(t.currentPage,l||1);r.forEach((function(e,t){var n=(s-1)*c,o=n+c;e.style.display=t>=n&&t<o?"":"none"})),this.renderPaginationControls(i,s,l,e),t.currentPage=s}}}},{key:"renderPaginationControls",value:function(e,t,n,o){if(n<=1)e.innerHTML="";else{var i='<nav><ul class="pagination">';i+='\n      <li class="page-item '.concat(1===t?"disabled":"",'">\n        <a class="page-link" href="#" data-page="').concat(t-1,'" data-table="').concat(o,'">上一页</a>\n      </li>\n    ');for(var r=Math.max(1,t-2),a=Math.min(n,t+2),c=r;c<=a;c++)i+='\n        <li class="page-item '.concat(c===t?"active":"",'">\n          <a class="page-link" href="#" data-page="').concat(c,'" data-table="').concat(o,'">').concat(c,"</a>\n        </li>\n      ");i+='\n      <li class="page-item '.concat(t===n?"disabled":"",'">\n        <a class="page-link" href="#" data-page="').concat(t+1,'" data-table="').concat(o,'">下一页</a>\n      </li>\n    '),i+="</ul></nav>",e.innerHTML=i}}},{key:"bindEvents",value:function(){var e=this;document.addEventListener("click",(function(t){if(t.target.matches(".page-link[data-page]")){t.preventDefault();var n=parseInt(t.target.dataset.page),o=t.target.dataset.table;e.goToPage(o,n)}}))}},{key:"goToPage",value:function(e,t){var n=this.tables.get(e);n&&(n.currentPage=t,this.updatePagination(e))}},{key:"exposeGlobalFunctions",value:function(){window.sortTable=this.sortTable}},{key:"destroy",value:function(){this.tables.clear(),this.isInitialized=!1,console.log("表格管理器已销毁")}}],t&&N(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}());function X(e){return X="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},X(e)}function V(){var e,t,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.toStringTag||"@@toStringTag";function r(n,o,i,r){var l=o&&o.prototype instanceof c?o:c,s=Object.create(l.prototype);return J(s,"_invoke",function(n,o,i){var r,c,l,s=0,u=i||[],d=!1,f={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return r=t,c=0,l=e,f.n=n,a}};function v(n,o){for(c=n,l=o,t=0;!d&&s&&!i&&t<u.length;t++){var i,r=u[t],v=f.p,p=r[2];n>3?(i=p===o)&&(c=r[4]||3,l=r[5]===e?r[3]:r[5],r[4]=3,r[5]=e):r[0]<=v&&((i=n<2&&v<r[1])?(c=0,f.v=o,f.n=r[1]):v<p&&(i=n<3||r[0]>o||o>p)&&(r[4]=n,r[5]=o,f.n=p,c=0))}if(i||n>1)return a;throw d=!0,o}return function(i,u,p){if(s>1)throw TypeError("Generator is already running");for(d&&1===u&&v(u,p),c=u,l=p;(t=c<2?e:l)||!d;){r||(c?c<3?(c>1&&(f.n=-1),v(c,l)):f.n=l:f.v=l);try{if(s=2,r){if(c||(i="next"),t=r[i]){if(!(t=t.call(r,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,c<2&&(c=0)}else 1===c&&(t=r.return)&&t.call(r),c<2&&(l=TypeError("The iterator does not provide a '"+i+"' method"),c=1);r=e}else if((t=(d=f.n<0)?l:n.call(o,f))!==a)break}catch(t){r=e,c=1,l=t}finally{s=1}}return{value:t,done:d}}}(n,i,r),!0),s}var a={};function c(){}function l(){}function s(){}t=Object.getPrototypeOf;var u=[][o]?t(t([][o]())):(J(t={},o,(function(){return this})),t),d=s.prototype=c.prototype=Object.create(u);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,J(e,i,"GeneratorFunction")),e.prototype=Object.create(d),e}return l.prototype=s,J(d,"constructor",s),J(s,"constructor",l),l.displayName="GeneratorFunction",J(s,i,"GeneratorFunction"),J(d),J(d,i,"Generator"),J(d,o,(function(){return this})),J(d,"toString",(function(){return"[object Generator]"})),(V=function(){return{w:r,m:f}})()}function J(e,t,n,o){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}J=function(e,t,n,o){if(t)i?i(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{function r(t,n){J(e,t,(function(e){return this._invoke(t,n,e)}))}r("next",0),r("throw",1),r("return",2)}},J(e,t,n,o)}function W(e,t,n,o,i,r,a){try{var c=e[r](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(o,i)}function Q(e){return function(){var t=this,n=arguments;return new Promise((function(o,i){var r=e.apply(t,n);function a(e){W(r,o,i,a,c,"next",e)}function c(e){W(r,o,i,a,c,"throw",e)}a(void 0)}))}}function Y(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Z(o.key),o)}}function Z(e){var t=function(e){if("object"!=X(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=X(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==X(t)?t:t+""}var K=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.currentCaptcha="",this.deleteCallback=null,this.deleteCallbackParams=null,this.init=this.init.bind(this),this.generateCaptcha=this.generateCaptcha.bind(this),this.showCaptchaDeleteModal=this.showCaptchaDeleteModal.bind(this),this.verifyCaptchaAndDelete=this.verifyCaptchaAndDelete.bind(this)},t=[{key:"init",value:function(){var e=this;this.isInitialized||(0,r.Qc)((function(){e.bindEvents(),e.isInitialized=!0,console.log("验证码删除模块初始化完成")}))}},{key:"generateCaptcha",value:function(){return Math.floor(1e3+9e3*Math.random()).toString()}},{key:"showCaptchaDeleteModal",value:function(e,t,n,o){var r=this;this.currentCaptcha=this.generateCaptcha(),console.log("生成验证码:",this.currentCaptcha),this.deleteCallback=n,this.deleteCallbackParams=o;var a='\n      <div class="form-group">\n        <label>请输入验证码：</label>\n        <div style="margin: 10px 0; text-align: center;">\n          <span id="captchaCode" style="font-size: 24px; font-weight: bold; background-color: #f0f0f0; color: #333; letter-spacing: 5px; padding: 8px 15px; border: 2px dashed #999; border-radius: 5px; display: inline-block;">\n            '.concat(this.currentCaptcha,'\n          </span>\n        </div>\n        <input type="text" id="captchaInput" class="form-control" placeholder="请输入上方的4位验证码" maxlength="4" autocomplete="off">\n        <div id="captchaError" class="text-danger" style="display: none; margin-top: 5px;">\n          验证码错误，请重新输入\n        </div>\n      </div>\n    '),c=i.A.create({title:e||"确认删除",body:"\n        <p>".concat(t||"确定要删除吗？此操作不可恢复！","</p>\n        ").concat(a,"\n      "),buttons:[{text:"取消",class:"btn-secondary",dismiss:!0},{text:"确认删除",class:"btn-danger",action:"confirm"}],onAction:function(e,t){"confirm"===e&&r.verifyCaptchaAndDelete(t)},onShow:function(){setTimeout((function(){var e=document.getElementById("captchaInput");e&&(e.focus(),e.addEventListener("keypress",(function(e){"Enter"===e.key&&r.verifyCaptchaAndDelete(c)})))}),100)}});c.show()}},{key:"verifyCaptchaAndDelete",value:function(e){var t=document.getElementById("captchaInput"),n=document.getElementById("captchaError");t&&(t.value.trim()===this.currentCaptcha?(e.hide(),"function"==typeof this.deleteCallback&&this.deleteCallback(this.deleteCallbackParams)):(n&&(n.style.display="block"),t.value="",t.focus()))}},{key:"performDelete",value:(n=Q(V().m((function e(t,n,r){return V().w((function(e){for(;;)switch(e.n){case 0:this.showCaptchaDeleteModal(n,r,Q(V().m((function e(){var n,r;return V().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,o.A.post(t);case 1:if(!1===(n=e.v).success){e.n=3;break}return e.n=2,i.A.alert("删除成功！","成功");case 2:window.location.reload(),e.n=4;break;case 3:return e.n=4,i.A.alert("删除失败：".concat(n.message||"未知错误"),"错误");case 4:e.n=6;break;case 5:return e.p=5,r=e.v,console.error("删除操作失败:",r),e.n=6,i.A.alert("删除操作失败，请重试","错误");case 6:return e.a(2)}}),e,null,[[0,5]])}))));case 1:return e.a(2)}}),e,this)}))),function(e,t,o){return n.apply(this,arguments)})},{key:"bindEvents",value:function(){var e=this;document.addEventListener("click",(function(t){if(t.target.matches('.delete-with-captcha, [data-action="delete-with-captcha"]')){t.preventDefault();var n=t.target.dataset.url||t.target.href,o=t.target.dataset.title||"确认删除",i=t.target.dataset.message||"确定要删除吗？此操作不可恢复！";if(!n)return void console.error("删除URL未指定");e.performDelete(n,o,i)}})),"undefined"!=typeof $&&($(document).off("click",".delete-with-captcha"),$(document).on("click",".delete-with-captcha",(function(t){t.preventDefault();var n=$(t.target),o=n.data("url")||n.attr("href"),i=n.data("title")||"确认删除",r=n.data("message")||"确定要删除吗？此操作不可恢复！";o?e.performDelete(o,i,r):console.error("删除URL未指定")})))}},{key:"exposeGlobalFunctions",value:function(){window.showCaptchaDeleteModal=this.showCaptchaDeleteModal,window.generateCaptcha=this.generateCaptcha,window.verifyCaptchaAndDelete=this.verifyCaptchaAndDelete}},{key:"destroy",value:function(){delete window.showCaptchaDeleteModal,delete window.generateCaptcha,delete window.verifyCaptchaAndDelete,this.isInitialized=!1,console.log("验证码删除模块已销毁")}}],t&&Y(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}(),ee=new K;function te(e){return te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},te(e)}function ne(){var e,t,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.toStringTag||"@@toStringTag";function r(n,o,i,r){var l=o&&o.prototype instanceof c?o:c,s=Object.create(l.prototype);return oe(s,"_invoke",function(n,o,i){var r,c,l,s=0,u=i||[],d=!1,f={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return r=t,c=0,l=e,f.n=n,a}};function v(n,o){for(c=n,l=o,t=0;!d&&s&&!i&&t<u.length;t++){var i,r=u[t],v=f.p,p=r[2];n>3?(i=p===o)&&(c=r[4]||3,l=r[5]===e?r[3]:r[5],r[4]=3,r[5]=e):r[0]<=v&&((i=n<2&&v<r[1])?(c=0,f.v=o,f.n=r[1]):v<p&&(i=n<3||r[0]>o||o>p)&&(r[4]=n,r[5]=o,f.n=p,c=0))}if(i||n>1)return a;throw d=!0,o}return function(i,u,p){if(s>1)throw TypeError("Generator is already running");for(d&&1===u&&v(u,p),c=u,l=p;(t=c<2?e:l)||!d;){r||(c?c<3?(c>1&&(f.n=-1),v(c,l)):f.n=l:f.v=l);try{if(s=2,r){if(c||(i="next"),t=r[i]){if(!(t=t.call(r,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,c<2&&(c=0)}else 1===c&&(t=r.return)&&t.call(r),c<2&&(l=TypeError("The iterator does not provide a '"+i+"' method"),c=1);r=e}else if((t=(d=f.n<0)?l:n.call(o,f))!==a)break}catch(t){r=e,c=1,l=t}finally{s=1}}return{value:t,done:d}}}(n,i,r),!0),s}var a={};function c(){}function l(){}function s(){}t=Object.getPrototypeOf;var u=[][o]?t(t([][o]())):(oe(t={},o,(function(){return this})),t),d=s.prototype=c.prototype=Object.create(u);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,oe(e,i,"GeneratorFunction")),e.prototype=Object.create(d),e}return l.prototype=s,oe(d,"constructor",s),oe(s,"constructor",l),l.displayName="GeneratorFunction",oe(s,i,"GeneratorFunction"),oe(d),oe(d,i,"Generator"),oe(d,o,(function(){return this})),oe(d,"toString",(function(){return"[object Generator]"})),(ne=function(){return{w:r,m:f}})()}function oe(e,t,n,o){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}oe=function(e,t,n,o){if(t)i?i(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{function r(t,n){oe(e,t,(function(e){return this._invoke(t,n,e)}))}r("next",0),r("throw",1),r("return",2)}},oe(e,t,n,o)}function ie(e,t,n,o,i,r,a){try{var c=e[r](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(o,i)}function re(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,ae(o.key),o)}}function ae(e){var t=function(e){if("object"!=te(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=te(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==te(t)?t:t+""}var ce=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.isLoading=!1,this.currentUrl=null,this.init=this.init.bind(this),this.loadContent=this.loadContent.bind(this),this.updateContent=this.updateContent.bind(this),this.showLoader=this.showLoader.bind(this),this.hideLoader=this.hideLoader.bind(this)},t=[{key:"init",value:function(){var e=this;this.isInitialized||(0,r.Qc)((function(){e.bindEvents(),e.setupLoader(),e.isInitialized=!0,console.log("AJAX加载器初始化完成")}))}},{key:"setupLoader",value:function(){if(!document.getElementById("ajax-loader")){var e=document.getElementById("content-block");if(!e)return;var t=document.createElement("div");if(t.id="ajax-loader",t.className="ajax-loader",t.innerHTML='\n        <div class="loader-content">\n          <div class="spinner"></div>\n        </div>\n      ',t.style.cssText="\n        position: absolute;\n        top: 10px;\n        right: 10px;\n        width: 60px;\n        height: 60px;\n        background: rgba(255, 255, 255, 0.9);\n        border-radius: 8px;\n        box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        z-index: 1000;\n        border: 1px solid #e0e0e0;\n        display: none;\n        justify-content: center;\n        align-items: center;\n      ",t.querySelector(".loader-content").style.cssText="\n        text-align: center;\n        padding: 0;\n        background: transparent;\n        border-radius: 0;\n        box-shadow: none;\n      ",t.querySelector(".spinner").style.cssText="\n        width: 30px;\n        height: 30px;\n        border: 3px solid #f3f3f3;\n        border-top: 3px solid #3498db;\n        border-radius: 50%;\n        animation: spin 1s linear infinite;\n        margin: 0 auto;\n      ",!document.getElementById("ajax-loader-styles")){var n=document.createElement("style");n.id="ajax-loader-styles",n.textContent="\n          @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n        ",document.head.appendChild(n)}"static"===getComputedStyle(e).position&&(e.style.position="relative"),e.appendChild(t)}}},{key:"bindEvents",value:function(){var e=this;document.addEventListener("click",(function(t){var n=t.target.closest("a[href]");if(n&&e.shouldUseAjax(n)){t.preventDefault();var o=n.getAttribute("href");e.loadContent(o),e.highlightActiveLink(n)}})),window.addEventListener("popstate",(function(t){t.state&&t.state.ajaxLoaded&&e.loadContent(window.location.pathname,!1)}))}},{key:"shouldUseAjax",value:function(e){if("false"===e.getAttribute("data-ajax-load"))return!1;var t=e.getAttribute("href");return!(!t||t.startsWith("http")||t.startsWith("mailto:")||t.startsWith("tel:")||["/admin/menu_management","/logout","/login"].some((function(e){return t.includes(e)}))||null===e.closest(".sidebar"))}},{key:"loadContent",value:(n=ne().m((function e(t){var n,i,r,a,c=arguments;return ne().w((function(e){for(;;)switch(e.n){case 0:if(n=!(c.length>1&&void 0!==c[1])||c[1],console.log("🔍 AjaxLoader.loadContent 被调用:",{url:t,updateHistory:n,caller:"AjaxLoader.loadContent",stack:(new Error).stack.split("\n").slice(1,10)}),"#"!==t){e.n=1;break}return console.log("🚫 AjaxLoader: 跳过 # 链接，不处理"),e.a(2);case 1:if(!this.isLoading||this.currentUrl!==t){e.n=2;break}return console.log("防止重复请求:",t),e.a(2);case 2:return this.isLoading=!0,this.currentUrl=t,e.p=3,this.showLoader(),e.n=4,fetch(t,{method:"GET",headers:{"X-Requested-With":"XMLHttpRequest","X-CSRFToken":o.A.getCsrfToken()}});case 4:if((i=e.v).ok){e.n=5;break}throw new Error("HTTP ".concat(i.status,": ").concat(i.statusText));case 5:return e.n=6,i.text();case 6:r=e.v,this.updateContent(r),n&&history.pushState({ajaxLoaded:!0},"",t),console.log("AJAX加载完成:",t),e.n=8;break;case 7:e.p=7,a=e.v,console.error("AJAX加载失败:",a),window.location.href=t;case 8:return e.p=8,this.hideLoader(),this.isLoading=!1,this.currentUrl=null,e.f(8);case 9:return e.a(2)}}),e,this,[[3,7,8,9]])})),i=function(){var e=this,t=arguments;return new Promise((function(o,i){var r=n.apply(e,t);function a(e){ie(r,o,i,a,c,"next",e)}function c(e){ie(r,o,i,a,c,"throw",e)}a(void 0)}))},function(e){return i.apply(this,arguments)})},{key:"updateContent",value:function(e){var t=document.createElement("div");t.innerHTML=e;var n=t.querySelector("#main-content, .main-content, .content-wrapper"),o=document.querySelector("#main-content, .main-content, .content-wrapper");if(n&&o)o.innerHTML=n.innerHTML;else{var i=t.querySelector("body");if(i){var r=document.querySelector(".sidebar"),a=i.querySelector(".main-content, .content-wrapper")||i.querySelector(".container-fluid");if(a&&r){var c=document.querySelector(".main-content, .content-wrapper")||document.querySelector(".container-fluid");c&&(c.innerHTML=a.innerHTML)}}}var l=t.querySelector("title");l&&(document.title=l.textContent),this.reinitializeScripts()}},{key:"reinitializeScripts",value:function(){var e=new CustomEvent("ajaxContentLoaded",{detail:{timestamp:Date.now()}});document.dispatchEvent(e),(0,r.I6)((function(e){e('[data-toggle="tooltip"]').tooltip(),e('[data-toggle="popover"]').popover(),void 0!==window.App&&window.App.initForms&&window.App.initForms()}))}},{key:"highlightActiveLink",value:function(e){document.querySelectorAll(".sidebar a").forEach((function(e){e.classList.remove("active")})),e.classList.add("active");var t=e.closest(".collapse");t&&(0,r.I6)((function(e){e(t).addClass("show"),e('[data-target="#'.concat(t.id,'"]')).attr("aria-expanded","true").addClass("active")}))}},{key:"showLoader",value:function(){var e=document.getElementById("ajax-loader");e&&(e.style.display="flex")}},{key:"hideLoader",value:function(){var e=document.getElementById("ajax-loader");e&&(e.style.display="none"),document.querySelectorAll(".loading, .loading-spinner, .loading-animation").forEach((function(e){e.style.display="none"}))}},{key:"exposeGlobalFunctions",value:function(){window.loadContent=this.loadContent,window.showLoader=this.showLoader,window.hideLoader=this.hideLoader}},{key:"destroy",value:function(){delete window.loadContent,delete window.showLoader,delete window.hideLoader,this.isInitialized=!1,console.log("AJAX加载器已销毁")}}],t&&re(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,i}(),le=new ce;function se(e){return se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},se(e)}function ue(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,de(o.key),o)}}function de(e){var t=function(e){if("object"!=se(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=se(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==se(t)?t:t+""}var fe=new(function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.init=this.init.bind(this),this.setupSelectPicker=this.setupSelectPicker.bind(this),this.handleDropdownPosition=this.handleDropdownPosition.bind(this),this.handleScroll=this.handleScroll.bind(this),this.handleResize=this.handleResize.bind(this)},t=[{key:"init",value:function(){var e=this;this.isInitialized||(0,r.Qc)((function(){(0,r.I6)((function(t){e.setupSelectPicker(t),e.bindEvents(t),e.isInitialized=!0,console.log("下拉框修复模块初始化完成")}))}))}},{key:"setupSelectPicker",value:function(e){e.fn.selectpicker?(e.fn.selectpicker.defaults={noneSelectedText:"请选择",liveSearch:!0,liveSearchPlaceholder:"搜索...",size:10,width:"100%",container:"body",mobile:!1},e(".selectpicker").selectpicker("refresh"),console.log("Bootstrap-select下拉框初始化完成")):console.warn("Bootstrap-select未加载，跳过下拉框修复")}},{key:"bindEvents",value:function(e){var t=this;e(document).on("shown.bs.select",".selectpicker",(function(n){t.handleDropdownPosition(e(n.target),e)})),e(window).on("scroll.dropdownFix",(function(){t.handleScroll(e)})),e(window).on("resize.dropdownFix",(function(){t.handleResize(e)}))}},{key:"handleDropdownPosition",value:function(e,t){var n=e.data("selectpicker");if(n){var o=n.$menu,i=n.$button;if(o&&i){var r=i.offset(),a=i.outerWidth(),c=i.outerHeight(),l=t(window).height(),s=t(window).scrollTop(),u=r.top+c+5,d=r.left;o.css({position:"fixed",top:u+"px",left:d+"px","min-width":a+"px","max-width":Math.max(a,300)+"px","z-index":9999});var f=o.outerHeight();u+f>l+s&&o.css("top",r.top-f-5+"px");var v=o.outerWidth(),p=t(window).width();d+v>p&&o.css("left",p-v-10+"px"),o.addClass("custom-dropdown-position")}}}},{key:"handleScroll",value:function(e){var t=e(".bootstrap-select.open");0!==t.length&&t.each((function(t,n){var o=e(n),i=o.find("button.dropdown-toggle"),r=o.data("selectpicker");if(r){var a=r.$menu;if(a&&i){var c=i.offset(),l=i.outerHeight(),s=a.outerHeight();e(window).height(),e(window).scrollTop(),parseInt(a.css("top"))>c.top?a.css("top",c.top+l+5+"px"):a.css("top",c.top-s-5+"px"),a.css("left",c.left+"px")}}}))}},{key:"handleResize",value:function(e){e(".bootstrap-select.open").removeClass("open"),e(".dropdown-menu.open").removeClass("open").hide()}},{key:"refreshAll",value:function(){(0,r.I6)((function(e){e.fn.selectpicker&&(e(".selectpicker").selectpicker("refresh"),console.log("所有下拉框已刷新"))}))}},{key:"destroy",value:function(){(0,r.I6)((function(e){e(window).off("scroll.dropdownFix resize.dropdownFix"),e(document).off("shown.bs.select",".selectpicker")})),this.isInitialized=!1,console.log("下拉框修复模块已销毁")}}],t&&ue(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}());function ve(e){return ve="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ve(e)}function pe(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,he(o.key),o)}}function he(e){var t=function(e){if("object"!=ve(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=ve(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==ve(t)?t:t+""}var ye=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.activeDropdown=null},t=[{key:"init",value:function(){this.isInitialized||(console.log("🔽 初始化通用下拉菜单组件"),this.bindEvents(),this.initBootstrapDropdown(),this.isInitialized=!0)}},{key:"bindEvents",value:function(){var e=this;$(document).on("click.universal-dropdown",'[data-toggle="dropdown"]',(function(t){e.handleDropdownToggle(t)})),$(document).on("click.universal-dropdown-close",(function(t){e.handleOutsideClick(t)})),$(window).on("resize.universal-dropdown",(function(){e.activeDropdown&&e.adjustPosition(e.activeDropdown)}))}},{key:"initBootstrapDropdown",value:function(){window.$&&$.fn.dropdown?($('[data-toggle="dropdown"]').dropdown(),console.log("🔽 Bootstrap下拉菜单已初始化")):console.warn("⚠️ Bootstrap下拉菜单插件不可用，使用自定义实现")}},{key:"handleDropdownToggle",value:function(e){e.preventDefault(),e.stopPropagation();var t=$(e.currentTarget),n=t.next(".dropdown-menu");0!==n.length?(this.closeAllDropdowns(n),n.hasClass("show")?this.hideDropdown(n,t):this.showDropdown(n,t)):console.warn("⚠️ 未找到下拉菜单元素")}},{key:"showDropdown",value:function(e,t){e.addClass("show").show(),t.attr("aria-expanded","true"),this.adjustPosition(e,t),this.activeDropdown=e,console.log("🔽 显示下拉菜单")}},{key:"hideDropdown",value:function(e,t){e.removeClass("show").addClass("force-hidden"),t.attr("aria-expanded","false"),e.removeClass("auto-position"),e[0].style.removeProperty("top"),e[0].style.removeProperty("left"),e[0].style.removeProperty("right"),e[0].style.removeProperty("bottom"),setTimeout((function(){e.removeClass("force-hidden")}),150),this.activeDropdown&&this.activeDropdown[0]===e[0]&&(this.activeDropdown=null),console.log("🔽 隐藏下拉菜单")}},{key:"closeAllDropdowns",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;$(".dropdown-menu.show").each((function(n,o){var i=$(o);if(!t||i[0]!==t[0]){var r=i.prev('[data-toggle="dropdown"]');e.hideDropdown(i,r)}}))}},{key:"adjustPosition",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;t||(t=e.prev('[data-toggle="dropdown"]'));var n=e[0].getBoundingClientRect(),o=t[0].getBoundingClientRect(),i=window.innerWidth,r=window.innerHeight;if(n.right>i||n.bottom>r||n.left<0||n.top<0){console.log("🔧 调整下拉菜单位置"),e.addClass("auto-position");var a=o.bottom+2,c=o.right-160;a+200>r&&(a=o.top-200),c<0&&(c=0),c+160>i&&(c=i-160),e[0].style.setProperty("top",Math.max(0,a)+"px","important"),e[0].style.setProperty("left",Math.max(0,c)+"px","important"),e[0].style.setProperty("right","auto","important"),e[0].style.setProperty("bottom","auto","important")}}},{key:"handleOutsideClick",value:function(e){var t=$(e.target),n=t.closest('[data-toggle="dropdown"]').length>0,o=t.closest(".dropdown-menu").length>0,i=t.closest(".btn-group").length>0;n||o||i||this.closeAllDropdowns()}},{key:"destroy",value:function(){$(document).off(".universal-dropdown"),$(document).off(".universal-dropdown-close"),$(window).off(".universal-dropdown"),this.isInitialized=!1,this.activeDropdown=null,console.log("🔽 通用下拉菜单组件已销毁")}}],t&&pe(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}(),me=new ye;window.UniversalDropdown=me;var ge=me;function be(e){return be="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},be(e)}function we(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,r,a,c=[],l=!0,s=!1;try{if(r=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(o=r.call(n)).done)&&(c.push(o.value),c.length!==t);l=!0);}catch(e){s=!0,i=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(e,t)||Se(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ke(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=Se(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var o=0,i=function(){};return{s:i,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,a=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){c=!0,r=e},f:function(){try{a||null==n.return||n.return()}finally{if(c)throw r}}}}function Se(e,t){if(e){if("string"==typeof e)return Ee(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ee(e,t):void 0}}function Ee(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function Ae(){var e,t,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.toStringTag||"@@toStringTag";function r(n,o,i,r){var l=o&&o.prototype instanceof c?o:c,s=Object.create(l.prototype);return Ce(s,"_invoke",function(n,o,i){var r,c,l,s=0,u=i||[],d=!1,f={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return r=t,c=0,l=e,f.n=n,a}};function v(n,o){for(c=n,l=o,t=0;!d&&s&&!i&&t<u.length;t++){var i,r=u[t],v=f.p,p=r[2];n>3?(i=p===o)&&(c=r[4]||3,l=r[5]===e?r[3]:r[5],r[4]=3,r[5]=e):r[0]<=v&&((i=n<2&&v<r[1])?(c=0,f.v=o,f.n=r[1]):v<p&&(i=n<3||r[0]>o||o>p)&&(r[4]=n,r[5]=o,f.n=p,c=0))}if(i||n>1)return a;throw d=!0,o}return function(i,u,p){if(s>1)throw TypeError("Generator is already running");for(d&&1===u&&v(u,p),c=u,l=p;(t=c<2?e:l)||!d;){r||(c?c<3?(c>1&&(f.n=-1),v(c,l)):f.n=l:f.v=l);try{if(s=2,r){if(c||(i="next"),t=r[i]){if(!(t=t.call(r,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,c<2&&(c=0)}else 1===c&&(t=r.return)&&t.call(r),c<2&&(l=TypeError("The iterator does not provide a '"+i+"' method"),c=1);r=e}else if((t=(d=f.n<0)?l:n.call(o,f))!==a)break}catch(t){r=e,c=1,l=t}finally{s=1}}return{value:t,done:d}}}(n,i,r),!0),s}var a={};function c(){}function l(){}function s(){}t=Object.getPrototypeOf;var u=[][o]?t(t([][o]())):(Ce(t={},o,(function(){return this})),t),d=s.prototype=c.prototype=Object.create(u);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,Ce(e,i,"GeneratorFunction")),e.prototype=Object.create(d),e}return l.prototype=s,Ce(d,"constructor",s),Ce(s,"constructor",l),l.displayName="GeneratorFunction",Ce(s,i,"GeneratorFunction"),Ce(d),Ce(d,i,"Generator"),Ce(d,o,(function(){return this})),Ce(d,"toString",(function(){return"[object Generator]"})),(Ae=function(){return{w:r,m:f}})()}function Ce(e,t,n,o){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}Ce=function(e,t,n,o){if(t)i?i(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{function r(t,n){Ce(e,t,(function(e){return this._invoke(t,n,e)}))}r("next",0),r("throw",1),r("return",2)}},Ce(e,t,n,o)}function Pe(e,t,n,o,i,r,a){try{var c=e[r](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(o,i)}function Te(e){return function(){var t=this,n=arguments;return new Promise((function(o,i){var r=e.apply(t,n);function a(e){Pe(r,o,i,a,c,"next",e)}function c(e){Pe(r,o,i,a,c,"throw",e)}a(void 0)}))}}function Le(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Fe(o.key),o)}}function Fe(e){var t=function(e){if("object"!=be(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=be(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==be(t)?t:t+""}var xe=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.loadingMode="ajax",this.menuState={},this.isLoading=!1,this.pageInitializers={},this.init=this.init.bind(this),this.setupMenuLinks=this.setupMenuLinks.bind(this),this.loadContent=this.loadContent.bind(this),this.loadContentAjax=this.loadContentAjax.bind(this),this.loadContentIframe=this.loadContentIframe.bind(this),this.saveMenuState=this.saveMenuState.bind(this),this.restoreMenuState=this.restoreMenuState.bind(this),this.registerPageInitializer=this.registerPageInitializer.bind(this),this.runPageInitializers=this.runPageInitializers.bind(this)},t=[{key:"init",value:function(){var e=this;this.isInitialized||(console.log("🚀 开始初始化统一AJAX菜单模块..."),(0,r.Qc)((function(){console.log("📋 DOM已就绪，开始设置AJAX菜单..."),e.setupMenuLinks(),e.bindEvents(),e.initMenuState(),e.isInitialized=!0;var t=window.location.pathname;console.log("🎯 初始化当前页面功能:",t),e.reinitializePageFeatures(t),console.log("✅ 统一AJAX菜单模块初始化完成"),console.log("📊 当前模块状态:",{isInitialized:e.isInitialized,loadingMode:e.loadingMode,isLoading:e.isLoading})})))}},{key:"setupMenuLinks",value:function(){console.log("🔗 开始设置菜单链接...");var e=document.querySelectorAll('.sidebar a[href]:not([data-toggle="collapse"])');console.log("📝 找到 ".concat(e.length," 个菜单链接"));var t=0,n=0;e.forEach((function(e,o){var i=e.getAttribute("href"),r=e.textContent.trim();console.log("🔍 处理链接 ".concat(o+1,":"),{href:i,linkText:r}),!i||"#"===i||i.startsWith("javascript:")||i.startsWith("mailto:")||i.startsWith("tel:")?console.log("⏭️ 跳过特殊链接: ".concat(i)):"false"!==e.getAttribute("data-ajax-load")?["admin.menu_management","admin.permission_matrix","admin.category_permission_matrix","admin.unified_permission_matrix","admin.permission_management","auth.logout","auth.switch_role","auth.select_role"].some((function(e){return i.includes(e.replace(".","/"))}))?(e.setAttribute("data-ajax-load","false"),n++,console.log("❌ 禁用AJAX: ".concat(i," (").concat(r,")"))):(e.setAttribute("data-ajax-load","true"),t++,console.log("✅ 启用AJAX: ".concat(i," (").concat(r,")"))):(n++,console.log("🚫 已禁用AJAX: ".concat(i," (").concat(r,")")))})),console.log("📊 菜单链接设置完成: 启用AJAX ".concat(t," 个, 禁用AJAX ").concat(n," 个"))}},{key:"bindEvents",value:function(){var e=this;console.log("🎯 绑定菜单点击事件..."),document.addEventListener("click",(function(t){console.log("🖱️ 检测到点击事件:",t.target);var n=t.target.closest(".sidebar a[href]");if(n){var o=n.getAttribute("data-toggle"),i=n.getAttribute("href");"collapse"!==o||i&&"#"!==i?("collapse"===o&&i&&"#"!==i&&console.log("🎯 一级菜单管理页面链接，需要特殊处理AJAX加载"),!i||"#"===i||i.startsWith("javascript:")||i.startsWith("mailto:")||i.startsWith("tel:")?console.log("⏭️ 特殊链接，使用默认行为:",i):i.startsWith("/")?(console.log("🎯 捕获到菜单链接点击:",i),e.handleMenuClick(t,n)):console.log("⏭️ 不是URL链接，忽略:",i)):console.log("🎯 纯手风琴链接，让Bootstrap完全处理，不捕获事件")}else console.log("⏭️ 不是菜单链接，忽略")})),window.addEventListener("popstate",(function(t){t.state&&t.state.url&&(console.log("浏览器历史导航:",t.state.url),e.loadContent(t.state.url,!1),e.highlightMenuLink(t.state.url))})),this.initializeMenuStates()}},{key:"initializeMenuStates",value:function(){console.log("🔧 初始化菜单状态，预防滚动条闪烁..."),document.querySelectorAll(".sidebar .collapse").forEach((function(e){var t=e.classList.contains("in")||e.classList.contains("show");if(console.log("📋 检查菜单: ".concat(e.id,", 当前状态: ").concat(t?"展开":"折叠")),!t){var n=document.querySelector('[data-target="#'.concat(e.id,'"]'));n&&n.setAttribute("aria-expanded","false")}})),console.log("✅ 菜单状态检查完成")}},{key:"addGlobalMethodInterception",value:function(){console.log("🔍 添加全局方法调用拦截...");var e=this.loadContent.bind(this);if(this.loadContent=function(t){var n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return console.log("🔍 [拦截] UnifiedAjaxMenu.loadContent 调用:",{url:t,updateHistory:n,stack:(new Error).stack.split("\n").slice(2,12)}),e(t,n)},window.ajaxLoader&&window.ajaxLoader.loadContent){var t=window.ajaxLoader.loadContent.bind(window.ajaxLoader);window.ajaxLoader.loadContent=function(e){var n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return console.log("🔍 [拦截] AjaxLoader.loadContent 调用:",{url:e,updateHistory:n,stack:(new Error).stack.split("\n").slice(2,12)}),t(e,n)}}document.addEventListener("click",(function(e){var t=e.target,n=t.closest("a");n&&"#"===n.getAttribute("href")&&console.log("🔍 [拦截] 点击 # 链接:",{target:t.tagName+(t.className?"."+t.className:""),link:n.outerHTML.substring(0,100),stack:(new Error).stack.split("\n").slice(1,8)})}),!0)}},{key:"addDetailedDebugListeners",value:function(){console.log("🔍 启用详细调试模式，追踪所有菜单相关事件..."),document.addEventListener("click",(function(e){var t=e.target,n=t.closest("a");n&&n.closest(".sidebar")&&console.log("🖱️ [捕获阶段] 侧边栏点击:",{target:t.tagName+(t.className?"."+t.className:""),link:n.outerHTML.substring(0,100)+"...",href:n.getAttribute("href"),dataToggle:n.getAttribute("data-toggle"),dataTarget:n.getAttribute("data-target"),defaultPrevented:e.defaultPrevented,propagationStopped:e.cancelBubble})}),!0),document.addEventListener("click",(function(e){var t=e.target,n=t.closest("a");n&&n.closest(".sidebar")&&console.log("🖱️ [冒泡阶段] 侧边栏点击:",{target:t.tagName+(t.className?"."+t.className:""),href:n.getAttribute("href"),defaultPrevented:e.defaultPrevented,propagationStopped:e.cancelBubble})}),!1),document.querySelectorAll(".collapse").forEach((function(e){var t=e.id;["show.bs.collapse","shown.bs.collapse","hide.bs.collapse","hidden.bs.collapse"].forEach((function(n){e.addEventListener(n,(function(e){console.log("🎯 Bootstrap事件 [".concat(n,"]:"),{menuId:t,target:e.target.id,currentTarget:e.currentTarget.id,defaultPrevented:e.defaultPrevented,timeStamp:e.timeStamp}),console.trace("Bootstrap事件调用栈 [".concat(n,"]:"))}))}))})),window.$&&window.$.fn.on&&window.$(document).on("show.bs.collapse hide.bs.collapse",".collapse",(function(e){console.log("🎯 jQuery Bootstrap事件:",{type:e.type,target:e.target.id,namespace:e.namespace,timeStamp:e.timeStamp})})),console.log("🔍 Bootstrap检查:",{jQueryLoaded:void 0!==window.$,jQueryVersion:window.$?window.$.fn.jquery:"N/A",bootstrapCollapse:window.$&&window.$.fn.collapse?"Available":"Not Available",bootstrapVersion:window.$&&window.$.fn.tooltip&&window.$.fn.tooltip.Constructor?window.$.fn.tooltip.Constructor.VERSION:"Unknown"}),document.querySelectorAll('.sidebar a[data-toggle="collapse"]').forEach((function(e,t){var n=e.getAttribute("data-target"),o=document.querySelector(n);console.log("🔍 菜单结构检查 ".concat(t+1,":"),{trigger:e.textContent.trim(),href:e.getAttribute("href"),dataTarget:n,targetExists:!!o,targetClasses:o?o.className:"N/A",ariaExpanded:e.getAttribute("aria-expanded")})}));var e=window.fetch;window.fetch=function(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];var i=n[0];return"string"!=typeof i||!i.startsWith("/admin/")&&"#"!==i||(console.log("🌐 AJAX请求拦截:",{url:i,method:n[1]?n[1].method:"GET",caller:"fetch",stack:(new Error).stack.split("\n").slice(1,8)}),console.log("🔍 完整调用栈:",(new Error).stack),"#"!==i)?e.apply(this,n):(console.log("🚫 阻止对 # 的AJAX请求"),Promise.resolve(new Response("",{status:200})))};var t=XMLHttpRequest.prototype.open;XMLHttpRequest.prototype.open=function(e,n){"string"==typeof n&&n.startsWith("/admin/")&&console.log("🌐 XHR请求拦截:",{method:e,url:n,caller:"XMLHttpRequest",stack:(new Error).stack.split("\n").slice(1,4)});for(var o=arguments.length,i=new Array(o>2?o-2:0),r=2;r<o;r++)i[r-2]=arguments[r];return t.apply(this,[e,n].concat(i))}}},{key:"addDebugListeners",value:function(){console.log("🔍 添加调试监听器，监控菜单状态变化..."),document.querySelectorAll(".collapse").forEach((function(e){var t=e.id;e.addEventListener("show.bs.collapse",(function(e){console.log("🟢 Bootstrap事件 - 菜单即将展开:",t,e)})),e.addEventListener("shown.bs.collapse",(function(e){console.log("✅ Bootstrap事件 - 菜单已展开:",t,e)})),e.addEventListener("hide.bs.collapse",(function(e){console.log("🔴 Bootstrap事件 - 菜单即将关闭:",t,e),console.trace("菜单关闭调用栈:")})),e.addEventListener("hidden.bs.collapse",(function(e){console.log("❌ Bootstrap事件 - 菜单已关闭:",t,e)}))}));var e=new MutationObserver((function(e){e.forEach((function(e){if("attributes"===e.type&&"class"===e.attributeName){var t=e.target;if(t.classList.contains("collapse")){var n=t.id,o=t.classList.contains("show");if(console.log("🔄 DOM变化 - 菜单类变化:",n,"show类:",o),console.log("   旧值:",e.oldValue),console.log("   新值:",t.className),o)console.log("✅ 菜单有show类，处于展开状态");else{console.log("⚠️ 检测到菜单没有show类:",n),e.oldValue&&e.oldValue.includes("show")?(console.log("🚨 菜单从展开变为关闭！"),console.trace("菜单关闭DOM变化调用栈:")):(console.log("🤔 菜单本来就没有show类，或者旧值为空"),console.log("   旧值详情:",JSON.stringify(e.oldValue)));var i=(new Error).stack;(i.includes("enhanced-menu-manager")||i.includes("unified-ajax-menu"))&&console.log("🚨 可能是我们的代码造成的菜单状态变化！")}}}}))}));document.querySelectorAll(".collapse").forEach((function(t){e.observe(t,{attributes:!0,attributeOldValue:!0,attributeFilter:["class"]})})),document.addEventListener("click",(function(e){var t=e.target;t.closest(".sidebar")&&console.log("🖱️ 侧边栏点击事件:",{target:t,tagName:t.tagName,className:t.className,href:t.getAttribute?t.getAttribute("href"):null,dataToggle:t.getAttribute?t.getAttribute("data-toggle"):null,dataTarget:t.getAttribute?t.getAttribute("data-target"):null})}),!0)}},{key:"handleMenuClick",value:function(e,t){var n=t.getAttribute("href"),o=t.getAttribute("data-ajax-load"),i=t.getAttribute("data-toggle"),r=t.getAttribute("data-target");if(console.log("菜单点击事件:",{href:n,ajaxLoadAttr:o,dataToggle:i,dataTarget:r,linkText:t.textContent.trim()}),"false"!==o)if("collapse"!==i||n&&"#"!==n){if("collapse"===i&&n&&"#"!==n)return console.log("🎯 一级菜单点击，只处理手风琴效果，不加载右侧内容"),e.preventDefault(),void console.log("✅ 一级菜单手风琴处理完成，右侧内容保持不变");e.preventDefault(),console.log("阻止默认行为，使用AJAX加载:",n),this.isLoading?console.log("正在加载中，忽略重复点击"):(this.highlightMenuLink(n,t),this.loadContent(n))}else console.log("🎯 纯手风琴链接，让Bootstrap完全处理，不干预");else console.log("使用正常页面跳转:",n)}},{key:"loadContent",value:(a=Te(Ae().m((function e(t){var n,o,i=arguments;return Ae().w((function(e){for(;;)switch(e.n){case 0:if(n=!(i.length>1&&void 0!==i[1])||i[1],console.log("🔍 UnifiedAjaxMenu.loadContent 被调用:",{url:t,updateHistory:n,caller:"UnifiedAjaxMenu.loadContent",stack:(new Error).stack.split("\n").slice(1,10)}),"#"!==t){e.n=1;break}return console.log("🚫 UnifiedAjaxMenu: 跳过 # 链接，不处理"),e.a(2);case 1:if(!this.isLoading){e.n=2;break}return e.a(2);case 2:if(this.isLoading=!0,e.p=3,"iframe"!==this.loadingMode){e.n=5;break}return e.n=4,this.loadContentIframe(t);case 4:e.n=6;break;case 5:return e.n=6,this.loadContentAjax(t);case 6:n&&this.updateHistory(t),e.n=8;break;case 7:e.p=7,o=e.v,console.error("内容加载失败:",o),window.location.href=t;case 8:return e.p=8,this.isLoading=!1,e.f(8);case 9:return e.a(2)}}),e,this,[[3,7,8,9]])}))),function(e){return a.apply(this,arguments)})},{key:"loadContentAjax",value:(i=Te(Ae().m((function e(t){var n,i,r,a;return Ae().w((function(e){for(;;)switch(e.n){case 0:return console.log("开始AJAX加载内容:",t),this.showLoader(),e.p=1,e.n=2,fetch(t,{method:"GET",headers:{"X-Requested-With":"XMLHttpRequest","X-CSRFToken":o.A.getCsrfToken()},credentials:"same-origin"});case 2:if((n=e.v).ok){e.n=3;break}throw new Error("HTTP error! Status: ".concat(n.status));case 3:return e.n=4,n.text();case 4:i=e.v,console.log("🔍 AJAX响应内容长度:",i.length),"/"===t&&(console.log("🏠 首页内容预览:",i.substring(0,500)),r=i.includes("广东省实验室安全检查系统")||i.includes("安全是我们的责任"),a=i.includes("统计卡片")||i.includes("card"),console.log("🔍 首页内容检查:",{hasWelcomeBanner:r,hasStatsCards:a})),this.updatePageContent(i,t),console.log("AJAX内容加载成功");case 5:return e.p=5,this.hideLoader(),e.f(5);case 6:return e.a(2)}}),e,this,[[1,,5,6]])}))),function(e){return i.apply(this,arguments)})},{key:"loadContentIframe",value:(n=Te(Ae().m((function e(t){var n,o=this;return Ae().w((function(e){for(;;)if(0===e.n)return console.log("开始iframe加载内容:",t),(n=document.getElementById("content-iframe"))||(n=this.createContentIframe()),this.showLoader(),e.a(2,new Promise((function(e,i){n.src=t,n.onload=function(){o.hideLoader();try{var t=n.contentDocument.title;t&&(document.title=t)}catch(e){console.warn("无法访问iframe内容:",e)}console.log("iframe内容加载成功"),e()},n.onerror=function(){o.hideLoader(),i(new Error("iframe加载失败"))}})))}),e,this)}))),function(e){return n.apply(this,arguments)})},{key:"createContentIframe",value:function(){var e=document.getElementById("content-block")||document.querySelector(".main-content")||document.querySelector(".content-wrapper");if(!e)throw new Error("找不到内容区域");e.innerHTML,e.innerHTML="";var t=document.createElement("iframe");return t.id="content-iframe",t.name="content-iframe",t.style.cssText="\n      width: 100%;\n      height: 100vh;\n      border: none;\n      overflow: auto;\n    ",e.appendChild(t),console.log("创建内容iframe成功"),t}},{key:"updatePageContent",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;console.log("🔄 开始更新页面内容...");var o=(new DOMParser).parseFromString(e,"text/html"),i=o.querySelector("title");i&&(document.title=i.textContent,console.log("📝 页面标题已更新:",i.textContent));var r=["#content-block",".main-content",".content-wrapper"],a=null,c=null;console.log("🔍 查找内容区域...");for(var l=0,s=r;l<s.length;l++){var u=s[l];if(a=o.querySelector(u),c=document.querySelector(u),console.log("检查选择器 ".concat(u,": 新内容=").concat(!!a,", 当前内容=").concat(!!c)),a&&c){if(console.log("✅ 找到匹配的内容区域: ".concat(u)),console.log("📏 新内容长度: ".concat(a.innerHTML.length)),console.log("📏 当前内容长度: ".concat(c.innerHTML.length)),"/"===n){var d=a.innerHTML,f=d.includes("广东省实验室安全检查系统")||d.includes("安全是我们的责任")||d.includes("管理员控制面板"),v=d.includes("统计")||d.includes("card")||d.includes("实验室总数量"),p=d.includes("page-header-section")||d.includes("current-time");console.log("🏠 首页新内容检查: 欢迎横幅=".concat(f,", 统计卡片=").concat(v,", 页面标题=").concat(p)),console.log("🔍 详细DOM结构检查:"),console.log("  - .page-header-section 存在:",!!a.querySelector(".page-header-section")),console.log("  - .container-fluid 存在:",!!a.querySelector(".container-fluid")),console.log("  - .improved-stat-card 数量:",a.querySelectorAll(".improved-stat-card").length);var h=a.firstElementChild;if(console.log("  - 第一个子元素:",h?h.tagName+"."+h.className:"null"),!p){console.warn("⚠️ 首页新内容缺少页面标题区域！"),console.log("🔍 新内容前1000字符:",d.substring(0,1e3));var y=o.querySelector(".page-header-section");if(y){console.log("✅ 在完整文档中找到页面标题区域，尝试修复..."),console.log("📋 页面标题区域内容:",y.outerHTML.substring(0,200));var m=a.querySelector(".container-fluid");if(m){var g=m.querySelector(".row");if(g){var b=document.createElement("div");b.className="row";var w=document.createElement("div");w.className="col-md-12",w.appendChild(y.cloneNode(!0)),b.appendChild(w),m.insertBefore(b,g),console.log("🔧 已修复首页内容，添加页面标题区域")}else m.insertBefore(y.cloneNode(!0),m.firstChild),console.log("🔧 已修复首页内容，直接添加页面标题区域")}}else console.error("❌ 在完整文档中未找到页面标题区域")}}break}}if(!a||!c)throw console.error("❌ 无法找到内容区域"),console.log("可用的新内容选择器:",r.map((function(e){return"".concat(e,": ").concat(!!o.querySelector(e))}))),console.log("可用的当前内容选择器:",r.map((function(e){return"".concat(e,": ").concat(!!document.querySelector(e))}))),new Error("无法找到内容区域");console.log("🔄 开始替换内容..."),c.style.opacity="0.95",requestAnimationFrame((function(){if(c.innerHTML=a.innerHTML,c.style.opacity="1",console.log("✅ 内容替换完成"),"/"===n){console.log("🔍 替换后验证首页DOM结构:");var e=c.querySelector(".page-header-section"),o=c.querySelector(".container-fluid"),i=c.querySelectorAll(".improved-stat-card");if(console.log("  - 替换后 .page-header-section 存在:",!!e),console.log("  - 替换后 .container-fluid 存在:",!!o),console.log("  - 替换后 .improved-stat-card 数量:",i.length),e){var r=window.getComputedStyle(e);console.log("  - 页面标题区域可见性:",r.display),console.log("  - 页面标题区域高度:",e.offsetHeight),console.log("  - 页面标题区域内容预览:",e.textContent.substring(0,100)),console.log("  - 背景色:",r.backgroundColor),console.log("  - 文字颜色:",r.color),console.log("  - z-index:",r.zIndex),console.log("  - position:",r.position),console.log("  - top:",r.top),console.log("  - left:",r.left),console.log("  - 边距:",r.margin),console.log("  - 内边距:",r.padding);var l=e.getBoundingClientRect();console.log("  - 位置信息:",{top:l.top,left:l.left,width:l.width,height:l.height,bottom:l.bottom,right:l.right}),console.log("✅ 首页CSS样式已全局化，无需动态注入")}else console.error("❌ 替换后页面标题区域丢失！"),console.log("🔍 当前内容前500字符:",c.innerHTML.substring(0,500))}t.executeScripts(c),t.reinitializePageFeatures(n||window.location.pathname),document.dispatchEvent(new CustomEvent("ajaxContentLoaded",{detail:{url:n||window.location.pathname}})),console.log("✅ 页面内容更新成功")}))}},{key:"injectHomePageCSS",value:function(){if(document.getElementById("homepage-dynamic-css"))console.log("首页CSS已存在，跳过注入");else{var e=document.createElement("style");e.id="homepage-dynamic-css",e.textContent="\n      /* 页面标题区域 - 保持蓝色主题 */\n      .page-header-section {\n        background: #2e6da4 !important;\n        color: white !important;\n        padding: 20px 25px !important;\n        border-radius: 4px !important;\n        margin-bottom: 20px !important;\n        box-shadow: 0 2px 4px rgba(46, 109, 164, 0.2) !important;\n      }\n\n      .page-header-title {\n        font-size: 22px !important;\n        font-weight: 500 !important;\n        margin-bottom: 5px !important;\n        display: flex !important;\n        justify-content: space-between !important;\n        align-items: center !important;\n      }\n\n      .page-header-subtitle {\n        font-size: 14px !important;\n        opacity: 0.9 !important;\n        margin-bottom: 0 !important;\n      }\n\n      .current-time {\n        font-size: 13px !important;\n        opacity: 0.9 !important;\n        background: rgba(255, 255, 255, 0.15) !important;\n        padding: 5px 10px !important;\n        border-radius: 3px !important;\n      }\n\n      /* 实时状态指示器 */\n      .status-indicators {\n        display: flex !important;\n        gap: 15px !important;\n        margin-top: 10px !important;\n      }\n\n      .status-item {\n        display: flex !important;\n        align-items: center !important;\n        font-size: 12px !important;\n        opacity: 0.9 !important;\n      }\n\n      .status-dot {\n        width: 8px !important;\n        height: 8px !important;\n        border-radius: 50% !important;\n        margin-right: 5px !important;\n      }\n\n      .status-online {\n        background: #5cb85c !important;\n      }\n\n      .status-warning {\n        background: #f0ad4e !important;\n      }\n\n      .status-error {\n        background: #d9534f !important;\n      }\n\n      /* 改进数据卡片样式 */\n      .improved-stat-card {\n        background: white !important;\n        border-radius: 4px !important;\n        padding: 20px !important;\n        margin-bottom: 20px !important;\n        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24) !important;\n        transition: all 0.3s ease !important;\n        position: relative !important;\n        overflow: hidden !important;\n      }\n\n      .improved-stat-card:hover {\n        transform: translateY(-2px) !important;\n        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23) !important;\n      }\n    ",document.head.appendChild(e),console.log("✅ 首页CSS样式已动态注入")}}},{key:"executeScripts",value:function(e){e.querySelectorAll("script").forEach((function(e){var t=document.createElement("script");Array.from(e.attributes).forEach((function(e){t.setAttribute(e.name,e.value)})),t.textContent=e.textContent,e.parentNode.replaceChild(t,e)}))}},{key:"reinitializePageFeatures",value:function(e){var t=this;console.log("🔄 重新初始化页面功能:",e),setTimeout((function(){t.runPageInitializers(e),"/"===e||e.includes("/admin/index")?t.initializeHomePage():e.includes("/admin/laboratories")?t.initializeLaboratoriesPage():e.includes("/admin/inspection_activities")?t.initializeInspectionActivitiesPage():e.includes("/admin/users")?t.initializeUsersPage():e.includes("/admin/statistics/by_indicator")?t.initializeStatisticsByIndicatorPage():e.includes("/admin/inspection_check")?t.initializeInspectionCheckPage():e.includes("/admin/special_inspection_check")?t.initializeSpecialInspectionCheckPage():e.includes("/admin/issue_review")?t.initializeIssueReviewPage():e.includes("/admin/hazards")&&e.includes("/detail")?t.initializeHazardDetailPage():e.includes("/admin/hazard_rectification")?t.initializeHazardRectificationPage():e.includes("/admin/announcements")?t.initializeAnnouncementsPage():e.includes("/admin/menu_management")?t.initializeMenuManagementPage():e.includes("/admin/inspection_templates")?t.initializeInspectionTemplatesPage():e.includes("/admin/dashboard")?t.initializeDashboardPage():e.includes("/admin/buildings")?t.initializeBuildingsPage():e.includes("/admin/campuses")?t.initializeCampusesPage():e.includes("/admin/departments")?t.initializeDepartmentsPage():e.includes("/admin/user_form")||e.includes("/admin/users/add")||e.includes("/admin/users/edit")?t.initializeUserFormPage():e.includes("/admin/laboratory_form")||e.includes("/admin/laboratories/add")||e.includes("/admin/laboratories/edit")?t.initializeLaboratoryFormPage():e.includes("/admin/system_settings")?t.initializeSystemSettingsPage():e.includes("/admin/backup")?t.initializeBackupPage():e.includes("/admin/data_edit")?t.initializeDataEditPage():e.includes("/admin/user_roles")?t.initializeUserRolesPage():e.includes("/admin/lab_types")?t.initializeLabTypesPage():e.includes("/admin/lab_properties")?t.initializeLabPropertiesPage():e.includes("/admin/risk_levels")?t.initializeRiskLevelsPage():e.includes("/admin/templates")?t.initializeTemplatesPage():e.includes("/admin/safety_ledgers")?t.initializeSafetyLedgersPage():e.includes("/admin/inspection_activity_form")||e.includes("/admin/inspection_activities/add")||e.includes("/admin/inspection_activities/edit")?t.initializeInspectionActivityFormPage():e.includes("/admin/inspection_clauses")?t.initializeInspectionClausesPage():e.includes("/admin/inspection_records")?t.initializeInspectionRecordsPage():e.includes("/admin/hazard_drafts")?t.initializeHazardDraftsPage():e.includes("/admin/hazard_report")?t.initializeHazardReportPage():e.includes("/admin/hazard_review")?t.initializeHazardReviewPage():e.includes("/admin/reports_management")&&t.initializeReportsManagementPage(),t.initializeCommonFeatures()}),100)}},{key:"initializeHomePage",value:function(){console.log("🏠 初始化首页功能"),window.HomePage&&window.HomePage.initCharts&&(console.log("🏠 调用首页模块的图表初始化"),window.HomePage.initCharts()),this.initializeHomeCharts(),this.initializeStatisticsCards(),this.initializeOnlineUsers(),this.initializeQuickActions(),this.initializeHomePageSpecific(),this.defineHomePageGlobalFunctions(),console.log("✅ 首页功能初始化完成")}},{key:"initializeHomeCharts",value:function(){if(window.Chart&&window.chartManager){console.log("📊 重新初始化图表");try{window.chartManager.reinitialize&&window.chartManager.reinitialize()}catch(e){console.warn("图表初始化失败:",e)}}}},{key:"initializeStatisticsCards",value:function(){console.log("📈 重新初始化统计卡片"),document.querySelectorAll(".stats-card, .panel").forEach((function(e){var t=e.querySelector("a");t&&t.href&&(e.style.cursor="pointer",e.addEventListener("click",(function(e){"A"!==e.target.tagName&&(window.location.href=t.href)})))}))}},{key:"initializeOnlineUsers",value:function(){if(console.log("👥 重新初始化在线用户模块"),window.onlineUsers&&window.onlineUsers.updateCount)try{window.onlineUsers.updateCount()}catch(e){console.warn("在线用户模块初始化失败:",e)}var e=document.querySelector('a[href*="online"]');e&&e.addEventListener("click",(function(e){e.preventDefault(),window.onlineUsers&&window.onlineUsers.showUserList&&window.onlineUsers.showUserList()}))}},{key:"initializeQuickActions",value:function(){console.log("⚡ 重新初始化快捷操作"),document.querySelectorAll(".quick-action-btn, .btn-quick").forEach((function(e){e.addEventListener("click",(function(e){var t=this.getAttribute("href")||this.getAttribute("data-href");t&&!t.startsWith("#")&&(e.preventDefault(),window.location.href=t)}))}))}},{key:"initializeHomePageSpecific",value:function(){console.log("🏡 重新初始化首页特有功能"),this.initializeRealTimeClock(),this.initializeOnlineUsersClick(),this.initializeAnnouncementToggle(),this.initializeStatCardHover()}},{key:"initializeRealTimeClock",value:function(){var e=document.getElementById("current-time");if(e){window.homePageTimeInterval&&clearInterval(window.homePageTimeInterval);var t=function(){var t=(new Date).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",weekday:"short"});e.textContent=t};t(),window.homePageTimeInterval=setInterval(t,1e3)}}},{key:"initializeOnlineUsersClick",value:function(){var e=document.getElementById("onlineUsersLink");if(e){e.replaceWith(e.cloneNode(!0)),document.getElementById("onlineUsersLink").addEventListener("click",(function(e){e.preventDefault(),console.log("点击了在线用户链接"),window.loadOnlineUsers&&window.loadOnlineUsers(),window.$&&window.$("#onlineUsersModal").length&&window.$("#onlineUsersModal").modal("show")}));var t=document.getElementById("refreshOnlineUsers");t&&(t.replaceWith(t.cloneNode(!0)),document.getElementById("refreshOnlineUsers").addEventListener("click",(function(){console.log("点击了刷新按钮"),window.loadOnlineUsers&&window.loadOnlineUsers()})))}}},{key:"initializeAnnouncementToggle",value:function(){var e=document.querySelectorAll(".announcement");e.length&&e.forEach((function(e){var t=e.cloneNode(!0);e.parentNode.replaceChild(t,e),t.addEventListener("click",(function(){var e=this.querySelector(".fa-chevron-down, .fa-chevron-up");this.classList.contains("expanded")?(this.classList.remove("expanded"),e&&(e.classList.remove("fa-chevron-up"),e.classList.add("fa-chevron-down"))):(this.classList.add("expanded"),e&&(e.classList.remove("fa-chevron-down"),e.classList.add("fa-chevron-up")))})),t.querySelectorAll(".announcement-content a").forEach((function(e){e.addEventListener("click",(function(e){e.stopPropagation()}))}))}))}},{key:"initializeStatCardHover",value:function(){document.querySelectorAll(".improved-stat-card, .stat-card").forEach((function(e){var t=e.cloneNode(!0);e.parentNode.replaceChild(t,e),t.addEventListener("mouseenter",(function(){this.style.transform="translateY(-2px)",this.style.boxShadow="0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)"})),t.addEventListener("mouseleave",(function(){this.style.transform="translateY(0)",this.style.boxShadow="0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)"}))}))}},{key:"defineHomePageGlobalFunctions",value:function(){void 0===window.checkAndReloadData&&(window.checkAndReloadData=function(){console.log("📋 checkAndReloadData函数被调用（兼容性函数）")}),void 0===window.refreshDashboard&&(window.refreshDashboard=function(){console.log("🔄 refreshDashboard函数被调用"),location.reload()}),void 0===window.loadOnlineUsers&&(window.loadOnlineUsers=function(){console.log("开始加载在线用户");var e=document.getElementById("onlineUsersContent");e&&(e.innerHTML='<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 正在加载...</div>'),window.$&&window.$.ajax&&window.$.ajax({url:"/main/api/online_users",type:"GET",success:function(t){console.log("API响应:",t),t.success?window.displayOnlineUsers(t.users,t.count):e&&(e.innerHTML='<div class="alert alert-danger">加载失败：'+t.message+"</div>")},error:function(t){console.error("AJAX错误:",t),e&&(e.innerHTML='<div class="alert alert-danger">加载失败，请重试</div>')}})}),void 0===window.displayOnlineUsers&&(window.displayOnlineUsers=function(e,t){console.log("显示在线用户:",e,t);var n=document.getElementById("onlineUsersContent");if(n){var o="";0===t?o='<div class="alert alert-info"><i class="fas fa-info-circle"></i> 当前没有在线用户</div>':(o+='<div class="alert alert-success">',o+='<i class="fas fa-users"></i> 当前共有 <strong>'+t+"</strong> 个用户在线",o+="</div>",o+='<div class="table-responsive">',o+='<table class="table table-striped table-hover">',o+="<thead><tr>",o+="<th>用户名</th><th>IP地址</th><th>登录时间</th><th>最后活动</th><th>在线时长</th>",o+="</tr></thead><tbody>",e.forEach((function(e){o+="<tr>",o+='<td><i class="fas fa-user text-primary"></i> '+e.username+"</td>",o+='<td><i class="fas fa-globe text-muted"></i> '+(e.ip_address||"未知")+"</td>",o+='<td><i class="fas fa-clock text-success"></i> '+window.formatDateTime(e.login_time)+"</td>",o+='<td><i class="fas fa-heartbeat text-info"></i> '+window.formatDateTime(e.last_seen)+"</td>",o+='<td><i class="fas fa-hourglass-half text-warning"></i> '+window.formatDuration(e.online_duration)+"</td>",o+="</tr>"})),o+="</tbody></table></div>"),n.innerHTML=o}}),void 0===window.formatDateTime&&(window.formatDateTime=function(e){return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}),void 0===window.formatDuration&&(window.formatDuration=function(e){var t=e.match(/(\d+):(\d+):(\d+)/);if(t){var n=parseInt(t[1]),o=parseInt(t[2]);return n>0?n+"小时"+o+"分钟":o+"分钟"}return e})}},{key:"initializeLaboratoriesPage",value:function(){console.log("🧪 初始化实验室管理页面功能"),window.LaboratoriesPage&&window.LaboratoriesPage.init&&(console.log("🧪 调用实验室管理页面模块的初始化"),window.LaboratoriesPage.init());var e=document.getElementById("selectAllLabs");e&&(e.addEventListener("change",(function(){var e=this.checked;document.querySelectorAll(".lab-checkbox").forEach((function(t){t.checked=e})),n()})),console.log("✅ 全选复选框事件已重新绑定")),document.querySelectorAll(".lab-checkbox").forEach((function(e){e.addEventListener("change",(function(){n();var e=document.querySelectorAll(".lab-checkbox"),t=document.querySelectorAll(".lab-checkbox:checked"),o=document.getElementById("selectAllLabs");o&&(o.checked=e.length===t.length)}))})),console.log("✅ 单个复选框事件已重新绑定");var t=document.getElementById("generateSafetyCardBtn");function n(){var e=document.querySelectorAll(".lab-checkbox:checked").length,t=document.getElementById("generateSafetyCardBtn");t&&(t.disabled=0===e,t.textContent=e>0?"生成安全信息牌 (".concat(e,")"):"生成安全信息牌")}t&&(t.addEventListener("click",(function(){var e=[];document.querySelectorAll(".lab-checkbox:checked").forEach((function(t){e.push(t.getAttribute("data-id"))})),e.length>0?1===e.length?window.location.href="/admin/laboratory_info_card/".concat(e[0]):window.location.href="/admin/batch_generate_info_card?lab_ids=".concat(e.join(",")):alert("请至少选择一个实验室")})),console.log("✅ 生成按钮事件已重新绑定")),document.querySelectorAll(".sort-icon").forEach((function(e){e.style.cursor="pointer",e.style.userSelect="none",e.addEventListener("click",(function(){var e=this.getAttribute("data-sort");console.log("排序列:",e)}))})),console.log("✅ 表格排序事件已重新绑定"),"function"==typeof window.loadLaboratoriesData&&(console.log("📞 调用全局loadLaboratoriesData函数"),window.loadLaboratoriesData()),n()}},{key:"initializeInspectionActivitiesPage",value:function(){console.log("🔍 初始化检查活动管理页面功能"),window.InspectionActivitiesPage&&window.InspectionActivitiesPage.init?(console.log("🔍 调用检查活动管理页面模块的初始化"),window.InspectionActivitiesPage.init()):console.warn("⚠️ 检查活动管理页面模块未找到或init方法不存在"),console.log("✅ 检查活动管理页面功能初始化完成")}},{key:"initializeUsersPage",value:function(){console.log("👥 初始化用户管理页面功能"),window.UsersPage&&window.UsersPage.init&&(console.log("👥 调用用户管理页面模块的初始化"),window.UsersPage.init()),console.log("✅ 用户管理页面功能初始化完成")}},{key:"initializeStatisticsByIndicatorPage",value:function(){console.log("📊 初始化按指标统计页面功能"),window.StatisticsByIndicatorPage&&window.StatisticsByIndicatorPage.init&&(console.log("📊 调用按指标统计页面模块的初始化"),window.StatisticsByIndicatorPage.init()),console.log("✅ 按指标统计页面功能初始化完成")}},{key:"initializeInspectionCheckPage",value:function(){console.log("🔍 初始化日常巡查检查页面功能"),window.InspectionCheckPage&&window.InspectionCheckPage.init&&(console.log("🔍 调用日常巡查检查页面模块的初始化"),window.InspectionCheckPage.init()),console.log("✅ 日常巡查检查页面功能初始化完成")}},{key:"initializeSpecialInspectionCheckPage",value:function(){console.log("🔍 初始化专项检查页面功能"),window.SpecialInspectionCheckPage&&window.SpecialInspectionCheckPage.init&&(console.log("🔍 调用专项检查页面模块的初始化"),window.SpecialInspectionCheckPage.init()),console.log("✅ 专项检查页面功能初始化完成")}},{key:"initializeIssueReviewPage",value:function(){console.log("📋 初始化隐患审核页面功能"),window.IssueReviewPage&&window.IssueReviewPage.init&&(console.log("📋 调用隐患审核页面模块的初始化"),window.IssueReviewPage.init()),console.log("✅ 隐患审核页面功能初始化完成")}},{key:"initializeHazardDetailPage",value:function(){console.log("📄 初始化隐患详情页面功能"),window.HazardDetailPage&&window.HazardDetailPage.init&&(console.log("📄 调用隐患详情页面模块的初始化"),window.HazardDetailPage.init()),console.log("✅ 隐患详情页面功能初始化完成")}},{key:"initializeHazardRectificationPage",value:function(){console.log("🔧 初始化隐患整改页面功能"),window.HazardRectificationPage&&window.HazardRectificationPage.init&&(console.log("🔧 调用隐患整改页面模块的初始化"),window.HazardRectificationPage.init()),console.log("✅ 隐患整改页面功能初始化完成")}},{key:"initializeAnnouncementsPage",value:function(){console.log("📢 初始化公告管理页面功能"),window.AnnouncementsPage&&window.AnnouncementsPage.init&&(console.log("📢 调用公告管理页面模块的初始化"),window.AnnouncementsPage.init()),console.log("✅ 公告管理页面功能初始化完成")}},{key:"initializeMenuManagementPage",value:function(){console.log("🔧 初始化菜单管理页面功能"),window.MenuManagementPage&&window.MenuManagementPage.init&&(console.log("🔧 调用菜单管理页面模块的初始化"),window.MenuManagementPage.init()),console.log("✅ 菜单管理页面功能初始化完成")}},{key:"initializeInspectionTemplatesPage",value:function(){console.log("📋 初始化检查模板页面功能"),window.InspectionTemplatesPage&&window.InspectionTemplatesPage.init&&(console.log("📋 调用检查模板页面模块的初始化"),window.InspectionTemplatesPage.init()),console.log("✅ 检查模板页面功能初始化完成")}},{key:"initializeDashboardPage",value:function(){console.log("📊 初始化仪表板页面功能"),window.DashboardPage&&window.DashboardPage.init&&(console.log("📊 调用仪表板页面模块的初始化"),window.DashboardPage.init()),console.log("✅ 仪表板页面功能初始化完成")}},{key:"initializeBuildingsPage",value:function(){console.log("🏢 初始化楼宇管理页面功能"),window.BuildingsPage&&window.BuildingsPage.init&&(console.log("🏢 调用楼宇管理页面模块的初始化"),window.BuildingsPage.init()),console.log("✅ 楼宇管理页面功能初始化完成")}},{key:"initializeCampusesPage",value:function(){console.log("🏫 初始化校区管理页面功能"),window.CampusesPage&&window.CampusesPage.init&&(console.log("🏫 调用校区管理页面模块的初始化"),window.CampusesPage.init()),console.log("✅ 校区管理页面功能初始化完成")}},{key:"initializeDepartmentsPage",value:function(){console.log("🏛️ 初始化部门管理页面功能"),window.DepartmentsPage&&window.DepartmentsPage.init&&(console.log("🏛️ 调用部门管理页面模块的初始化"),window.DepartmentsPage.init()),console.log("✅ 部门管理页面功能初始化完成")}},{key:"initializeUserFormPage",value:function(){console.log("👤 初始化用户表单页面功能"),window.UserFormPage&&window.UserFormPage.init&&(console.log("👤 调用用户表单页面模块的初始化"),window.UserFormPage.init()),console.log("✅ 用户表单页面功能初始化完成")}},{key:"initializeLaboratoryFormPage",value:function(){console.log("🧪 初始化实验室表单页面功能"),window.LaboratoryFormPage&&window.LaboratoryFormPage.init&&(console.log("🧪 调用实验室表单页面模块的初始化"),window.LaboratoryFormPage.init()),console.log("✅ 实验室表单页面功能初始化完成")}},{key:"initializeSystemSettingsPage",value:function(){console.log("⚙️ 初始化系统设置页面功能"),window.SystemSettingsPage&&window.SystemSettingsPage.init&&(console.log("⚙️ 调用系统设置页面模块的初始化"),window.SystemSettingsPage.init()),console.log("✅ 系统设置页面功能初始化完成")}},{key:"initializeBackupPage",value:function(){console.log("💾 初始化数据备份页面功能"),window.BackupPage&&window.BackupPage.init&&(console.log("💾 调用数据备份页面模块的初始化"),window.BackupPage.init()),console.log("✅ 数据备份页面功能初始化完成")}},{key:"initializeDataEditPage",value:function(){console.log("📝 初始化数据编辑页面功能"),window.DataEditPage&&window.DataEditPage.init&&(console.log("📝 调用数据编辑页面模块的初始化"),window.DataEditPage.init()),console.log("✅ 数据编辑页面功能初始化完成")}},{key:"initializeUserRolesPage",value:function(){console.log("👥 初始化用户角色页面功能"),window.UserRolesPage&&window.UserRolesPage.init&&(console.log("👥 调用用户角色页面模块的初始化"),window.UserRolesPage.init()),console.log("✅ 用户角色页面功能初始化完成")}},{key:"initializeLabTypesPage",value:function(){console.log("🧪 初始化实验室类型页面功能"),window.LabTypesPage&&window.LabTypesPage.init&&(console.log("🧪 调用实验室类型页面模块的初始化"),window.LabTypesPage.init()),console.log("✅ 实验室类型页面功能初始化完成")}},{key:"initializeLabPropertiesPage",value:function(){console.log("🔬 初始化实验室属性页面功能"),window.LabPropertiesPage&&window.LabPropertiesPage.init&&(console.log("🔬 调用实验室属性页面模块的初始化"),window.LabPropertiesPage.init()),console.log("✅ 实验室属性页面功能初始化完成")}},{key:"initializeRiskLevelsPage",value:function(){console.log("⚠️ 初始化风险等级页面功能"),window.RiskLevelsPage&&window.RiskLevelsPage.init&&(console.log("⚠️ 调用风险等级页面模块的初始化"),window.RiskLevelsPage.init()),console.log("✅ 风险等级页面功能初始化完成")}},{key:"initializeTemplatesPage",value:function(){console.log("📄 初始化模板页面功能"),window.TemplatesPage&&window.TemplatesPage.init&&(console.log("📄 调用模板页面模块的初始化"),window.TemplatesPage.init()),console.log("✅ 模板页面功能初始化完成")}},{key:"initializeSafetyLedgersPage",value:function(){console.log("📊 初始化安全台账页面功能"),window.SafetyLedgersPage&&window.SafetyLedgersPage.init&&(console.log("📊 调用安全台账页面模块的初始化"),window.SafetyLedgersPage.init()),console.log("✅ 安全台账页面功能初始化完成")}},{key:"initializeInspectionActivityFormPage",value:function(){console.log("📋 初始化检查活动表单页面功能"),window.InspectionActivityFormPage&&window.InspectionActivityFormPage.init&&(console.log("📋 调用检查活动表单页面模块的初始化"),window.InspectionActivityFormPage.init()),console.log("✅ 检查活动表单页面功能初始化完成")}},{key:"initializeInspectionClausesPage",value:function(){console.log("📋 初始化检查条款页面功能"),window.InspectionClausesPage&&window.InspectionClausesPage.init&&(console.log("📋 调用检查条款页面模块的初始化"),window.InspectionClausesPage.init()),console.log("✅ 检查条款页面功能初始化完成")}},{key:"initializeInspectionRecordsPage",value:function(){console.log("📝 初始化检查记录页面功能"),window.InspectionRecordsPage&&window.InspectionRecordsPage.init&&(console.log("📝 调用检查记录页面模块的初始化"),window.InspectionRecordsPage.init()),console.log("✅ 检查记录页面功能初始化完成")}},{key:"initializeHazardDraftsPage",value:function(){console.log("📄 初始化隐患草稿页面功能"),window.HazardDraftsPage&&window.HazardDraftsPage.init&&(console.log("📄 调用隐患草稿页面模块的初始化"),window.HazardDraftsPage.init()),console.log("✅ 隐患草稿页面功能初始化完成")}},{key:"initializeHazardReportPage",value:function(){console.log("📊 初始化隐患报告页面功能"),window.HazardReportPage&&window.HazardReportPage.init&&(console.log("📊 调用隐患报告页面模块的初始化"),window.HazardReportPage.init()),console.log("✅ 隐患报告页面功能初始化完成")}},{key:"initializeHazardReviewPage",value:function(){console.log("✅ 初始化隐患审核页面功能"),window.HazardReviewPage&&window.HazardReviewPage.init&&(console.log("✅ 调用隐患审核页面模块的初始化"),window.HazardReviewPage.init()),console.log("✅ 隐患审核页面功能初始化完成")}},{key:"initializeReportsManagementPage",value:function(){console.log("📋 初始化报告管理页面功能"),window.ReportsManagementPage&&window.ReportsManagementPage.init&&(console.log("📋 调用报告管理页面模块的初始化"),window.ReportsManagementPage.init()),console.log("✅ 报告管理页面功能初始化完成")}},{key:"initializeCommonFeatures",value:function(){console.log("🔧 初始化通用功能"),window.$&&$.fn.modal&&$('[data-toggle="modal"]').off("click.modal").on("click.modal",(function(){var e=$(this).attr("data-target");e&&$(e).modal("show")})),console.log("🔽 下拉菜单由通用组件处理，跳过Bootstrap重复初始化"),window.$&&$.fn.tooltip&&$('[data-toggle="tooltip"]').tooltip(),console.log("✅ 通用功能初始化完成")}},{key:"highlightMenuLink",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;console.log("🎯 高亮菜单链接:",e),document.querySelectorAll(".sidebar a").forEach((function(e){e.classList.remove("active")})),("/"===e||e.toLowerCase().includes("/admin/index"))&&(console.log("🏠 检测到首页，准备折叠所有子菜单"),this.collapseAllSubmenus());var n=null;if(t)n=t;else{var o,i=document.querySelectorAll(".sidebar a[href]"),r=ke(i);try{for(r.s();!(o=r.n()).done;){var a=o.value,c=a.getAttribute("href");if(c&&c.toLowerCase()===e.toLowerCase()){n=a;break}}}catch(e){r.e(e)}finally{r.f()}if(!n){var l,s=ke(i);try{for(s.s();!(l=s.n()).done;){var u=l.value,d=u.getAttribute("href");if(d&&"/"!==d&&e.toLowerCase().includes(d.toLowerCase())){n=u;break}}}catch(e){s.e(e)}finally{s.f()}}}n?(n.classList.add("active"),console.log("✅ 高亮菜单:",n.textContent.trim())):console.log("⚠️ 未找到匹配的菜单链接:",e)}},{key:"collapseAllSubmenus",value:function(){console.log("📁 折叠所有子菜单 - 使用Bootstrap原生方法"),document.querySelectorAll(".sidebar .collapse").forEach((function(e){if(e.classList.contains("in")||e.classList.contains("show"))if(console.log("📁 使用Bootstrap方法折叠菜单: ".concat(e.id)),window.$&&window.$.fn.collapse)window.$(e).collapse("hide");else{var t=new Event("hide.bs.collapse",{bubbles:!0});e.dispatchEvent(t),e.classList.remove("show","in");var n=document.querySelector('[data-target="#'.concat(e.id,'"]'));n&&(n.setAttribute("aria-expanded","false"),n.classList.remove("active"))}console.log("📁 已处理菜单: ".concat(e.id))})),console.log("✅ 所有子菜单已处理")}},{key:"initMenuState",value:function(){var e=window.location.pathname,t=document.querySelector('.sidebar a[href="'.concat(e,'"]'));if(t){this.highlightMenuLink(e,t);var n=t.closest(".collapse");if(n){n.classList.add("show");var o=document.querySelector('[data-target="#'.concat(n.id,'"]'));o&&(o.setAttribute("aria-expanded","true"),o.classList.add("active"))}}}},{key:"saveMenuState",value:function(){var e={};return document.querySelectorAll(".sidebar .collapse").forEach((function(t){e[t.id]={isShown:t.classList.contains("show"),ariaExpanded:t.previousElementSibling?t.previousElementSibling.getAttribute("aria-expanded"):null}})),e}},{key:"restoreMenuState",value:function(e){for(var t in e){var n=document.getElementById(t),o=e[t];if(n){o.isShown?n.classList.add("show"):n.classList.remove("show");var i=document.querySelector('[data-target="#'.concat(t,'"]'));i&&null!==o.ariaExpanded&&i.setAttribute("aria-expanded",o.ariaExpanded)}}}},{key:"updateHistory",value:function(e){history.pushState({url:e},"",e)}},{key:"showLoader",value:function(){window.AjaxLoader&&window.AjaxLoader.showLoader&&window.AjaxLoader.showLoader()}},{key:"hideLoader",value:function(){window.AjaxLoader&&window.AjaxLoader.hideLoader&&window.AjaxLoader.hideLoader()}},{key:"setLoadingMode",value:function(e){["ajax","iframe"].includes(e)&&(this.loadingMode=e,console.log("AJAX菜单加载模式设置为:",e))}},{key:"exposeGlobalFunctions",value:function(){window.loadContent=this.loadContent,window.setupMenuLinks=this.setupMenuLinks,window.saveMenuState=this.saveMenuState,window.restoreMenuState=this.restoreMenuState}},{key:"registerPageInitializer",value:function(e,t){"function"==typeof t?(this.pageInitializers[e]=t,console.log("📝 注册页面初始化器: ".concat(e))):console.warn("页面初始化器必须是函数:",e)}},{key:"runPageInitializers",value:function(e){console.log("🚀 运行页面初始化器: ".concat(e));for(var t=0,n=Object.entries(this.pageInitializers);t<n.length;t++){var o=we(n[t],2),i=o[0],r=o[1];if(e.includes(i)||"base"===i)try{console.log("✅ 执行初始化器: ".concat(i)),r()}catch(e){console.error("❌ 初始化器执行失败 ".concat(i,":"),e)}}}},{key:"destroy",value:function(){delete window.loadContent,delete window.setupMenuLinks,delete window.saveMenuState,delete window.restoreMenuState,this.pageInitializers={},this.isInitialized=!1,console.log("统一AJAX菜单模块已销毁")}}],t&&Le(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,i,a}(),Ie=new xe,ze=n(2183),je=n(1559),Oe=n(165),Me=n(5276),De=n(5906),_e=n(987),Be=n(9886),Re=n(1270),qe=n(6346),He=n(508),Ne=n(370),Ge=n(1505),$e=n(928),Ue=n(2304),Xe=n(2952),Ve=n(9292),Je=n(6080),We=n(594),Qe=n(6751),Ye=n(2650),Ze=n(8502),Ke=n(5066);function et(e){return et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},et(e)}function tt(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,nt(o.key),o)}}function nt(e){var t=function(e){if("object"!=et(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=et(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==et(t)?t:t+""}n(5164),n(5793),n(3885),n(5073),n(9006),n(8245),n(3568),n(3710),n(6015),n(1019),n(3410),n(4241),n(784),n(4952),n(6806),n(8762),n(8060),n(4275),n(717),n(1164),n(1303),n(5771),n(7162),n(6772),n(9170),n(2796),n(1902),n(7749),n(1759),n(3637),n(1460),n(9568),n(6371),n(2132),n(8186),n(9097),n(1848),n(3898),n(3284),n(8988),n(5123),n(9227),n(2748),n(1766);var ot=new(function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.modules=new Map,this.init=this.init.bind(this),this.initGlobalFeatures=this.initGlobalFeatures.bind(this),this.registerModule=this.registerModule.bind(this)},t=[{key:"init",value:function(){var e=this;this.isInitialized||(console.log("开始初始化应用..."),(0,r.Qc)((function(){e.initGlobalFeatures(),e.initModules(),e.bindGlobalEvents(),e.isInitialized=!0,console.log("应用初始化完成")})))}},{key:"initGlobalFeatures",value:function(){this.initSidebar(),this.initStatCards(),this.initTables(),this.initForms()}},{key:"initModules",value:function(){console.log("🔧 开始初始化核心模块..."),console.log("📋 菜单管理器已禁用，使用统一AJAX菜单..."),console.log("👥 初始化在线用户模块..."),p.init(),this.registerModule("onlineUsers",p),console.log("📊 初始化报告管理器..."),E.init(),this.registerModule("reportManager",E),console.log("🔍 初始化检查活动管理器..."),z.init(),this.registerModule("inspectionActivityManager",z),console.log("📈 初始化图表管理器..."),q.init(),this.registerModule("chartManager",q),console.log("📋 初始化表格管理器..."),U.init(),this.registerModule("tableManager",U),console.log("🔐 初始化验证码删除模块..."),ee.init(),this.registerModule("captchaDelete",ee),console.log("⚡ 重新启用AJAX加载器..."),le.init(),this.registerModule("ajaxLoader",le),console.log("🔽 初始化下拉框修复模块..."),fe.init(),this.registerModule("dropdownFix",fe),console.log("🔽 初始化通用下拉菜单组件..."),ge.init(),this.registerModule("universalDropdown",ge),console.log("🌐 重新启用统一AJAX菜单..."),Ie.init(),this.registerModule("unifiedAjaxMenu",Ie),console.log("✨ 增强菜单管理器已禁用，使用Bootstrap原生动画..."),console.log("✅ 所有核心模块初始化完成")}},{key:"registerModule",value:function(e,t){this.modules.set(e,t),console.log("模块已注册: ".concat(e))}},{key:"getModule",value:function(e){return this.modules.get(e)||null}},{key:"initSidebar",value:function(){var e=document.getElementById("sidebar-toggle");e&&e.addEventListener("click",(function(){document.body.classList.toggle("sidebar-collapsed")})),window.innerWidth<768&&document.body.classList.add("sidebar-collapsed")}},{key:"initStatCards",value:function(){document.querySelectorAll(".stat-card").forEach((function(e){e.addEventListener("mouseenter",(function(){this.style.transform="translateY(-5px)",this.style.boxShadow="0 5px 15px rgba(0,0,0,0.1)"})),e.addEventListener("mouseleave",(function(){this.style.transform="translateY(0)",this.style.boxShadow="0 1px 3px rgba(0,0,0,0.1)"}))}))}},{key:"initTables",value:function(){var e=this;document.querySelectorAll("[data-sort]").forEach((function(t){t.addEventListener("click",(function(t){var n=parseInt(t.target.dataset.sort),o=t.target.closest("table");o&&e.sortTable(o,n)}))}))}},{key:"sortTable",value:function(e,t){var n=e.querySelector("tbody");if(n){var o=Array.from(n.querySelectorAll("tr")),i="asc"!==e.dataset.sortDir;e.dataset.sortDir=i?"asc":"desc",o.sort((function(e,n){var o,r,a=(null===(o=e.cells[t])||void 0===o?void 0:o.textContent.trim())||"",c=(null===(r=n.cells[t])||void 0===r?void 0:r.textContent.trim())||"",l=parseFloat(a),s=parseFloat(c);return isNaN(l)||isNaN(s)?i?a.localeCompare(c,"zh-CN"):c.localeCompare(a,"zh-CN"):i?l-s:s-l})),o.forEach((function(e){return n.appendChild(e)}))}}},{key:"initForms",value:function(){var e=this;document.querySelectorAll("form[data-validate]").forEach((function(t){t.addEventListener("submit",e.validateForm.bind(e))})),this.setupCSRFToken()}},{key:"setupCSRFToken",value:function(){(0,r.I6)((function(e){e.ajaxSetup({beforeSend:function(e,t){/^(GET|HEAD|OPTIONS|TRACE)$/i.test(t.type)||this.crossDomain||e.setRequestHeader("X-CSRFToken",o.A.getCsrfToken())}})}))}},{key:"validateForm",value:function(e){var t=e.target.querySelectorAll("[required]"),n=!0;t.forEach((function(e){e.value.trim()?e.classList.remove("is-invalid"):(e.classList.add("is-invalid"),n=!1)})),n||(e.preventDefault(),i.A.alert("请填写所有必填字段"))}},{key:"bindGlobalEvents",value:function(){var e=this;window.addEventListener("error",(function(e){console.error("全局错误:",e.error)})),window.addEventListener("unhandledrejection",(function(e){console.error("未处理的Promise拒绝:",e.reason)})),document.addEventListener("visibilitychange",(function(){if(document.hidden)console.log("页面隐藏");else{console.log("页面显示");var t=e.getModule("onlineUsers");t&&t.refresh()}}))}},{key:"destroy",value:function(){this.modules.forEach((function(e,t){e.destroy&&"function"==typeof e.destroy&&(e.destroy(),console.log("模块已销毁: ".concat(t)))})),this.modules.clear(),this.isInitialized=!1,console.log("应用已销毁")}}],t&&tt(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}());console.log("🚀 app.js 文件已加载"),console.log("📦 开始初始化应用..."),ot.init(),console.log("✅ 应用初始化完成"),window.App=ot,window.ApiClient=o.A,window.Modal=i.A,window.OnlineUsersModule=p,window.ReportManager=E,window.InspectionActivityManager=z,window.ChartManager=q,window.TableManager=U,window.CaptchaDelete=ee,window.AjaxLoader=le,window.DropdownFix=fe,window.UniversalDropdown=ge,window.UnifiedAjaxMenu=Ie,console.log("📦 预加载的页面模块:",{InspectionActivitiesPage:et(ze.A),UsersPage:et(je.A),IssueReviewPage:et(Oe.A)});var it=[ze.A,je.A,Oe.A,Me.A,De.A,_e.A,Be.A,Re.A,qe.A,He.A,Ne.A,Ge.A,$e.A,Ue.A,Xe.A,Ve.A,Je.A,We.A,Qe.A,Ye.A,Ze.A,Ke.A];console.log("✅ 已引用",it.length,"个页面模块，确保它们被执行"),E.exposeGlobalFunctions(),z.exposeGlobalFunctions(),U.exposeGlobalFunctions(),ee.exposeGlobalFunctions(),le.exposeGlobalFunctions(),Ie.exposeGlobalFunctions()},8502:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function c(n,o,r,a){var c=o&&o.prototype instanceof s?o:s,u=Object.create(c.prototype);return i(u,"_invoke",function(n,o,i){var r,a,c,s=0,u=i||[],d=!1,f={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return r=t,a=0,c=e,f.n=n,l}};function v(n,o){for(a=n,c=o,t=0;!d&&s&&!i&&t<u.length;t++){var i,r=u[t],v=f.p,p=r[2];n>3?(i=p===o)&&(a=r[4]||3,c=r[5]===e?r[3]:r[5],r[4]=3,r[5]=e):r[0]<=v&&((i=n<2&&v<r[1])?(a=0,f.v=o,f.n=r[1]):v<p&&(i=n<3||r[0]>o||o>p)&&(r[4]=n,r[5]=o,f.n=p,a=0))}if(i||n>1)return l;throw d=!0,o}return function(i,u,p){if(s>1)throw TypeError("Generator is already running");for(d&&1===u&&v(u,p),a=u,c=p;(t=a<2?e:c)||!d;){r||(a?a<3?(a>1&&(f.n=-1),v(a,c)):f.n=c:f.v=c);try{if(s=2,r){if(a||(i="next"),t=r[i]){if(!(t=t.call(r,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,a<2&&(a=0)}else 1===a&&(t=r.return)&&t.call(r),a<2&&(c=TypeError("The iterator does not provide a '"+i+"' method"),a=1);r=e}else if((t=(d=f.n<0)?c:n.call(o,f))!==l)break}catch(t){r=e,a=1,c=t}finally{s=1}}return{value:t,done:d}}}(n,r,a),!0),u}var l={};function s(){}function u(){}function d(){}t=Object.getPrototypeOf;var f=[][r]?t(t([][r]())):(i(t={},r,(function(){return this})),t),v=d.prototype=s.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,i(e,a,"GeneratorFunction")),e.prototype=Object.create(v),e}return u.prototype=d,i(v,"constructor",d),i(d,"constructor",u),u.displayName="GeneratorFunction",i(d,a,"GeneratorFunction"),i(v),i(v,a,"Generator"),i(v,r,(function(){return this})),i(v,"toString",(function(){return"[object Generator]"})),(o=function(){return{w:c,m:p}})()}function i(e,t,n,o){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}i=function(e,t,n,o){if(t)r?r(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{function a(t,n){i(e,t,(function(e){return this._invoke(t,n,e)}))}a("next",0),a("throw",1),a("return",2)}},i(e,t,n,o)}function r(e,t,n,o,i,r,a){try{var c=e[r](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(o,i)}function a(e){return function(){var t=this,n=arguments;return new Promise((function(o,i){var a=e.apply(t,n);function c(e){r(a,o,i,c,l,"next",e)}function l(e){r(a,o,i,c,l,"throw",e)}c(void 0)}))}}function c(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,l(o.key),o)}}function l(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var s=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.sortableInstances=[],this.currentRoleId=null,this.currentRoleName="",this.isAdminRole=!1},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("菜单管理页面已初始化，跳过重复初始化"):(console.log("🔧 初始化菜单管理页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化菜单管理页面功能"),this.extractGlobalVariables(),this.initSortable(),this.initRoleSelection(),this.initMenuToggle(),this.initPreview(),this.initCategoryOrder(),this.initializeButtonEvents(),this.defineGlobalFunctions(),console.log("🔍 页面初始化完成后的状态检查:"),console.log("  当前页面URL:",window.location.href),console.log("  MenuManagementPage实例:",this),console.log("  window.MenuManagementPage:",window.MenuManagementPage),setTimeout((function(){var e=document.querySelectorAll('button[data-action="toggle-menu"]'),t=document.querySelectorAll('button[data-action="edit-menu"]');console.log("  找到toggle-menu按钮数量:",e.length),console.log("  找到edit-menu按钮数量:",t.length),e.length>0&&(console.log("  第一个toggle-menu按钮:",e[0]),console.log("  data-menu-id属性:",e[0].getAttribute("data-menu-id")))}),100),console.log("✅ 菜单管理页面功能初始化完成")}},{key:"extractGlobalVariables",value:function(){this.currentRoleId=window.currentRoleId||null,this.currentRoleName=window.currentRoleName||"",this.isAdminRole="admin"===this.currentRoleName,console.log("当前角色ID:",this.currentRoleId),console.log("当前角色名称:",this.currentRoleName),console.log("是否管理员:",this.isAdminRole)}},{key:"initSortable",value:function(){console.log("🔄 初始化拖拽排序"),void 0!==window.Sortable?(this.sortableInstances&&this.sortableInstances.length>0&&(this.sortableInstances.forEach((function(e){e&&e.destroy&&e.destroy()})),this.sortableInstances=[]),this.initMenuItemSortable(),console.log("✅ 二级菜单拖拽排序初始化完成，共创建",this.sortableInstances.length,"个sortable实例")):console.warn("⚠️ Sortable库未加载，跳过拖拽排序功能")}},{key:"initCategorySortable",value:function(){var e=this;console.log("🔄 初始化一级菜单排序");var t=document.getElementById("categoryOrderTableBody");if(t){console.log("找到一级菜单表格tbody，创建sortable实例");var n=window.Sortable.create(t,{animation:200,ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",handle:".category-drag-handle",forceFallback:!1,fallbackTolerance:3,scroll:!0,scrollSensitivity:30,scrollSpeed:10,onStart:function(t){console.log("开始拖拽一级菜单:",t.item.dataset.categoryId),e.showDragHint()},onEnd:function(t){console.log("一级菜单拖拽结束:",t.item.dataset.categoryId),e.hideDragHint(),t.oldIndex!==t.newIndex&&(e.updateCategoryOrder(),e.showSuccessMessage('一级菜单排序已更改，请点击"保存全局一级菜单设置"按钮保存更改'))},onSort:function(e){console.log("一级菜单排序更改:",e.oldIndex,"->",e.newIndex)}});this.sortableInstances.push(n),console.log("✅ 一级菜单sortable实例创建成功")}else console.warn("⚠️ 未找到一级菜单表格tbody #categoryOrderTableBody")}},{key:"initMenuItemSortable",value:function(){var e=this;console.log("🔄 初始化二级菜单排序");var t=document.querySelectorAll(".menu-items-table tbody");console.log("找到菜单表格tbody数量:",t.length),t.forEach((function(t,n){console.log("初始化第".concat(n+1,"个菜单表格的拖拽功能"));var o=window.Sortable.create(t,{group:{name:"menu-items",pull:!0,put:!0},animation:200,ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",handle:".drag-handle",forceFallback:!1,fallbackTolerance:3,scroll:!0,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0,delay:0,delayOnTouchOnly:!1,delayOnTouchStart:!0,onStart:function(t){console.log("开始拖拽菜单项:",t.item.dataset.id),document.querySelectorAll(".menu-items-table tbody").forEach((function(e){e.classList.add("drag-target")})),e.showDragHint()},onEnd:function(t){console.log("拖拽结束:",t.item.dataset.id),document.querySelectorAll(".menu-items-table tbody").forEach((function(e){e.classList.remove("drag-target")})),e.hideDragHint(),t.oldIndex===t.newIndex&&t.from===t.to||(e.updateMenuItemOrder(t.to),e.showSuccessMessage('菜单排序已更改，请点击"保存排序"按钮保存更改'))},onAdd:function(t){console.log("菜单项移动到新分类:",t.item.dataset.id),e.updateMenuItemOrder(t.to)},onUpdate:function(t){console.log("菜单项内部排序:",t.item.dataset.id),e.updateMenuItemOrder(t.from)},onSort:function(e){console.log("排序更改:",e.oldIndex,"->",e.newIndex)}});e.sortableInstances.push(o)}))}},{key:"updateCategoryOrder",value:function(){var e=document.querySelectorAll("#categoryOrderTableBody .category-order-item"),t=[];e.forEach((function(e,n){var o=e.getAttribute("data-category-id");o&&t.push({category_id:o,order:n+1})})),console.log("一级菜单新顺序:",t)}},{key:"updateMenuItemOrder",value:function(e){var t=e.querySelectorAll(".menu-item"),n=[];t.forEach((function(e,t){var o=e.getAttribute("data-id");if(o){var i=e.querySelector(".menu-item-order");i&&(i.textContent=t+1),n.push({item_id:o,order:t+1})}})),console.log("二级菜单新顺序:",n)}},{key:"showDragHint",value:function(){var e=this;this.hideDragHint();var t=document.createElement("div");t.className="drag-hint alert alert-info",t.style.cssText="\n      position: fixed;\n      top: 10px;\n      right: 10px;\n      z-index: 2000;\n      max-width: 300px;\n      box-shadow: 0 4px 8px rgba(0,0,0,0.2);\n    ",t.innerHTML='\n      <i class="fas fa-hand-rock"></i>\n      <strong>拖拽提示：</strong><br>\n      • 可以在同分类内调整顺序<br>\n      • 可以拖拽到其他分类<br>\n      • 松开鼠标完成拖拽\n    ',document.body.appendChild(t),setTimeout((function(){e.hideDragHint()}),3e3)}},{key:"hideDragHint",value:function(){var e=document.querySelector(".drag-hint");e&&e.remove()}},{key:"initRoleSelection",value:function(){var e=this;console.log("👤 初始化角色选择");var t=document.getElementById("roleSelect");t&&t.addEventListener("change",(function(t){e.switchRole(t.target.value)}))}},{key:"switchRole",value:(p=a(o().m((function e(t){var n,i,r;return o().w((function(e){for(;;)switch(e.n){case 0:if(t){e.n=1;break}return e.a(2);case 1:return console.log("切换角色:",t),e.p=2,this.showLoading(),e.n=3,fetch("/admin/api/menu_management/role/".concat(t),{method:"GET",headers:{"X-CSRFToken":this.getCSRFToken()}});case 3:return n=e.v,e.n=4,n.json();case 4:(i=e.v).success?(this.updateMenuDisplay(i.menus),this.currentRoleId=t):(console.error("切换角色失败:",i.message),alert("切换角色失败")),e.n=6;break;case 5:e.p=5,r=e.v,console.error("切换角色异常:",r),alert("网络错误，请重试");case 6:return e.p=6,this.hideLoading(),e.f(6);case 7:return e.a(2)}}),e,this,[[2,5,6,7]])}))),function(e){return p.apply(this,arguments)})},{key:"updateMenuDisplay",value:function(e){document.getElementById("menuContainer")&&console.log("更新菜单显示:",e)}},{key:"initMenuToggle",value:function(){console.log("🔄 初始化菜单切换（已废弃）")}},{key:"initializeButtonEvents",value:function(){var e=this;console.log("🔧 初始化按钮事件委托..."),document.addEventListener("click",(function(t){var n=t.target.closest('button[data-action="toggle-menu"]');if(n){var o=n.getAttribute("data-menu-id");o&&(console.log("🔄 检测到菜单切换按钮点击:",o),t.preventDefault(),t.stopPropagation(),e.toggleMenuItemStatus(o))}else{var i=t.target.closest('button[data-action="edit-menu"]');if(i){var r=i.getAttribute("data-menu-id");r&&(console.log("✏️ 检测到菜单编辑按钮点击:",r),t.preventDefault(),t.stopPropagation(),e.editMenuItem(r))}else{var a=t.target.closest('button[data-action="toggle-category-visibility"]');if(a){var c=a.getAttribute("data-category-id");c&&(console.log("👁️ 检测到分类可见性切换按钮点击:",c),t.preventDefault(),t.stopPropagation(),e.toggleCategoryVisibility(c))}else{var l=t.target.closest('button[data-action="toggle-category-access"]');if(l){var s=l.getAttribute("data-category-id");s&&(console.log("🔐 检测到分类启用状态切换按钮点击:",s),t.preventDefault(),t.stopPropagation(),e.toggleCategoryAccess(s))}}}}})),console.log("✅ 按钮事件委托初始化完成")}},{key:"toggleMenuItem",value:(v=a(o().m((function e(t){return o().w((function(e){for(;;)if(0===e.n)return console.log("⚠️ toggleMenuItem已废弃，请使用toggleMenuItemStatus"),e.a(2,this.toggleMenuItemStatus(t))}),e,this)}))),function(e){return v.apply(this,arguments)})},{key:"toggleMenuItemStatus",value:(f=a(o().m((function e(t){var n,i,r;return o().w((function(e){for(;;)switch(e.n){case 0:if(console.log("🔄 切换菜单项状态被调用:",t),t){e.n=1;break}return console.error("菜单项ID不能为空"),e.a(2);case 1:return e.p=1,this.showLoading(),e.n=2,fetch("/admin/menu_management/api/toggle_menu/".concat(t),{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":this.getCSRFToken()}});case 2:return n=e.v,e.n=3,n.json();case 3:(i=e.v).success?(this.updateButtonState(t,i.is_menu_item),this.showSuccessMessage('菜单项 "'.concat(i.message||"状态已更新",'" '))):alert("操作失败: "+i.message),e.n=5;break;case 4:e.p=4,r=e.v,console.error("切换菜单项失败:",r),alert("操作失败，请重试");case 5:return e.p=5,this.hideLoading(),e.f(5);case 6:return e.a(2)}}),e,this,[[1,4,5,6]])}))),function(e){return f.apply(this,arguments)})},{key:"updateButtonState",value:function(e,t){console.log("🔄 更新按钮状态:",{menuId:e,isVisible:t});var n=document.querySelector('button[data-action="toggle-menu"][data-menu-id="'.concat(e,'"]'));if(n){var o=n.querySelector("i");o&&(t?(o.className="fas fa-eye",n.title="隐藏菜单项"):(o.className="fas fa-eye-slash",n.title="显示菜单项"),console.log("✅ 按钮状态已更新:",o.className)),this.updateRowStatus(e,t)}else console.error("❌ 找不到对应的按钮:",e)}},{key:"updateRowStatus",value:function(e,t){var n=document.querySelector('tr[data-menu-id="'.concat(e,'"]'));if(n)this.updateRowStatusDisplay(n,t);else{var o=document.querySelector('button[data-menu-id="'.concat(e,'"]'));if(o){var i=o.closest("tr");i&&this.updateRowStatusDisplay(i,t)}}}},{key:"updateRowStatusDisplay",value:function(e,t){var n=e.querySelector(".td-status");if(n){var o=n.querySelector(".status-badge");o&&(t?(o.className="status-badge status-visible",o.textContent="显示"):(o.className="status-badge status-hidden",o.textContent="隐藏"),console.log("✅ 行状态已更新:",t?"显示":"隐藏"))}}},{key:"updateToggleButton",value:function(e,t){console.log("⚠️ updateToggleButton已废弃，请使用updateButtonState");var n="show"===t;this.updateButtonState(e,n)}},{key:"initPreview",value:function(){var e=this;console.log("👁️ 初始化预览功能");var t=document.getElementById("previewBtn");t&&t.addEventListener("click",(function(){e.showPreview()}));var n=document.getElementById("hidePreviewBtn");n&&n.addEventListener("click",(function(){e.hidePreview()}))}},{key:"showPreview",value:(d=a(o().m((function e(){var t,n,i;return o().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,this.showLoading(),e.n=1,fetch("/admin/api/menu_management/preview/".concat(this.currentRoleId),{method:"GET",headers:{"X-CSRFToken":this.getCSRFToken()}});case 1:return t=e.v,e.n=2,t.json();case 2:(n=e.v).success?this.displayPreview(n.preview_html):(console.error("获取预览失败:",n.message),alert("获取预览失败")),e.n=4;break;case 3:e.p=3,i=e.v,console.error("获取预览异常:",i),alert("网络错误，请重试");case 4:return e.p=4,this.hideLoading(),e.f(4);case 5:return e.a(2)}}),e,this,[[0,3,4,5]])}))),function(){return d.apply(this,arguments)})},{key:"displayPreview",value:function(e){var t=document.getElementById("previewContainer"),n=document.getElementById("previewContent");t&&n&&(n.innerHTML=e,t.style.display="block")}},{key:"hidePreview",value:function(){var e=document.getElementById("previewContainer");e&&(e.style.display="none")}},{key:"initCategoryOrder",value:function(){console.log("📋 初始化分类顺序"),this.loadGlobalCategories()}},{key:"loadGlobalCategories",value:(u=a(o().m((function e(){var t,n,i,r=this;return o().w((function(e){for(;;)switch(e.n){case 0:return console.log("📋 从API加载全局分类数据"),e.p=1,e.n=2,fetch("/admin/api/menu_management/global_categories",{method:"GET",headers:{"X-CSRFToken":this.getCSRFToken()}});case 2:return t=e.v,e.n=3,t.json();case 3:(n=e.v).success?(console.log("✅ 成功获取全局分类数据:",n.categories),this.displayCategoryOrder(n.categories),setTimeout((function(){r.initCategorySortable()}),100)):(console.warn("⚠️ API获取分类失败，回退到页面提取方式:",n.message),this.loadCategoryOrderFromPage()),e.n=5;break;case 4:e.p=4,i=e.v,console.error("❌ API请求失败，回退到页面提取方式:",i),this.loadCategoryOrderFromPage();case 5:return e.a(2)}}),e,this,[[1,4]])}))),function(){return u.apply(this,arguments)})},{key:"loadCategoryOrderFromPage",value:function(){var e=this;console.log("📋 从页面数据加载分类顺序");var t=document.querySelectorAll(".menu-category"),n=[];t.forEach((function(e,t){var o=e.getAttribute("data-category"),i=e.querySelector(".category-header"),r=e.querySelectorAll(".menu-item").length;o&&i&&n.push({id:o,name:o,icon_class:"fas fa-folder",is_visible:!0,order:t+1,item_count:r})})),console.log("找到分类数据:",n),n.length>0?(this.displayCategoryOrder(n),setTimeout((function(){e.initCategorySortable()}),100)):console.warn("未找到分类数据")}},{key:"loadCategoryOrder",value:(s=a(o().m((function e(){var t,n,i;return o().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,fetch("/admin/api/menu_management/category_order",{method:"GET",headers:{"X-CSRFToken":this.getCSRFToken()}});case 1:return t=e.v,e.n=2,t.json();case 2:(n=e.v).success?this.displayCategoryOrder(n.categories):console.error("加载分类顺序失败:",n.message),e.n=4;break;case 3:e.p=3,i=e.v,console.error("加载分类顺序异常:",i);case 4:return e.a(2)}}),e,this,[[0,3]])}))),function(){return s.apply(this,arguments)})},{key:"displayCategoryOrder",value:function(e){var t=this;console.log("🔍 displayCategoryOrder 被调用，分类数量:",e.length);var n=document.getElementById("categoryOrderTableBody");n?(n.innerHTML="",e.forEach((function(e){var o=t.createCategoryOrderRow(e);n.appendChild(o)})),console.log("🎯 一级菜单表格生成完成，行数:",n.children.length),console.log("🎯 表格HTML长度:",n.innerHTML.length)):console.error("❌ 未找到 categoryOrderTableBody 元素")}},{key:"createCategoryOrderRow",value:function(e){console.log("🔍 创建一级菜单行:",e.name);var t=document.createElement("tr");t.className="category-order-item menu-item",t.setAttribute("data-category-id",e.id),t.setAttribute("data-order",e.order||0);var n=this.isProtectedCategory(e.name),o=document.createElement("td");o.className="td-drag-handle",o.innerHTML='<i class="fas fa-grip-vertical drag-handle category-drag-handle"></i>';var i=document.createElement("td");i.className="td-checkbox",i.innerHTML='<input type="checkbox" class="menu-item-checkbox category-checkbox" value="'.concat(e.id,'">');var r=document.createElement("td");r.className="td-icon",r.innerHTML='<i class="'.concat(e.icon_class||"fas fa-folder",'"></i>');var a=document.createElement("td");a.className="td-name",a.innerHTML="".concat(e.name).concat(n?'<span class="badge badge-warning ml-2">受保护</span>':"");var c=document.createElement("td");c.className="td-description",c.innerHTML='<span class="menu-item-description">'.concat(e.item_count||0," 个菜单项</span>");var l=document.createElement("td");l.className="td-order",l.innerHTML='<span class="menu-item-order">'.concat(e.order||0,"</span>");var s=document.createElement("td");s.className="td-status",s.innerHTML=e.is_visible?'<span class="status-badge status-visible">显示</span>':'<span class="status-badge status-hidden">隐藏</span>';var u=document.createElement("td");u.className="td-status",u.innerHTML=!1!==e.can_access?'<span class="status-badge status-enabled">启用</span>':'<span class="status-badge status-disabled">禁用</span>';var d=document.createElement("td");return d.className="td-actions",d.innerHTML='\n      <button type="button" class="btn btn-sm btn-outline-primary toggle-category-visibility-btn"\n              data-category-id="'.concat(e.id,'" data-action="toggle-category-visibility"\n              title="').concat(e.is_visible?"隐藏分类":"显示分类",'"\n              ').concat(n?"disabled":"",'>\n        <i class="fas fa-eye').concat(e.is_visible?"-slash":"",'"></i>\n      </button>\n      <button type="button" class="btn btn-sm btn-outline-secondary toggle-category-access-btn"\n              data-category-id="').concat(e.id,'" data-action="toggle-category-access"\n              title="').concat(!1!==e.can_access?"禁用分类":"启用分类",'"\n              ').concat(n?"disabled":"",'>\n        <i class="fas fa-').concat(!1!==e.can_access?"ban":"check",'"></i>\n      </button>\n    '),t.appendChild(o),t.appendChild(i),t.appendChild(r),t.appendChild(a),t.appendChild(c),t.appendChild(l),t.appendChild(s),t.appendChild(u),t.appendChild(d),console.log("🎯 一级菜单行创建完成，td数量:",t.children.length),t}},{key:"isProtectedCategory",value:function(e){return this.isAdminRole&&["系统管理","权限管理","菜单管理","角色管理","用户管理"].includes(e)}},{key:"toggleCategoryVisibility",value:(l=a(o().m((function e(t){var n,i,r;return o().w((function(e){for(;;)switch(e.n){case 0:if(console.log("切换分类可见性:",t),!this.isProtectedCategory(t)){e.n=1;break}return alert("admin账户的权限管理菜单不能隐藏，这是系统关键功能！"),e.a(2);case 1:return e.p=1,this.showLoading(),e.n=2,fetch("/admin/api/menu_management/toggle_category_visibility",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":this.getCSRFToken()},body:JSON.stringify({category_id:t,role_id:this.currentRoleId})});case 2:return n=e.v,e.n=3,n.json();case 3:(i=e.v).success?(this.updateCategoryVisibilityButton(t,i.is_visible),this.showSuccessMessage('分类"'.concat(t,'"已').concat(i.is_visible?"显示":"隐藏")),i.is_visible||this.refreshSideMenu()):alert("操作失败: "+i.message),e.n=5;break;case 4:e.p=4,r=e.v,console.error("切换分类可见性失败:",r),alert("操作失败，请重试");case 5:return e.p=5,this.hideLoading(),e.f(5);case 6:return e.a(2)}}),e,this,[[1,4,5,6]])}))),function(e){return l.apply(this,arguments)})},{key:"toggleCategoryAccess",value:(r=a(o().m((function e(t){var n,i,r;return o().w((function(e){for(;;)switch(e.n){case 0:if(console.log("切换分类启用状态:",t),!this.isProtectedCategory(t)){e.n=1;break}return alert("admin账户的权限管理菜单不能禁用，这是系统关键功能！"),e.a(2);case 1:return e.p=1,this.showLoading(),e.n=2,fetch("/admin/api/menu_management/toggle_category_access",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":this.getCSRFToken()},body:JSON.stringify({category_id:t,role_id:this.currentRoleId})});case 2:return n=e.v,e.n=3,n.json();case 3:(i=e.v).success?(this.updateCategoryAccessButton(t,i.can_access),this.showSuccessMessage('分类"'.concat(t,'"已').concat(i.can_access?"启用":"禁用"))):alert("操作失败: "+i.message),e.n=5;break;case 4:e.p=4,r=e.v,console.error("切换分类启用状态失败:",r),alert("操作失败，请重试");case 5:return e.p=5,this.hideLoading(),e.f(5);case 6:return e.a(2)}}),e,this,[[1,4,5,6]])}))),function(e){return r.apply(this,arguments)})},{key:"updateCategoryVisibilityButton",value:function(e,t){var n=document.querySelector('button[data-action="toggle-category-visibility"][data-category-id="'.concat(e,'"]'));if(n){var o=n.querySelector("i");o&&(o.className="fas fa-eye".concat(t?"-slash":"")),n.title=t?"隐藏分类":"显示分类"}var i=document.querySelector('tr[data-category-id="'.concat(e,'"]'));if(i){var r=i.querySelectorAll(".td-status");if(r.length>0){var a=r[0].querySelector(".status-badge");a&&(t?(a.className="status-badge status-visible",a.textContent="显示"):(a.className="status-badge status-hidden",a.textContent="隐藏"))}}}},{key:"updateCategoryAccessButton",value:function(e,t){var n=document.querySelector('button[data-action="toggle-category-access"][data-category-id="'.concat(e,'"]'));if(n){var o=n.querySelector("i");o&&(o.className="fas fa-".concat(t?"ban":"check")),n.title=t?"禁用分类":"启用分类"}var i=document.querySelector('tr[data-category-id="'.concat(e,'"]'));if(i){var r=i.querySelectorAll(".td-status");if(r.length>1){var a=r[1].querySelector(".status-badge");a&&(t?(a.className="status-badge status-enabled",a.textContent="启用"):(a.className="status-badge status-disabled",a.textContent="禁用"))}}}},{key:"refreshSideMenu",value:function(){window.UnifiedAjaxMenu&&window.UnifiedAjaxMenu.refreshMenu?window.UnifiedAjaxMenu.refreshMenu():console.log("刷新页面以更新左侧菜单")}},{key:"saveCategoryOrder",value:(i=a(o().m((function e(){var t,n,i,r;return o().w((function(e){for(;;)switch(e.n){case 0:return t=this.getCategoryOrderData(),e.p=1,this.showLoading(),e.n=2,fetch("/admin/api/menu_management/save_global_category_order",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":this.getCSRFToken()},body:JSON.stringify({categories:t})});case 2:return n=e.v,e.n=3,n.json();case 3:(i=e.v).success?alert("分类顺序保存成功"):alert("保存失败: "+i.message),e.n=5;break;case 4:e.p=4,r=e.v,console.error("保存分类顺序失败:",r),alert("保存失败，请重试");case 5:return e.p=5,this.hideLoading(),e.f(5);case 6:return e.a(2)}}),e,this,[[1,4,5,6]])}))),function(){return i.apply(this,arguments)})},{key:"getCategoryOrderData",value:function(){var e=document.querySelectorAll("#categoryOrderTableBody .category-order-item"),t=[];return e.forEach((function(e,n){var o=e.getAttribute("data-category-id");if(o){var i=e.querySelector(".td-status .status-badge"),r=!i||i.classList.contains("status-visible"),a=e.querySelectorAll(".td-status"),c=a.length>1?a[1].querySelector(".status-badge"):null,l=!c||c.classList.contains("status-enabled"),s=e.querySelector(".td-icon i"),u=s?s.className:"fas fa-folder";t.push({id:o,name:o,icon_class:u,is_visible:r,can_access:l,order:n+1})}})),t}},{key:"showLoading",value:function(){var e=document.getElementById("loadingOverlay");e&&(e.style.display="flex")}},{key:"hideLoading",value:function(){var e=document.getElementById("loadingOverlay");e&&(e.style.display="none")}},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"showSuccessMessage",value:function(e){console.log("✅ 成功:",e);var t=document.createElement("div");t.className="alert alert-success alert-dismissible fade show",t.style.cssText="\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      z-index: 9999;\n      min-width: 300px;\n      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n    ",t.innerHTML='\n      <i class="fas fa-check-circle"></i> '.concat(e,'\n      <button type="button" class="close" data-dismiss="alert">\n        <span>&times;</span>\n      </button>\n    '),document.body.appendChild(t),setTimeout((function(){t.parentNode&&t.parentNode.removeChild(t)}),3e3)}},{key:"editMenuItem",value:function(e){if(console.log("编辑菜单项:",e),e){var t="/admin/menu_management/edit/".concat(e);window.open(t,"_blank")}else console.error("菜单项ID不能为空")}},{key:"showUnifiedPermissionMatrix",value:function(){console.log("显示统一权限管理矩阵"),window.open("/admin/menu_management/unified_permission_matrix","_blank")}},{key:"showCategoryPermissionMatrix",value:function(){console.log("显示一级菜单权限矩阵"),window.open("/admin/menu_management/category_permission_matrix","_blank")}},{key:"showPermissionMatrix",value:function(){console.log("显示二级菜单权限矩阵"),window.open("/admin/menu_management/permission_matrix","_blank")}},{key:"saveMenuOrder",value:function(){console.log("保存菜单排序"),alert("保存菜单排序功能待实现")}},{key:"resetOrder",value:function(){console.log("重置排序"),confirm("确定要重置排序吗？")&&alert("重置排序功能待实现")}},{key:"undoLastChange",value:function(){console.log("撤销更改"),alert("撤销更改功能待实现")}},{key:"batchOperation",value:function(e){console.log("批量操作:",e),alert("批量".concat(e,"功能待实现"))}},{key:"resetCategoryOrder",value:function(){console.log("重置分类顺序"),confirm("确定要重置分类顺序吗？")&&alert("重置分类顺序功能待实现")}},{key:"saveCategorySettings",value:function(){console.log("保存分类设置"),alert("保存分类设置功能待实现")}},{key:"toggleCategory",value:function(e){console.log("切换分类:",e);var t=document.getElementById("category-".concat(e));if(t){var n="none"!==t.style.display;t.style.display=n?"none":"block";var o=t.previousElementSibling.querySelector(".fa-chevron-down, .fa-chevron-up");o&&(o.classList.toggle("fa-chevron-down"),o.classList.toggle("fa-chevron-up"))}}},{key:"defineGlobalFunctions",value:function(){var e=this;console.log("🔧 开始定义全局函数..."),window.switchRole=function(t){return e.switchRole(t)},window.toggleMenuItem=function(t,n){console.log("🔄 全局toggleMenuItem被调用:",t,n);try{var o=e.toggleMenuItem(t,n);return console.log("🔄 toggleMenuItem调用结果:",o),o}catch(e){throw console.error("❌ toggleMenuItem调用出错:",e),e}},window.showPreview=function(){return e.showPreview()},window.hidePreview=function(){return e.hidePreview()},window.saveCategoryOrder=function(){return e.saveCategoryOrder()},window.editMenuItem=function(t){return console.log("✏️ 全局editMenuItem被调用:",t),e.editMenuItem(t)},window.showUnifiedPermissionMatrix=function(){return e.showUnifiedPermissionMatrix()},window.showCategoryPermissionMatrix=function(){return e.showCategoryPermissionMatrix()},window.showPermissionMatrix=function(){return e.showPermissionMatrix()},window.saveMenuOrder=function(){return e.saveMenuOrder()},window.resetOrder=function(){return e.resetOrder()},window.undoLastChange=function(){return e.undoLastChange()},window.batchOperation=function(t){return e.batchOperation(t)},window.resetCategoryOrder=function(){return e.resetCategoryOrder()},window.saveCategorySettings=function(){return e.saveCategorySettings()},window.toggleCategory=function(t){return e.toggleCategory(t)},window.toggleCategoryVisibility=function(t){return e.toggleCategoryVisibility(t)},window.toggleCategoryAccess=function(t){return e.toggleCategoryAccess(t)},console.log("🔍 验证全局函数定义:"),console.log("  window.toggleMenuItem:",n(window.toggleMenuItem)),console.log("  window.editMenuItem:",n(window.editMenuItem)),console.log("🧪 测试全局函数调用...");try{"function"==typeof window.toggleMenuItem?console.log("✅ toggleMenuItem函数可用"):console.error("❌ toggleMenuItem函数不可用"),"function"==typeof window.editMenuItem?console.log("✅ editMenuItem函数可用"):console.error("❌ editMenuItem函数不可用")}catch(e){console.error("❌ 全局函数测试失败:",e)}console.log("✅ 全局函数已定义")}},{key:"destroy",value:function(){this.sortableInstances.forEach((function(e){e&&e.destroy&&e.destroy()})),this.isInitialized=!1,this.sortableInstances=[],this.currentRoleId=null,this.currentRoleName="",this.isAdminRole=!1,console.log("菜单管理页面模块已销毁")}}],t&&c(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,i,r,l,s,u,d,f,v,p}();window.MenuManagementPage=new s,t.A=s},9292:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,i(o.key),o)}}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var r=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("实验室类型页面已初始化，跳过重复初始化"):(console.log("🧪 初始化实验室类型页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化实验室类型页面功能"),this.initBasicFeatures(),this.initFormHandling(),this.initTableActions(),this.defineGlobalFunctions(),console.log("✅ 实验室类型页面功能初始化完成")}},{key:"initBasicFeatures",value:function(){window.$&&$.fn.tooltip&&$('[data-toggle="tooltip"]').tooltip(),window.$&&$.fn.modal&&$(".modal").modal({backdrop:"static",keyboard:!1})}},{key:"initFormHandling",value:function(){var e=this;document.querySelectorAll("form").forEach((function(t){t.addEventListener("submit",(function(n){if(!e.validateForm(t))return n.preventDefault(),!1;e.showSubmitLoading(t)}))}))}},{key:"validateForm",value:function(e){var t=this,n=e.querySelectorAll("[required]"),o=!0;return n.forEach((function(e){e.value.trim()||(t.showFieldError(e,"此字段为必填项"),o=!1)})),o}},{key:"showFieldError",value:function(e,t){var n=e.parentNode.querySelector(".field-error");n&&n.remove(),e.classList.add("is-invalid");var o=document.createElement("div");o.className="field-error text-danger small",o.textContent=t,e.parentNode.appendChild(o)}},{key:"showSubmitLoading",value:function(e){var t=e.querySelector('[type="submit"]');t&&(t.disabled=!0,t.innerHTML='<i class="fas fa-spinner fa-spin"></i> 提交中...')}},{key:"initTableActions",value:function(){var e=this;document.addEventListener("click",(function(t){t.target.classList.contains("btn-edit")?e.handleEdit(t.target):t.target.classList.contains("btn-delete")&&e.handleDelete(t.target)}))}},{key:"handleEdit",value:function(e){var t=e.getAttribute("data-id");t&&console.log("编辑实验室类型:",t)}},{key:"handleDelete",value:function(e){var t=e.getAttribute("data-id");t&&confirm("确定要删除这个实验室类型吗？")&&console.log("删除实验室类型:",t)}},{key:"getCSRFToken",value:function(){var e,t=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");return t||console.warn("无法获取CSRF令牌"),t}},{key:"defineGlobalFunctions",value:function(){}},{key:"destroy",value:function(){this.isInitialized=!1,console.log("实验室类型页面模块已销毁")}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();window.LabTypesPage=new r,t.A=r},9886:function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function c(n,o,r,a){var c=o&&o.prototype instanceof s?o:s,u=Object.create(c.prototype);return i(u,"_invoke",function(n,o,i){var r,a,c,s=0,u=i||[],d=!1,f={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return r=t,a=0,c=e,f.n=n,l}};function v(n,o){for(a=n,c=o,t=0;!d&&s&&!i&&t<u.length;t++){var i,r=u[t],v=f.p,p=r[2];n>3?(i=p===o)&&(a=r[4]||3,c=r[5]===e?r[3]:r[5],r[4]=3,r[5]=e):r[0]<=v&&((i=n<2&&v<r[1])?(a=0,f.v=o,f.n=r[1]):v<p&&(i=n<3||r[0]>o||o>p)&&(r[4]=n,r[5]=o,f.n=p,a=0))}if(i||n>1)return l;throw d=!0,o}return function(i,u,p){if(s>1)throw TypeError("Generator is already running");for(d&&1===u&&v(u,p),a=u,c=p;(t=a<2?e:c)||!d;){r||(a?a<3?(a>1&&(f.n=-1),v(a,c)):f.n=c:f.v=c);try{if(s=2,r){if(a||(i="next"),t=r[i]){if(!(t=t.call(r,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,a<2&&(a=0)}else 1===a&&(t=r.return)&&t.call(r),a<2&&(c=TypeError("The iterator does not provide a '"+i+"' method"),a=1);r=e}else if((t=(d=f.n<0)?c:n.call(o,f))!==l)break}catch(t){r=e,a=1,c=t}finally{s=1}}return{value:t,done:d}}}(n,r,a),!0),u}var l={};function s(){}function u(){}function d(){}t=Object.getPrototypeOf;var f=[][r]?t(t([][r]())):(i(t={},r,(function(){return this})),t),v=d.prototype=s.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,i(e,a,"GeneratorFunction")),e.prototype=Object.create(v),e}return u.prototype=d,i(v,"constructor",d),i(d,"constructor",u),u.displayName="GeneratorFunction",i(d,a,"GeneratorFunction"),i(v),i(v,a,"Generator"),i(v,r,(function(){return this})),i(v,"toString",(function(){return"[object Generator]"})),(o=function(){return{w:c,m:p}})()}function i(e,t,n,o){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}i=function(e,t,n,o){if(t)r?r(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{function a(t,n){i(e,t,(function(e){return this._invoke(t,n,e)}))}a("next",0),a("throw",1),a("return",2)}},i(e,t,n,o)}function r(e,t,n,o,i,r,a){try{var c=e[r](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(o,i)}function a(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,c(o.key),o)}}function c(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var l=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.dataLoaded=!1,this.currentSort={column:"",direction:"asc"}},t=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("实验室管理页面已初始化，跳过重复初始化"):(console.log("🏢 初始化实验室管理页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化实验室管理页面功能"),this.checkOperationPermissions(),this.dataLoaded=!0,this.initSortingFeature(),this.initSearchFeature(),this.initPropertyDropdown(),this.initRiskLevelColors(),this.initLabSelection(),this.initVisibilityChange(),this.initActionButtons(),this.defineGlobalFunctions(),console.log("✅ 实验室管理页面功能初始化完成")}},{key:"initSortingFeature",value:function(){var e=this,t=document.querySelectorAll(".sort-icon");t.forEach((function(n){n.style.cursor="pointer",n.style.userSelect="none",n.addEventListener("click",(function(n){var o=n.target.dataset.sort;e.currentSort.column===o?e.currentSort.direction="asc"===e.currentSort.direction?"desc":"asc":(e.currentSort.column=o,e.currentSort.direction="asc"),t.forEach((function(e){return e.textContent="⇅"})),n.target.textContent="asc"===e.currentSort.direction?"↑":"↓",e.sortTable(o,e.currentSort.direction)}))}))}},{key:"sortTable",value:function(e,t){var n=document.getElementById("laboratoriesTable");if(n){var o=n.querySelector("tbody"),i=Array.from(o.querySelectorAll("tr"));i.sort((function(n,o){var i,r,a,c,l,s,u={id:1,campus:2,building:3,department:4,room:5,name:6,type:7,risk:8,manager:9,capacity:11,property:12}[e]||1;return"id"===e||"capacity"===e?(i=parseInt(null===(a=n.children[u])||void 0===a?void 0:a.textContent.trim())||0,r=parseInt(null===(c=o.children[u])||void 0===c?void 0:c.textContent.trim())||0):(i=(null===(l=n.children[u])||void 0===l?void 0:l.textContent.trim().toLowerCase())||"",r=(null===(s=o.children[u])||void 0===s?void 0:s.textContent.trim().toLowerCase())||""),i<r?"asc"===t?-1:1:i>r?"asc"===t?1:-1:0})),i.forEach((function(e){return o.appendChild(e)}))}}},{key:"initSearchFeature",value:function(){var e=this,t=document.getElementById("search_campus_id");t&&t.addEventListener("change",(function(t){var n=t.target.value;n&&e.loadBuildingsForCampus(n)}));var n=document.getElementById("reset");n&&n.addEventListener("click",(function(t){t.preventDefault();var n=document.querySelector("form");n&&(n.reset(),document.querySelectorAll('input[name="property_ids"]').forEach((function(e){return e.checked=!1})),e.updatePropertyDropdownText(),n.submit())}))}},{key:"loadBuildingsForCampus",value:(n=o().m((function e(t){var n,i,r,a,c;return o().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,n="/admin/get_buildings_for_search/".concat(t),e.n=1,fetch(n);case 1:return i=e.v,e.n=2,i.json();case 2:r=e.v,(a=document.getElementById("search_building_id"))&&(a.innerHTML="",r.forEach((function(e){var t=document.createElement("option");t.value=e[0],t.textContent=e[1],a.appendChild(t)}))),e.n=4;break;case 3:e.p=3,c=e.v,console.error("加载楼宇选项失败:",c);case 4:return e.a(2)}}),e,null,[[0,3]])})),i=function(){var e=this,t=arguments;return new Promise((function(o,i){var a=n.apply(e,t);function c(e){r(a,o,i,c,l,"next",e)}function l(e){r(a,o,i,c,l,"throw",e)}c(void 0)}))},function(e){return i.apply(this,arguments)})},{key:"initPropertyDropdown",value:function(){var e=this;document.querySelectorAll(".property-dropdown-menu, .property-items-wrapper").forEach((function(e){e.addEventListener("click",(function(e){return e.stopPropagation()}))}));var t=document.getElementById("selectAllProperties");t&&t.addEventListener("change",(function(t){var n=t.target.checked;document.querySelectorAll('input[name="property_ids"]').forEach((function(e){return e.checked=n})),e.updatePropertyDropdownText()}));var n=document.querySelectorAll('input[name="property_ids"]');n.forEach((function(o){o.addEventListener("change",(function(){var o=n.length,i=document.querySelectorAll('input[name="property_ids"]:checked').length;t&&(t.checked=i>0&&i===o),e.updatePropertyDropdownText()}))})),this.updatePropertyDropdownText(),document.querySelectorAll(".property-dropdown-menu .checkbox").forEach((function(e){e.addEventListener("click",(function(e){return e.stopPropagation()}))}))}},{key:"updatePropertyDropdownText",value:function(){var e=document.querySelectorAll('input[name="property_ids"]:checked'),t=document.getElementById("propertyDropdown"),n=null==t?void 0:t.querySelector(".selected-text");n&&(0===e.length?(n.textContent="-- 请选择属性 --",t.classList.remove("btn-has-selection")):1===e.length?(n.textContent=e[0].parentElement.textContent.trim(),t.classList.add("btn-has-selection")):(n.textContent="已选择 ".concat(e.length," 项"),t.classList.add("btn-has-selection")))}},{key:"initRiskLevelColors",value:function(){document.querySelectorAll("#risk_level option").forEach((function(e){var t=e.value,n={red:"#ffcccc",orange:"#ffddcc",yellow:"#ffffcc",blue:"#ccccff"};n[t]&&(e.style.backgroundColor=n[t])})),document.querySelectorAll("table tbody tr").forEach((function(e){var t=e.children[8];if(t){var n=t.textContent.trim(),o={"红":"#ffcccc","橙":"#ffddcc","黄":"#ffffcc","蓝":"#ccccff"};Object.keys(o).forEach((function(e){n.includes(e)&&(t.style.backgroundColor=o[e])}))}}))}},{key:"initLabSelection",value:function(){var e=this,t=document.getElementById("selectAllLabs");t&&t.addEventListener("change",(function(t){var n=t.target.checked;document.querySelectorAll(".lab-checkbox").forEach((function(e){return e.checked=n})),e.updateGenerateButtonState()}));var n=document.querySelectorAll(".lab-checkbox");n.forEach((function(o){o.addEventListener("change",(function(){e.updateGenerateButtonState();var o=document.querySelectorAll(".lab-checkbox:checked").length===n.length;t&&(t.checked=o)}))}));var o=document.getElementById("generateInfoCardBtn");o&&o.addEventListener("click",(function(){var e=[];document.querySelectorAll(".lab-checkbox:checked").forEach((function(t){e.push(t.dataset.id)})),e.length>0?1===e.length?window.location.href="/admin/laboratory_info_card/".concat(e[0]):window.location.href="/admin/batch_generate_info_card?lab_ids=".concat(e.join(",")):alert("请至少选择一个实验室")})),this.updateGenerateButtonState()}},{key:"updateGenerateButtonState",value:function(){var e=document.querySelectorAll(".lab-checkbox:checked").length,t=document.getElementById("generateInfoCardBtn");t&&(t.disabled=0===e,t.textContent=e>0?"批量生成实验室信息牌 (".concat(e,")"):"批量生成实验室信息牌")}},{key:"initVisibilityChange",value:function(){var e=this;document.addEventListener("visibilitychange",(function(){"visible"===document.visibilityState&&(console.log("页面变为可见，检查数据加载状态"),e.checkAndReloadData())}))}},{key:"initActionButtons",value:function(){document.addEventListener("click",(function(e){e.target.closest(".hover-action-container")||document.querySelectorAll(".hover-action-container.expanded").forEach((function(e){e.classList.remove("expanded")}))})),console.log("✅ 操作按钮事件监听器已设置")}},{key:"checkOperationPermissions",value:function(){console.log("权限已在服务器端预加载，无需AJAX检查")}},{key:"checkAndReloadData",value:function(){console.log("检查是否需要重新加载数据"),this.dataLoaded?console.log("数据已加载，无需重新加载"):(console.log("标记数据为已加载"),this.dataLoaded=!0)}},{key:"defineGlobalFunctions",value:function(){var e=this;window.loadLaboratoriesData=function(){console.log("全局函数loadLaboratoriesData被调用"),e.checkOperationPermissions(),e.dataLoaded=!0},window.toggleActionButtons=function(e){console.log("🔄 toggleActionButtons被调用 (实验室页面)"),event&&event.stopPropagation(),document.querySelectorAll(".hover-action-container.expanded").forEach((function(t){t!==e&&t.classList.remove("expanded")})),e.classList.toggle("expanded"),console.log("✅ 操作按钮状态已切换")},console.log("✅ toggleActionButtons函数已定义 (实验室页面)")}},{key:"destroy",value:function(){this.isInitialized=!1,this.dataLoaded=!1,console.log("实验室管理页面模块已销毁")}}],t&&a(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,i}();window.LaboratoriesPage=new l,t.A=l}},n={};function o(e){var i=n[e];if(void 0!==i)return i.exports;var r=n[e]={exports:{}};return t[e](r,r.exports,o),r.exports}o.m=t,e=[],o.O=function(t,n,i,r){if(!n){var a=1/0;for(u=0;u<e.length;u++){n=e[u][0],i=e[u][1],r=e[u][2];for(var c=!0,l=0;l<n.length;l++)(!1&r||a>=r)&&Object.keys(o.O).every((function(e){return o.O[e](n[l])}))?n.splice(l--,1):(c=!1,r<a&&(a=r));if(c){e.splice(u--,1);var s=i();void 0!==s&&(t=s)}}return t}r=r||0;for(var u=e.length;u>0&&e[u-1][2]>r;u--)e[u]=e[u-1];e[u]=[n,i,r]},o.d=function(e,t){for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.j=3524,function(){var e={268:0,1038:0,2343:0,2429:0,3276:0,3514:0,3524:0,3681:0,4052:0,4784:0,5229:0,5524:0,6070:0,6822:0,7228:0,7624:0,7922:0,8301:0,8692:0,8809:0,9638:0,9922:0,9988:0};o.O.j=function(t){return 0===e[t]};var t=function(t,n){var i,r,a=n[0],c=n[1],l=n[2],s=0;if(a.some((function(t){return 0!==e[t]}))){for(i in c)o.o(c,i)&&(o.m[i]=c[i]);if(l)var u=l(o)}for(t&&t(n);s<a.length;s++)r=a[s],o.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return o.O(u)},n=self.webpackChunklab_safety_frontend=self.webpackChunklab_safety_frontend||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))}();var i=o.O(void 0,[2076],(function(){return o(7367)}));i=o.O(i)}();
//# sourceMappingURL=app.f78412eecf34bacdb3c8.js.map