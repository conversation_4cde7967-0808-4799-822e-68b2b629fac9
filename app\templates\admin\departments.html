{% extends "base.html" %}

{% block title %}部门管理 - 实验室安全检查系统{% endblock %}

{% block content %}
<div class="container">
    <h1>部门管理</h1>
    <div class="mb-3">
        {% if current_user.is_admin() %}
        <a href="{{ url_for('admin.add_department') }}" class="btn btn-success">新建部门</a>
        {% endif %}
    </div>

    <!-- 搜索表单 -->
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">部门搜索</h3>
        </div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">部门名称</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="searchName" placeholder="输入部门名称">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">部门代码</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="searchCode" placeholder="输入部门代码">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <div class="col-sm-offset-2 col-sm-10">
                                <button type="button" id="searchBtn" class="btn btn-primary">搜索</button>
                                <button type="button" id="resetBtn" class="btn btn-default">重置</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover" id="departmentsTable">
            <thead>
                <tr>
                    <th>ID <span class="sort-icon" data-sort="id">⇅</span></th>
                    <th>部门名称 <span class="sort-icon" data-sort="name">⇅</span></th>
                    <th>部门代码 <span class="sort-icon" data-sort="code">⇅</span></th>
                    <th>描述</th>
                    <th>关联实验室数量 <span class="sort-icon" data-sort="labs">⇅</span></th>
                    {% if current_user.is_admin() %}
                    <th>操作</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for dept in departments %}
                <tr>
                    <td>{{ dept.id }}</td>
                    <td>{{ dept.name }}</td>
                    <td>{{ dept.code }}</td>
                    <td>{{ dept.description }}</td>
                    <td>{{ dept.laboratories.count() }}</td>
                    {% if current_user.is_admin() %}
                    <td>
                        <a href="{{ url_for('admin.edit_department', id=dept.id) }}"
                            class="btn btn-sm btn-primary">编辑</a>
                        <a href="javascript:void(0)" class="btn btn-sm btn-danger delete-with-captcha"
                            data-url="{{ url_for('admin.delete_department', id=dept.id) }}" data-title="确认删除部门"
                            data-message="确定要删除部门 &quot;{{ dept.name }}&quot; 吗？此操作不可恢复！">删除</a>
                    </td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<!-- 引入departments.html页面专用模块化JavaScript -->
{% for js_url in webpack_js('departments') %}
<script src="{{ js_url }}"></script>
{% endfor %}
<script>
    // 页面加载完成后初始化departments.html功能
    document.addEventListener('DOMContentLoaded', function() {
        if (window.DepartmentsPage) {
            window.DepartmentsPage.init();
        }
    });

    // AJAX加载时重新初始化
    if (window.UnifiedAjaxMenu) {
        window.UnifiedAjaxMenu.registerPageInitializer('/admin/departments', function() {
            if (window.DepartmentsPage) {
                window.DepartmentsPage.init();
            }
        });
    }
</script>
{% endblock %}