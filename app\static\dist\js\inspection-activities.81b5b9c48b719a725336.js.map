{"version": 3, "file": "js/inspection-activities.81b5b9c48b719a725336.js", "mappings": ";4QACA,IAAAA,EAAAC,EAAAC,EAAA,mBAAAC,OAAAA,OAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,aAAA,yBAAAC,EAAAN,EAAAE,EAAAE,EAAAE,GAAA,IAAAC,EAAAL,GAAAA,EAAAM,qBAAAC,EAAAP,EAAAO,EAAAC,EAAAC,OAAAC,OAAAL,EAAAC,WAAA,OAAAK,EAAAH,EAAA,mBAAAV,EAAAE,EAAAE,GAAA,IAAAE,EAAAC,EAAAG,EAAAI,EAAA,EAAAC,EAAAX,GAAA,GAAAY,GAAA,EAAAC,EAAA,CAAAF,EAAA,EAAAb,EAAA,EAAAgB,EAAApB,EAAAqB,EAAAC,EAAAN,EAAAM,EAAAC,KAAAvB,EAAA,GAAAsB,EAAA,SAAArB,EAAAC,GAAA,OAAAM,EAAAP,EAAAQ,EAAA,EAAAG,EAAAZ,EAAAmB,EAAAf,EAAAF,EAAAmB,CAAA,YAAAC,EAAApB,EAAAE,GAAA,IAAAK,EAAAP,EAAAU,EAAAR,EAAAH,EAAA,GAAAiB,GAAAF,IAAAV,GAAAL,EAAAgB,EAAAO,OAAAvB,IAAA,KAAAK,EAAAE,EAAAS,EAAAhB,GAAAqB,EAAAH,EAAAF,EAAAQ,EAAAjB,EAAA,GAAAN,EAAA,GAAAI,EAAAmB,IAAArB,KAAAK,EAAAD,EAAA,MAAAI,EAAAJ,EAAA,KAAAR,EAAAQ,EAAA,GAAAA,EAAA,GAAAA,EAAA,KAAAA,EAAA,GAAAR,GAAAQ,EAAA,IAAAc,KAAAhB,EAAAJ,EAAA,GAAAoB,EAAAd,EAAA,KAAAC,EAAA,EAAAU,EAAAC,EAAAhB,EAAAe,EAAAf,EAAAI,EAAA,IAAAc,EAAAG,IAAAnB,EAAAJ,EAAA,GAAAM,EAAA,GAAAJ,GAAAA,EAAAqB,KAAAjB,EAAA,GAAAN,EAAAM,EAAA,GAAAJ,EAAAe,EAAAf,EAAAqB,EAAAhB,EAAA,OAAAH,GAAAJ,EAAA,SAAAmB,EAAA,MAAAH,GAAA,EAAAd,CAAA,iBAAAE,EAAAW,EAAAQ,GAAA,GAAAT,EAAA,QAAAU,UAAA,oCAAAR,GAAA,IAAAD,GAAAK,EAAAL,EAAAQ,GAAAhB,EAAAQ,EAAAL,EAAAa,GAAAxB,EAAAQ,EAAA,EAAAT,EAAAY,KAAAM,GAAA,CAAAV,IAAAC,EAAAA,EAAA,GAAAA,EAAA,IAAAU,EAAAf,GAAA,GAAAkB,EAAAb,EAAAG,IAAAO,EAAAf,EAAAQ,EAAAO,EAAAC,EAAAR,GAAA,OAAAI,EAAA,EAAAR,EAAA,IAAAC,IAAAH,EAAA,QAAAL,EAAAO,EAAAF,GAAA,MAAAL,EAAAA,EAAA0B,KAAAnB,EAAAI,IAAA,MAAAc,UAAA,wCAAAzB,EAAA2B,KAAA,OAAA3B,EAAAW,EAAAX,EAAA4B,MAAApB,EAAA,IAAAA,EAAA,YAAAA,IAAAR,EAAAO,EAAAsB,SAAA7B,EAAA0B,KAAAnB,GAAAC,EAAA,IAAAG,EAAAc,UAAA,oCAAApB,EAAA,YAAAG,EAAA,GAAAD,EAAAR,CAAA,UAAAC,GAAAiB,EAAAC,EAAAf,EAAA,GAAAQ,EAAAV,EAAAyB,KAAAvB,EAAAe,MAAAE,EAAA,YAAApB,GAAAO,EAAAR,EAAAS,EAAA,EAAAG,EAAAX,CAAA,SAAAe,EAAA,UAAAa,MAAA5B,EAAA2B,KAAAV,EAAA,GAAAhB,EAAAI,EAAAE,IAAA,GAAAI,CAAA,KAAAS,EAAA,YAAAV,IAAA,UAAAoB,IAAA,UAAAC,IAAA,CAAA/B,EAAAY,OAAAoB,eAAA,IAAAxB,EAAA,GAAAL,GAAAH,EAAAA,EAAA,GAAAG,QAAAW,EAAAd,EAAA,GAAAG,GAAA,0BAAAH,GAAAW,EAAAoB,EAAAtB,UAAAC,EAAAD,UAAAG,OAAAC,OAAAL,GAAA,SAAAO,EAAAhB,GAAA,OAAAa,OAAAqB,eAAArB,OAAAqB,eAAAlC,EAAAgC,IAAAhC,EAAAmC,UAAAH,EAAAjB,EAAAf,EAAAM,EAAA,sBAAAN,EAAAU,UAAAG,OAAAC,OAAAF,GAAAZ,CAAA,QAAA+B,EAAArB,UAAAsB,EAAAjB,EAAAH,EAAA,cAAAoB,GAAAjB,EAAAiB,EAAA,cAAAD,GAAAA,EAAAK,YAAA,oBAAArB,EAAAiB,EAAA1B,EAAA,qBAAAS,EAAAH,GAAAG,EAAAH,EAAAN,EAAA,aAAAS,EAAAH,EAAAR,GAAA,0BAAAW,EAAAH,EAAA,sDAAAyB,EAAA,kBAAAC,EAAA9B,EAAA+B,EAAAvB,EAAA,cAAAD,EAAAf,EAAAE,EAAAE,EAAAH,GAAA,IAAAO,EAAAK,OAAA2B,eAAA,IAAAhC,EAAA,gBAAAR,GAAAQ,EAAA,EAAAO,EAAA,SAAAf,EAAAE,EAAAE,EAAAH,GAAA,GAAAC,EAAAM,EAAAA,EAAAR,EAAAE,EAAA,CAAA2B,MAAAzB,EAAAqC,YAAAxC,EAAAyC,cAAAzC,EAAA0C,UAAA1C,IAAAD,EAAAE,GAAAE,MAAA,UAAAE,EAAAJ,EAAAE,GAAAW,EAAAf,EAAAE,GAAA,SAAAF,GAAA,YAAA4C,QAAA1C,EAAAE,EAAAJ,EAAA,IAAAM,EAAA,UAAAA,EAAA,WAAAA,EAAA,cAAAS,EAAAf,EAAAE,EAAAE,EAAAH,EAAA,UAAA4C,EAAAzC,EAAAH,EAAAD,EAAAE,EAAAI,EAAAe,EAAAZ,GAAA,QAAAD,EAAAJ,EAAAiB,GAAAZ,GAAAG,EAAAJ,EAAAqB,KAAA,OAAAzB,GAAA,YAAAJ,EAAAI,EAAA,CAAAI,EAAAoB,KAAA3B,EAAAW,GAAAkC,QAAAC,QAAAnC,GAAAoC,KAAA9C,EAAAI,EAAA,UAAA2C,EAAAjD,EAAAE,GAAA,QAAAD,EAAA,EAAAA,EAAAC,EAAAsB,OAAAvB,IAAA,KAAAK,EAAAJ,EAAAD,GAAAK,EAAAmC,WAAAnC,EAAAmC,aAAA,EAAAnC,EAAAoC,cAAA,YAAApC,IAAAA,EAAAqC,UAAA,GAAA9B,OAAA2B,eAAAxC,EAAAkD,EAAA5C,EAAA6C,KAAA7C,EAAA,WAAA4C,EAAAjD,GAAA,IAAAO,EAAA,SAAAP,GAAA,aAAAmD,EAAAnD,KAAAA,EAAA,OAAAA,EAAA,IAAAD,EAAAC,EAAAE,OAAAkD,aAAA,YAAArD,EAAA,KAAAQ,EAAAR,EAAA2B,KAAA1B,EAAAC,UAAA,aAAAkD,EAAA5C,GAAA,OAAAA,EAAA,UAAAkB,UAAA,uDAAA4B,OAAArD,EAAA,CAAAsD,CAAAtD,GAAA,gBAAAmD,EAAA5C,GAAAA,EAAAA,EAAA,GADA,IAGMgD,EAAwB,WAM5B,OARFxD,EAGE,SAAAwD,KAHF,SAAAnC,EAAAjB,GAAA,KAAAiB,aAAAjB,GAAA,UAAAsB,UAAA,qCAGgB+B,CAAA,KAAAD,GACZE,KAAKC,eAAgB,EACrBD,KAAKE,YAAa,CACpB,EANF1D,EAQE,EAAAiD,IAAA,OAAAtB,MAGA,WAAO,IAAAgC,EAAA,KACDH,KAAKC,cACPG,QAAQC,IAAI,yBAIdD,QAAQC,IAAI,kBAGgB,YAAxBC,SAASC,WACXD,SAASE,iBAAiB,oBAAoB,kBAAML,EAAKM,oBAAoB,IAE7ET,KAAKS,qBAGPT,KAAKC,eAAgB,EACvB,GAEA,CAAAR,IAAA,qBAAAtB,MAGA,WACEiC,QAAQC,IAAI,oBAGZL,KAAKU,sBACLV,KAAKW,oBACLX,KAAKY,iBACLZ,KAAKa,kBACLb,KAAKc,oBAGLd,KAAKe,wBAELX,QAAQC,IAAI,oBACd,GAEA,CAAAZ,IAAA,sBAAAtB,MAGA,WAEE6C,OAAOC,uBAAyB,SAACC,GAC3BC,QAAQ,kBACVC,MAAM,gCAADC,OAAiCH,EAAU,SAAS,CACvDI,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,cAAejB,SAASkB,cAAc,yBAAyBC,aAAa,cAG/EnC,MAAK,SAAAoC,GAAQ,OAAIA,EAASC,MAAM,IAChCrC,MAAK,SAAAsC,GACAA,EAAKC,SACPC,MAAM,WACNC,SAASC,UAETF,MAAM,QAAUF,EAAKK,QAEzB,IACCC,OAAM,SAAAC,GACL/B,QAAQ+B,MAAM,SAAUA,GACxBL,MAAM,OACR,GAEJ,EAGAd,OAAOoB,6BAA+B,SAAClB,GACjCC,QAAQ,oBACVC,MAAM,gCAADC,OAAiCH,EAAU,eAAe,CAC7DI,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,cAAejB,SAASkB,cAAc,yBAAyBC,aAAa,cAG/EnC,MAAK,SAAAoC,GAAQ,OAAIA,EAASC,MAAM,IAChCrC,MAAK,SAAAsC,GACAA,EAAKC,SACPC,MAAM,aACNC,SAASC,UAETF,MAAM,UAAYF,EAAKK,QAE3B,IACCC,OAAM,SAAAC,GACL/B,QAAQ+B,MAAM,SAAUA,GACxBL,MAAM,SACR,GAEJ,EAEA1B,QAAQC,IAAI,iBACZD,QAAQC,IAAI,kBACd,GAEA,CAAAZ,IAAA,oBAAAtB,MAGA,WAEE6C,OAAOqB,cAAgB,SAACnB,GACtB,GAAIC,QAAQ,wBAAyB,KAAAmB,EAC7BC,EAAOjC,SAASkC,cAAc,QACpCD,EAAKjB,OAAS,OACdiB,EAAKE,OAAS,yBAAHpB,OAA4BH,GAGvC,IAAMwB,EAA6D,QAApDJ,EAAGhC,SAASkB,cAAc,kCAA0B,IAAAc,OAAA,EAAjDA,EAAmDb,aAAa,WAClF,GAAIiB,EAAW,CACb,IAAMC,EAAYrC,SAASkC,cAAc,SACzCG,EAAUC,KAAO,SACjBD,EAAUE,KAAO,aACjBF,EAAUxE,MAAQuE,EAClBH,EAAKO,YAAYH,EACnB,CAEArC,SAASyC,KAAKD,YAAYP,GAC1BA,EAAKS,QACP,CACF,EAGAhC,OAAOiC,gBAAkB,SAAC/B,GACxB,GAAIC,QAAQ,iBAAkB,KAAA+B,EACtBX,EAAOjC,SAASkC,cAAc,QACpCD,EAAKjB,OAAS,OACdiB,EAAKE,OAAS,2BAAHpB,OAA8BH,GAGzC,IAAMwB,EAA6D,QAApDQ,EAAG5C,SAASkB,cAAc,kCAA0B,IAAA0B,OAAA,EAAjDA,EAAmDzB,aAAa,WAClF,GAAIiB,EAAW,CACb,IAAMC,EAAYrC,SAASkC,cAAc,SACzCG,EAAUC,KAAO,SACjBD,EAAUE,KAAO,aACjBF,EAAUxE,MAAQuE,EAClBH,EAAKO,YAAYH,EACnB,CAEArC,SAASyC,KAAKD,YAAYP,GAC1BA,EAAKS,QACP,CACF,EAGAhC,OAAOmC,kBAAoB,SAACjC,GAC1B,GAAIC,QAAQ,yBAA0B,KAAAiC,EAC9Bb,EAAOjC,SAASkC,cAAc,QACpCD,EAAKjB,OAAS,OACdiB,EAAKE,OAAS,8BAAHpB,OAAiCH,GAG5C,IAAMwB,EAA6D,QAApDU,EAAG9C,SAASkB,cAAc,kCAA0B,IAAA4B,OAAA,EAAjDA,EAAmD3B,aAAa,WAClF,GAAIiB,EAAW,CACb,IAAMC,EAAYrC,SAASkC,cAAc,SACzCG,EAAUC,KAAO,SACjBD,EAAUE,KAAO,aACjBF,EAAUxE,MAAQuE,EAClBH,EAAKO,YAAYH,EACnB,CAEArC,SAASyC,KAAKD,YAAYP,GAC1BA,EAAKS,QACP,CACF,CACF,GAEA,CAAAvD,IAAA,iBAAAtB,MAGA,WAEE6B,KAAKqD,kBACP,GAEA,CAAA5D,IAAA,mBAAAtB,OA3LFzB,EA2LEiC,IAAAE,GAGA,SAAAyE,IAAA,IAAA5B,EAAAE,EAAA2B,EAAAC,EAAAC,EAAA,OAAA9E,IAAAC,GAAA,SAAA8E,GAAA,cAAAA,EAAAhH,GAAA,cAAAgH,EAAAnG,EAAA,EAAAmG,EAAAhH,EAAA,EAE2B0E,MAAM,gCAA+B,OAA9C,OAARM,EAAQgC,EAAAhG,EAAAgG,EAAAhH,EAAG,EACEgF,EAASC,OAAM,OAA5BC,EAAI8B,EAAAhG,GAEJ6F,EAAsBjD,SAASqD,eAAe,kBACzB/B,EAAKgC,MAAQ,GACtCL,EAAoBM,YAAcjC,EAAKgC,MACvCL,EAAoBO,MAAMC,QAAU,UAC3BR,IACTA,EAAoBO,MAAMC,QAAU,QACrCL,EAAAhH,EAAA,eAAAgH,EAAAnG,EAAA,EAAAkG,EAAAC,EAAAhG,EAED0C,QAAQ+B,MAAM,aAAYsB,IACpBF,EAAsBjD,SAASqD,eAAe,mBAElDJ,EAAoBO,MAAMC,QAAU,QACrC,cAAAL,EAAA/F,EAAA,MAAA2F,EAAA,iBApBLU,EA3LF,eAAAzH,EAAA,KAAAD,EAAA2H,UAAA,WAAA7E,SAAA,SAAA5C,EAAAI,GAAA,IAAAe,EAAAjB,EAAAwH,MAAA3H,EAAAD,GAAA,SAAA6H,EAAAzH,GAAAyC,EAAAxB,EAAAnB,EAAAI,EAAAuH,EAAAC,EAAA,OAAA1H,EAAA,UAAA0H,EAAA1H,GAAAyC,EAAAxB,EAAAnB,EAAAI,EAAAuH,EAAAC,EAAA,QAAA1H,EAAA,CAAAyH,OAAA,OAiNG,WAnBqB,OAAAH,EAAAE,MAAC,KAADD,UAAA,IAqBtB,CAAAxE,IAAA,kBAAAtB,MAGA,WACE6B,KAAKE,YAAa,EAClBE,QAAQC,IAAI,iBACd,GAEA,CAAAZ,IAAA,oBAAAtB,MAGA,WAEE6C,OAAOqD,oBAAsB,SAACC,GAC5BlE,QAAQC,IAAI,6BAGRkE,OACFA,MAAMC,kBAIRlE,SAASmE,iBAAiB,oCAAoCC,SAAQ,SAASC,GACzEA,IAASL,GACXK,EAAKC,UAAUC,OAAO,WAE1B,IAGAP,EAAUM,UAAUE,OAAO,YAE3B1E,QAAQC,IAAI,cACd,EAEAD,QAAQC,IAAI,6BACd,GAEA,CAAAZ,IAAA,wBAAAtB,MAGA,SAAsB+C,EAAY6D,EAAcC,GAC9C,GAAIA,EAEF,OADAlD,MAAM,0CACC,EAGT,IAAMmD,EAAiB,aAAH5D,OAAgB0D,EAAY,kCAEhD,GAAI5D,QAAQ8D,GAAiB,KAAAC,EAErB3C,EAAOjC,SAASkC,cAAc,QACpCD,EAAKjB,OAAS,OACdiB,EAAKE,OAAS,gCAAHpB,OAAmCH,EAAU,WAGxD,IAAMwB,EAA6D,QAApDwC,EAAG5E,SAASkB,cAAc,kCAA0B,IAAA0D,OAAA,EAAjDA,EAAmDzD,aAAa,WAClF,GAAIiB,EAAW,CACb,IAAMC,EAAYrC,SAASkC,cAAc,SACzCG,EAAUC,KAAO,SACjBD,EAAUE,KAAO,aACjBF,EAAUxE,MAAQuE,EAClBH,EAAKO,YAAYH,EACnB,CAIA,OAFArC,SAASyC,KAAKD,YAAYP,GAC1BA,EAAKS,UACE,CACT,CAEA,OAAO,CACT,GAEA,CAAAvD,IAAA,wBAAAtB,MAGA,WAAwB,IAAAgH,EAAA,KACtB/E,QAAQC,IAAI,mBAGZW,OAAOoE,sBAAwBpF,KAAKoF,sBAAsBvH,KAAKmC,MAC/DgB,OAAOqB,cAAgBrB,OAAOqB,eAAkB,kBAAMjC,QAAQiF,KAAK,4BAA4B,EAC/FrE,OAAOiC,gBAAkBjC,OAAOiC,iBAAoB,kBAAM7C,QAAQiF,KAAK,8BAA8B,EACrGrE,OAAOmC,kBAAoBnC,OAAOmC,mBAAsB,kBAAM/C,QAAQiF,KAAK,gCAAgC,EAG3GrE,OAAOsE,6BAA+B,WACpClF,QAAQC,IAAI,uCACZ8E,EAAKtE,iBACP,EAGKG,OAAOqD,sBACVjE,QAAQiF,KAAK,mCACbrF,KAAKc,qBAGPV,QAAQC,IAAI,mBACd,GAIA,CAAAZ,IAAA,UAAAtB,MAGA,WACE6B,KAAKC,eAAgB,EACrBD,KAAKE,YAAa,EAClBE,QAAQC,IAAI,gBACd,IA/TF7D,GAAA+C,EAAAjD,EAAAU,UAAAR,GAAAW,OAAA2B,eAAAxC,EAAA,aAAA2C,UAAA,IAAA3C,EAAA,IAAAA,EAAAE,EAAAE,EA2LEsH,CAoIC,CA7T2B,GAiU9BhD,OAAOlB,yBAA2B,IAAIA", "sources": ["webpack://lab-safety-frontend/./src/js/pages/inspection-activities.js"], "sourcesContent": ["/**\n * 检查活动管理页面模块\n */\nclass InspectionActivitiesPage {\n  constructor() {\n    this.isInitialized = false;\n    this.dataLoaded = false;\n  }\n\n  /**\n   * 初始化检查活动管理页面\n   */\n  init() {\n    if (this.isInitialized) {\n      console.log('检查活动管理页面已初始化，跳过重复初始化');\n      return;\n    }\n\n    console.log('🔍 初始化检查活动管理页面');\n\n    // 等待DOM准备就绪\n    if (document.readyState === 'loading') {\n      document.addEventListener('DOMContentLoaded', () => this.initializeFeatures());\n    } else {\n      this.initializeFeatures();\n    }\n\n    this.isInitialized = true;\n  }\n\n  /**\n   * 初始化页面功能\n   */\n  initializeFeatures() {\n    console.log('🔧 初始化检查活动管理页面功能');\n\n    // 初始化各种功能\n    this.initActivityActions();\n    this.initReportActions();\n    this.initRecycleBin();\n    this.initDataLoading();\n    this.initActionButtons();\n\n    // 定义全局函数（兼容性）\n    this.defineGlobalFunctions();\n\n    console.log('✅ 检查活动管理页面功能初始化完成');\n  }\n\n  /**\n   * 初始化活动操作功能\n   */\n  initActivityActions() {\n    // 停止检查活动函数\n    window.stopInspectionActivity = (activityId) => {\n      if (confirm('确定要停止这个检查活动吗？')) {\n        fetch(`/admin/inspection_activities/${activityId}/stop`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')\n          }\n        })\n        .then(response => response.json())\n        .then(data => {\n          if (data.success) {\n            alert('检查活动已停止');\n            location.reload();\n          } else {\n            alert('停止失败：' + data.message);\n          }\n        })\n        .catch(error => {\n          console.error('Error:', error);\n          alert('停止失败');\n        });\n      }\n    };\n\n    // 重新激活检查活动函数\n    window.reactivateInspectionActivity = (activityId) => {\n      if (confirm('确定要重新激活这个检查活动吗？')) {\n        fetch(`/admin/inspection_activities/${activityId}/reactivate`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')\n          }\n        })\n        .then(response => response.json())\n        .then(data => {\n          if (data.success) {\n            alert('检查活动已重新激活');\n            location.reload();\n          } else {\n            alert('重新激活失败：' + data.message);\n          }\n        })\n        .catch(error => {\n          console.error('Error:', error);\n          alert('重新激活失败');\n        });\n      }\n    };\n\n    console.log('✅ 停止检查活动函数已定义');\n    console.log('✅ 重新激活检查活动函数已定义');\n  }\n\n  /**\n   * 初始化报告操作功能\n   */\n  initReportActions() {\n    // 发布通报\n    window.publishReport = (activityId) => {\n      if (confirm('确定要发布这个通报吗？发布后将无法修改。')) {\n        const form = document.createElement('form');\n        form.method = 'POST';\n        form.action = `/admin/publish_report/${activityId}`;\n        \n        // 添加CSRF token\n        const csrfToken = document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content');\n        if (csrfToken) {\n          const csrfInput = document.createElement('input');\n          csrfInput.type = 'hidden';\n          csrfInput.name = 'csrf_token';\n          csrfInput.value = csrfToken;\n          form.appendChild(csrfInput);\n        }\n        \n        document.body.appendChild(form);\n        form.submit();\n      }\n    };\n\n    // 取消发布通报\n    window.unpublishReport = (activityId) => {\n      if (confirm('确定要取消发布这个通报吗？')) {\n        const form = document.createElement('form');\n        form.method = 'POST';\n        form.action = `/admin/unpublish_report/${activityId}`;\n        \n        // 添加CSRF token\n        const csrfToken = document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content');\n        if (csrfToken) {\n          const csrfInput = document.createElement('input');\n          csrfInput.type = 'hidden';\n          csrfInput.name = 'csrf_token';\n          csrfInput.value = csrfToken;\n          form.appendChild(csrfInput);\n        }\n        \n        document.body.appendChild(form);\n        form.submit();\n      }\n    };\n\n    // 删除通报草稿\n    window.deleteReportDraft = (activityId) => {\n      if (confirm('确定要删除这个通报草稿吗？此操作不可恢复。')) {\n        const form = document.createElement('form');\n        form.method = 'POST';\n        form.action = `/admin/delete_report_draft/${activityId}`;\n        \n        // 添加CSRF token\n        const csrfToken = document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content');\n        if (csrfToken) {\n          const csrfInput = document.createElement('input');\n          csrfInput.type = 'hidden';\n          csrfInput.name = 'csrf_token';\n          csrfInput.value = csrfToken;\n          form.appendChild(csrfInput);\n        }\n        \n        document.body.appendChild(form);\n        form.submit();\n      }\n    };\n  }\n\n  /**\n   * 初始化回收站功能\n   */\n  initRecycleBin() {\n    // 加载回收站计数\n    this.loadRecycleCount();\n  }\n\n  /**\n   * 加载回收站计数\n   */\n  async loadRecycleCount() {\n    try {\n      const response = await fetch('/admin/api/recycle_bin/count');\n      const data = await response.json();\n      \n      const recycleCountElement = document.getElementById('recycleCount');\n      if (recycleCountElement && data.count > 0) {\n        recycleCountElement.textContent = data.count;\n        recycleCountElement.style.display = 'inline';\n      } else if (recycleCountElement) {\n        recycleCountElement.style.display = 'none';\n      }\n    } catch (error) {\n      console.error('加载回收站计数失败:', error);\n      const recycleCountElement = document.getElementById('recycleCount');\n      if (recycleCountElement) {\n        recycleCountElement.style.display = 'none';\n      }\n    }\n  }\n\n  /**\n   * 初始化数据加载\n   */\n  initDataLoading() {\n    this.dataLoaded = true;\n    console.log('检查活动数据已标记为加载完成');\n  }\n\n  /**\n   * 初始化操作按钮功能\n   */\n  initActionButtons() {\n    // 定义toggleActionButtons函数\n    window.toggleActionButtons = (container) => {\n      console.log('🔄 toggleActionButtons被调用');\n\n      // 阻止事件冒泡\n      if (event) {\n        event.stopPropagation();\n      }\n\n      // 先关闭其他已展开的按钮\n      document.querySelectorAll('.hover-action-container.expanded').forEach(function(item) {\n        if (item !== container) {\n          item.classList.remove('expanded');\n        }\n      });\n\n      // 切换当前容器的展开状态\n      container.classList.toggle('expanded');\n\n      console.log('✅ 操作按钮状态已切换');\n    };\n\n    console.log('✅ toggleActionButtons函数已定义');\n  }\n\n  /**\n   * 删除确认逻辑\n   */\n  confirmDeleteActivity(activityId, activityName, hasPublishedReport) {\n    if (hasPublishedReport) {\n      alert('该活动已发布通报，无法直接删除。\\n\\n请先取消发布通报，然后再删除活动。');\n      return false;\n    }\n\n    const confirmMessage = `确定要删除检查活动\"${activityName}\"吗？\\n\\n删除后该活动将移入回收站，可以在回收站中恢复。`;\n    \n    if (confirm(confirmMessage)) {\n      // 创建表单并提交删除请求\n      const form = document.createElement('form');\n      form.method = 'POST';\n      form.action = `/admin/inspection_activities/${activityId}/delete`;\n      \n      // 添加CSRF token\n      const csrfToken = document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content');\n      if (csrfToken) {\n        const csrfInput = document.createElement('input');\n        csrfInput.type = 'hidden';\n        csrfInput.name = 'csrf_token';\n        csrfInput.value = csrfToken;\n        form.appendChild(csrfInput);\n      }\n      \n      document.body.appendChild(form);\n      form.submit();\n      return true;\n    }\n    \n    return false;\n  }\n\n  /**\n   * 定义全局函数（兼容性）\n   */\n  defineGlobalFunctions() {\n    console.log('🔧 定义检查活动页面全局函数');\n\n    // 确保全局函数可用\n    window.confirmDeleteActivity = this.confirmDeleteActivity.bind(this);\n    window.publishReport = window.publishReport || (() => console.warn('publishReport not defined'));\n    window.unpublishReport = window.unpublishReport || (() => console.warn('unpublishReport not defined'));\n    window.deleteReportDraft = window.deleteReportDraft || (() => console.warn('deleteReportDraft not defined'));\n\n    // 数据加载函数\n    window.loadInspectionActivitiesData = () => {\n      console.log('全局函数loadInspectionActivitiesData被调用');\n      this.initDataLoading();\n    };\n\n    // 确保toggleActionButtons函数可用（防御性编程）\n    if (!window.toggleActionButtons) {\n      console.warn('⚠️ toggleActionButtons未定义，重新初始化');\n      this.initActionButtons();\n    }\n\n    console.log('✅ 检查活动页面全局函数定义完成');\n  }\n\n\n\n  /**\n   * 销毁检查活动管理页面模块\n   */\n  destroy() {\n    this.isInitialized = false;\n    this.dataLoaded = false;\n    console.log('检查活动管理页面模块已销毁');\n  }\n}\n\n// 创建全局实例\nwindow.InspectionActivitiesPage = new InspectionActivitiesPage();\n\nexport default InspectionActivitiesPage;\n"], "names": ["e", "t", "r", "Symbol", "n", "iterator", "o", "toStringTag", "i", "c", "prototype", "Generator", "u", "Object", "create", "_regeneratorDefine2", "f", "p", "y", "G", "v", "a", "d", "bind", "length", "l", "TypeError", "call", "done", "value", "return", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "setPrototypeOf", "__proto__", "displayName", "_regenerator", "w", "m", "defineProperty", "enumerable", "configurable", "writable", "_invoke", "asyncGeneratorStep", "Promise", "resolve", "then", "_defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_typeof", "toPrimitive", "String", "_toPrimitive", "InspectionActivitiesPage", "_classCallCheck", "this", "isInitialized", "dataLoaded", "_this", "console", "log", "document", "readyState", "addEventListener", "initializeFeatures", "initActivityActions", "initReportActions", "initRecycleBin", "initDataLoading", "initActionButtons", "defineGlobalFunctions", "window", "stopInspectionActivity", "activityId", "confirm", "fetch", "concat", "method", "headers", "querySelector", "getAttribute", "response", "json", "data", "success", "alert", "location", "reload", "message", "catch", "error", "reactivateInspectionActivity", "publishReport", "_document$querySelect", "form", "createElement", "action", "csrfToken", "csrfInput", "type", "name", "append<PERSON><PERSON><PERSON>", "body", "submit", "unpublishReport", "_document$querySelect2", "deleteReportDraft", "_document$querySelect3", "loadRecycleCount", "_callee", "recycleCountElement", "_recycleCountElement", "_t", "_context", "getElementById", "count", "textContent", "style", "display", "_loadRecycleCount", "arguments", "apply", "_next", "_throw", "toggleActionButtons", "container", "event", "stopPropagation", "querySelectorAll", "for<PERSON>ach", "item", "classList", "remove", "toggle", "activityName", "hasPublishedReport", "confirmMessage", "_document$querySelect4", "_this2", "confirmDeleteActivity", "warn", "loadInspectionActivitiesData"], "sourceRoot": ""}