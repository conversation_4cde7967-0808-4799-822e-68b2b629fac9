/**
 * 主应用样式文件
 * 包含全局样式和组件样式
 * 备份时间: 2025.1.6 - 删除重复样式前备份
 */

/* ========== 全局 Z-INDEX 层级规范 ========== */
/*
 * 统一的z-index层级管理，避免层级冲突
 *
 * 层级分配：
 * 1-99:     基础元素（工具提示、下拉菜单等）
 * 100-199:  浮动元素（固定导航、侧边栏等）
 * 200-299:  弹出层（下拉菜单、选择器等）
 * 300-399:  覆盖层（加载遮罩、通知等）
 * 400-499:  模态框（对话框、弹窗等）
 * 500-999:  特殊用途（调试、开发工具等）
 * 1000+:    紧急修复（临时使用，需要重构）
 */

:root {
    /* 基础层级 */
    --z-tooltip: 10;
    --z-dropdown: 20;
    --z-sticky: 30;

    /* 浮动层级 */
    --z-sidebar: 100;
    --z-navbar: 110;
    --z-floating: 120;

    /* 弹出层级 */
    --z-popover: 200;
    --z-dropdown-menu: 1000;
    --z-select: 1010;

    /* 覆盖层级 */
    --z-overlay: 300;
    --z-loading: 310;
    --z-notification: 320;

    /* 模态框层级 */
    --z-modal-backdrop: 400;
    --z-modal: 410;
    --z-modal-content: 420;

    /* 特殊层级 */
    --z-drag: 999;
    --z-datepicker: 1000;
    --z-preview: 1000;
    --z-loading-overlay: 2000;
    --z-debug: 9000;
    --z-emergency: 9999;
}

/* 全局样式重置 */
* {
    box-sizing: border-box;
}

/* ========== 点击展开操作按钮样式 ========== */
.hover-action-container {
    display: block; /* 改为块级元素，占满单元格宽度 */
    width: 100%;
}

/* 默认显示的触发按钮 */
.action-trigger {
    display: block;
}

.action-trigger .btn {
    padding: 4px 8px !important;
    font-size: 12px !important;
    line-height: 1.3 !important;
    min-height: 26px !important;
    cursor: pointer;
}

/* 点击时显示的具体按钮 */
.action-buttons {
    display: none;
    position: static; /* 改为静态定位，保持在单元格内 */
    white-space: normal; /* 允许换行 */
    background: rgba(248, 249, 250, 0.9);
    padding: 3px;
    border-radius: 3px;
    border: 1px solid #ddd;
    margin: 2px auto 0 auto; /* 上边距2px，左右自动居中 */
    width: -moz-fit-content;
    width: fit-content; /* 宽度自适应内容 */
    min-width: auto; /* 最小宽度自动 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    text-align: center; /* 内容居中对齐 */
}

.action-buttons .btn {
    margin-right: 2px;
    margin-bottom: 1px;
    padding: 3px 6px !important;
    font-size: 11px !important;
    line-height: 1.2 !important;
    min-height: 24px !important;
}

/* 点击展开的显示逻辑 */
.hover-action-container.expanded .action-trigger {
    display: none;
}

.hover-action-container.expanded .action-buttons {
    display: block !important; /* 改为块级显示，允许按钮换行 */
}

/* 按钮在容器内的布局 */
.action-buttons .btn {
    margin: 1px; /* 按钮之间的间距 */
    display: inline-block; /* 内联块级元素，可以换行 */
    padding: 3px 6px !important;
    font-size: 11px !important;
    line-height: 1.2 !important;
    min-height: 24px !important;
}

/* ========== 表格列宽自适应样式 ========== */
.auto-width-table {
    table-layout: auto !important; /* 自动调整列宽 */
    width: 100%;
}

.auto-width-table th,
.auto-width-table td {
    width: auto !important; /* 根据内容自动调整 */
    white-space: nowrap; /* 防止文字换行 */
}

/* 特定列的宽度控制 */
.col-id {
    width: 60px !important; /* ID列固定宽度 */
}

.col-status {
    width: 80px !important; /* 状态列固定宽度 */
}

.col-date {
    width: 120px !important; /* 日期列固定宽度 */
}

.col-actions {
    width: 200px !important; /* 操作列固定宽度 */
    text-align: center;
}

.col-name {
    min-width: 150px; /* 名称列最小宽度 */
    max-width: 300px; /* 名称列最大宽度 */
    white-space: normal; /* 允许名称换行 */
    word-break: break-word; /* 长单词换行 */
}

/* 加载状态 */
.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 统计卡片样式 */
.stat-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* 表格增强样式 */
.data-table {
    width: 100%;
    margin-bottom: 20px;
}

.table-controls {
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.table-search {
    max-width: 300px;
    flex: 1;
}

.table-pagination {
    margin-top: 15px;
    text-align: center;
}

.table-pagination .btn {
    margin: 0 2px;
}

.batch-actions {
    display: none;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.batch-actions .selected-count {
    font-weight: bold;
    color: #007bff;
}

/* 文件上传样式 */
.file-preview {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.file-preview-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f9f9f9;
}

.file-preview-item img {
    margin-bottom: 5px;
}

.file-preview-item span {
    font-size: 12px;
    text-align: center;
    word-break: break-all;
}

/* 表单验证样式 */
.form-group.has-error .form-control {
    border-color: #dc3545;
}

.form-group.has-error .help-block {
    color: #dc3545;
}

.is-invalid {
    border-color: #dc3545 !important;
}

.is-valid {
    border-color: #28a745 !important;
}

/* 模态框增强样式 */
.modal-content {
    border-radius: 6px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-bottom: 1px solid #e5e5e5;
    padding: 15px 20px;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    border-top: 1px solid #e5e5e5;
    padding: 15px 20px;
    text-align: right;
}

.modal-footer .btn {
    margin-left: 5px;
}

/* 在线用户样式 */
.online-users-count {
    cursor: pointer;
    transition: color 0.3s ease;
}

.online-users-count:hover {
    color: #007bff;
    -webkit-text-decoration: underline;
    text-decoration: underline;
}

/* 侧边栏基础样式 */
.sidebar {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 220px !important;
    height: 100vh !important;
    background-color: #2e6da4 !important;
    color: white !important;
    z-index: 100 !important;
    z-index: var(--z-sidebar) !important;
    overflow-y: auto !important;
    /* 强制显示滚动条，避免菜单展开/折叠时的震动 */
    min-height: 100vh;
    /* 确保侧边栏始终有足够高度 */
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

/* 侧边栏头部样式 */
.sidebar-header {
    padding: 15px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header img {
    max-width: 100px;
    max-height: 100px;
    margin: 0 auto;
    display: block;
}

.sidebar-title {
    font-weight: bold;
    margin: 10px 0;
    color: white;
    font-size: 18px;
}

/* 导航菜单样式 */
.nav-sidebar {
    margin-top: 20px;
    padding: 0;
    list-style: none;
}

.nav-sidebar li {
    margin: 0;
    padding: 0;
}

.nav-sidebar a {
    display: block;
    padding: 12px 20px;
    color: white !important;
    -webkit-text-decoration: none;
    text-decoration: none;
    transition: background-color 0.3s;
    border: none;
}

.nav-sidebar a:hover,
.nav-sidebar a.active {
    background-color: #1e4d72 !important;
    color: white !important;
    -webkit-text-decoration: none;
    text-decoration: none;
    border-left: 4px solid #fff;
}

.nav-sidebar i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* 预设菜单最小高度，避免第一次展开时的滚动条闪烁 */
.sidebar .collapse {
    min-height: 0 !important;
    /* 重置Bootstrap默认的min-height */
}

.sidebar .collapse.collapsing {
    overflow: hidden !important;
    /* 动画过程中隐藏溢出内容 */
    min-height: 0 !important;
}

.sidebar .collapse.in {
    overflow: visible !important;
    /* 展开后显示所有内容 */
}

.main-content {
    margin-left: 220px !important;
    /* 相应调整主内容区域的左边距 */
}

/* 防止页面整体滚动条造成的震动 */
html {
    overflow-y: scroll !important;
    /* 强制显示页面滚动条 */
}

body {
    overflow-x: hidden;
    /* 隐藏水平滚动条 */
}

.sidebar-collapsed .sidebar {
    width: 60px;
}

.sidebar-collapsed .sidebar .nav-label {
    display: none;
}

.sidebar-collapsed .sidebar .nav-icon {
    text-align: center;
    width: 100%;
}

/* 响应式设计已移除 */

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

/* ========== 搜索结果模态框样式 - 全局通用 ========== */
/* 搜索模态框高度限制和滚动 - 确保不遮挡页面底部 */
body .search-modal.modal .modal-dialog {
    max-height: 75vh !important;
    margin: 20px auto !important;
    margin-bottom: 120px !important; /* 确保底部有足够空间 */
}

body .search-modal.modal .modal-content {
    max-height: 70vh !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
}

body .search-modal.modal .modal-header {
    flex-shrink: 0 !important;
    padding: 10px 15px !important;
}

body .search-modal.modal .modal-body {
    max-height: 50vh !important;
    overflow-y: auto !important;
    flex: 1 !important;
    padding: 10px !important;
}

body .search-modal.modal .modal-footer {
    flex-shrink: 0 !important;
    padding: 10px 15px !important;
}

/* 搜索模态框表格容器样式 */
body .search-modal.modal .table-responsive {
    max-height: 40vh !important;
    overflow-y: auto !important;
    border: 1px solid #ddd !important;
    border-radius: 4px;
}

/* 确保搜索结果表格有滚动条 */
body .search-modal.modal .table-responsive table {
    margin-bottom: 0 !important;
}

/* 搜索模态框表格样式优化 */
.search-modal .table th {
    background-color: #f5f5f5;
    font-weight: bold;
    border-bottom: 2px solid #ddd;
    position: sticky;
    top: 0;
    /* z-index: 10; - 取消低优先级z-index */
}

.search-modal .table tbody tr:hover {
    background-color: #f9f9f9;
}

/* 搜索模态框按钮样式 */
.search-modal .btn {
    margin: 2px;
}

/* 搜索模态框复选框样式 */
.search-modal .table input[type="checkbox"] {
    transform: scale(1.2);
}

/* 搜索模态框响应式调整已移除 */

/* 确保模态框不会超出视口 */
.search-modal.modal {
    overflow-y: auto !important;
}

.search-modal.modal .modal-dialog {
    position: relative !important;
    top: 0 !important;
    transform: none !important;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 工具提示样式 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: #555;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    /* z-index: 1; - 取消低优先级z-index */
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* ========== 实验室管理员主页样式 ========== */
/* 实验室管理员主页数据卡片样式 */
.lab-admin-stat-card {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.lab-admin-stat-card:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
    transform: translateY(-2px);
}

.lab-admin-stat-card .icon {
    position: absolute;
    top: 0;
    left: 0;
    width: 80px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
}

.lab-admin-stat-card .number {
    font-size: 36px;
    font-weight: bold;
    margin-left: 90px;
    padding-top: 15px;
    padding-right: 15px;
}

.lab-admin-stat-card .title {
    margin-left: 90px;
    padding-bottom: 15px;
    color: #777;
}

/* 实验室管理员主页面板样式 */
.lab-admin-panel {
    margin-bottom: 20px;
    background-color: #fff;
    border: 1px solid transparent;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.lab-admin-panel-heading {
    padding: 10px 15px;
    border-bottom: 1px solid transparent;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.lab-admin-panel-title {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
}

.lab-admin-panel-body {
    padding: 15px;
}

/* 实验室管理员主页待办事项样式 */
.lab-admin-todo-item {
    padding: 10px;
    margin-bottom: 10px;
    border-left: 4px solid #f0ad4e;
    background-color: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.lab-admin-todo-item.overdue {
    border-left-color: #d9534f;
}

.lab-admin-todo-item-content {
    flex-grow: 1;
}

.lab-admin-todo-item-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.lab-admin-todo-item-date {
    font-size: 12px;
    color: #777;
}

.lab-admin-todo-item-action {
    margin-left: 10px;
}

/* 实验室管理员主页快捷入口样式 */
.lab-admin-shortcut-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.lab-admin-shortcut-item {
    flex: 1;
    min-width: 150px;
    text-align: center;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    transition: all 0.3s ease;
}

.lab-admin-shortcut-item:hover {
    background-color: #e9ecef;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
    transform: translateY(-2px);
}

.lab-admin-shortcut-icon {
    font-size: 24px;
    margin-bottom: 10px;
}

.lab-admin-shortcut-title {
    font-weight: bold;
}

/* 实验室管理员主页实验室卡片样式 */
.lab-admin-lab-card {
    margin-bottom: 15px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    transition: all 0.3s ease;
}

.lab-admin-lab-card:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
    transform: translateY(-2px);
}

.lab-admin-lab-card .panel-heading {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.lab-admin-lab-card .panel-body {
    padding: 15px;
}

.lab-admin-lab-card .btn-group {
    margin-top: 10px;
}

/* 实验室管理员主页公告样式 */
.lab-admin-announcement {
    padding: 10px;
    margin-bottom: 10px;
    border-left: 4px solid #5bc0de;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
}

.lab-admin-announcement:hover {
    background-color: #e9ecef;
    border-left-color: #31b0d5;
}

.lab-admin-announcement-title {
    font-weight: bold;
    margin-bottom: 5px;
    color: #337ab7;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.lab-admin-announcement-date {
    font-size: 12px;
    color: #777;
    margin-bottom: 5px;
}

.lab-admin-announcement-content {
    color: #333;
    display: none;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #ddd;
}

.lab-admin-announcement.expanded .lab-admin-announcement-content {
    display: block;
}

/* 实验室管理员主页旧样式合并 */
.lab-admin-lab-card .panel-footer {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    background-color: #f9f9f9;
}

.lab-admin-status-normal {
    color: #5cb85c;
}

.lab-admin-status-warning {
    color: #f0ad4e;
}

.lab-admin-status-danger {
    color: #d9534f;
}

.lab-admin-task-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.lab-admin-task-item:last-child {
    border-bottom: none;
}

.lab-admin-task-item .label {
    margin-right: 5px;
}

.lab-admin-task-item .deadline {
    color: #777;
    font-size: 0.9em;
}

.lab-admin-task-item .overdue {
    color: #d9534f;
    font-weight: bold;
}

/* ========== 统计分析页面样式 ========== */
/* 统计分析首页样式 */
.stats-card {
    margin-bottom: 20px;
    transition: transform 0.3s;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #007bff;
}

.card-title {
    font-size: 1.2rem;
    font-weight: bold;
}

.card-description {
    color: #6c757d;
    margin-top: 10px;
}

/* 按指标项统计页面样式 */
.search-box {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

/* 基础表格响应式容器样式 */
.table-responsive {
    margin-top: 20px;
}

.nav-tabs {
    margin-bottom: 15px;
}

.btn-export {
    margin-left: 10px;
}

/* ========== 检查记录页面样式 ========== */
/* 检查记录过滤区域样式 */
.filter-section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.result-tab {
    margin-bottom: 20px;
}

.result-tab .nav-tabs {
    margin-bottom: 0;
}

.result-tab .tab-content {
    padding: 15px;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    border-radius: 0 0 4px 4px;
}

.sort-icon {
    margin-left: 5px;
}

.batch-actions {
    margin-bottom: 10px;
}

/* ========== 首页时间选择器样式 ========== */
/* 首页时间范围选择器样式 */
.time-range-selector-container {
    margin: 15px 0 20px 0;
    display: flex;
    align-items: center;
}

.time-range-label {
    margin-right: 10px;
    font-weight: bold;
}

.time-range-selector .btn.active {
    background-color: #337ab7;
    color: white;
    border-color: #2e6da4;
}

/* 按钮增强样式 */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn:active {
    transform: translateY(0);
}

/* 卡片样式 */
.card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* 徽章样式 */
.badge {
    font-size: 0.75em;
    font-weight: 700;
    padding: 0.25rem 0.5rem;
    border-radius: 0.35rem;
}

/* 进度条样式 */
.progress {
    height: 1rem;
    border-radius: 0.35rem;
    background-color: #eaecf4;
}

.progress-bar {
    border-radius: 0.35rem;
    transition: width 0.6s ease;
}

/* 列表组样式 */
.list-group-item {
    border: 1px solid #e3e6f0;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: #f8f9fc;
}

/* 导航样式 */
.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.35rem;
    border-top-right-radius: 0.35rem;
}

.nav-tabs .nav-link.active {
    background-color: #fff;
    border-color: #e3e6f0 #e3e6f0 #fff;
}

/* ========== 首页专用样式 ========== */
/* 页面标题区域 - 保持蓝色主题 */
.page-header-section {
    background: #2e6da4;
    color: white;
    padding: 20px 25px;
    border-radius: 4px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(46, 109, 164, 0.2);
}

.page-header-title {
    font-size: 22px;
    font-weight: 500;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-header-subtitle {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 0;
}

.current-time {
    font-size: 13px;
    opacity: 0.9;
    background: rgba(255, 255, 255, 0.15);
    padding: 5px 10px;
    border-radius: 3px;
}

/* 实时状态指示器 */
.status-indicators {
    display: flex;
    gap: 15px;
    margin-top: 10px;
}

.status-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    opacity: 0.9;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-online {
    background: #5cb85c;
}

.status-warning {
    background: #f0ad4e;
}

.status-error {
    background: #d9534f;
}

/* 改进数据卡片样式 - 保持原有色调 */
.improved-stat-card {
    background: white;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.improved-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.stat-icon-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.stat-trend {
    font-size: 11px;
    padding: 3px 8px;
    border-radius: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 3px;
}

.trend-up {
    background: #d4edda;
    color: #155724;
}

.trend-down {
    background: #f8d7da;
    color: #721c24;
}

.trend-stable {
    background: #e2e3e5;
    color: #383d41;
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
    line-height: 1;
}

.stat-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 0;
    font-weight: 500;
}

/* 最新动态样式 */
.activity-item {
    padding: 12px 0;
    border-bottom: 1px solid #f1f1f1;
    display: flex;
    align-items: center;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 14px;
    color: white;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-size: 13px;
    color: #333;
    margin-bottom: 2px;
    font-weight: 500;
}

.activity-time {
    font-size: 11px;
    color: #777;
}

/* 改进面板样式 - 保持原有风格 */
.panel {
    margin-bottom: 20px;
    background-color: #fff;
    border: 1px solid transparent;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    transition: all 0.3s ease;
}

.panel:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
    transform: translateY(-1px);
}

.panel-heading {
    padding: 15px 20px;
    border-bottom: 1px solid transparent;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.panel-title i {
    margin-right: 8px;
}

.panel-body {
    padding: 20px;
}

/* 待办事项样式 */
.todo-item {
    padding: 12px 15px;
    margin-bottom: 10px;
    border-left: 4px solid #f0ad4e;
    background-color: #f8f9fa;
    border-radius: 0 4px 4px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.todo-item:hover {
    background-color: #e9ecef;
    transform: translateX(3px);
}

.todo-item.overdue {
    border-left-color: #d9534f;
}

.todo-item.completed {
    border-left-color: #5cb85c;
    opacity: 0.7;
}

.todo-item-content {
    flex-grow: 1;
}

.todo-item-title {
    font-weight: 500;
    margin-bottom: 3px;
    font-size: 13px;
    color: #333;
}

.todo-item-date {
    font-size: 11px;
    color: #777;
}

.todo-item-action {
    margin-left: 10px;
}

/* 公告样式 */
.announcement {
    padding: 10px;
    margin-bottom: 10px;
    border-left: 4px solid #5bc0de;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
}

.announcement:hover {
    background-color: #e9ecef;
    border-left-color: #31b0d5;
}

.announcement-title {
    font-weight: bold;
    margin-bottom: 5px;
    color: #337ab7;
}

.announcement-date {
    font-size: 12px;
    color: #777;
    margin-bottom: 5px;
}

.announcement-content {
    color: #333;
    display: none;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #ddd;
}

.announcement.expanded .announcement-content {
    display: block;
}

/* 改进快捷入口样式 - 保持原有风格 */
.shortcut-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.shortcut-item {
    flex: 1;
    min-width: 120px;
    text-align: center;
    padding: 15px 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    transition: all 0.3s ease;
    -webkit-text-decoration: none;
    text-decoration: none;
    color: #333;
    border: 1px solid #e9ecef;
}

.shortcut-item:hover {
    background-color: #e9ecef;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
    transform: translateY(-2px);
    -webkit-text-decoration: none;
    text-decoration: none;
    color: #333;
    border-color: #dee2e6;
}

.shortcut-icon {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
}

.shortcut-title {
    font-weight: 500;
    font-size: 13px;
    line-height: 1.3;
}

.shortcut-badge {
    display: inline-block;
    min-width: 18px;
    padding: 3px 6px;
    font-size: 11px;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    background-color: #d9534f;
    border-radius: 10px;
    margin-left: 5px;
}

/* ========== base 页面样式 ========== */
.footer {
    margin-top: 30px;
    padding: 15px 0;
    background-color: #f5f5f5;
    border-top: 1px solid #ddd;
}

@media print {
    .no-print {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }

    body {
        background-color: white !important;
    }
}

/* 菜单展开样式 - Bootstrap 3兼容，强制优先级 */
.sidebar .collapse.in {
    display: block !important;
}

.sidebar a[data-toggle="collapse"] .fa-angle-down {
    transition: transform 0.4s ease-in-out;
}

/* Bootstrap collapse动画优化 - 统一动画时间 */
.sidebar .collapsing {
    transition: height 0.35s ease-in-out !important;
}

/* 快速折叠动画 - 用于菜单切换时的关闭动画 */
.sidebar .collapse.fast-collapse.collapsing {
    transition: height 0.25s ease-in-out !important;
}

/* 🚫 DISABLED: 确保菜单动画同步 - 与Bootstrap冲突，导致双重动画 */
/*
        .sidebar .collapse.in {
            animation: slideDown 0.4s ease-in-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
            }

            to {
                opacity: 1;
                max-height: 500px;
            }
        }
        */

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
            }

            to {
                opacity: 1;
                max-height: 500px;
            }
        }

.sidebar .collapse {
    position: relative;
    /* z-index: 1; - 取消低优先级z-index */
    /* 🚫 DISABLED: transition: all 0.4s ease-in-out; - 与Bootstrap冲突 */
    overflow: hidden;
    height: auto;
}

/* 添加菜单展开/折叠动画 - 兼容Bootstrap 3和4+ */
.sidebar .collapse:not(.show):not(.in) {
    display: none;
}

/* ========== base-backup 页面样式 ========== */
.footer {
    margin-top: 30px;
    padding: 15px 0;
    background-color: #f5f5f5;
    border-top: 1px solid #ddd;
}

@media print {
    .no-print {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }

    body {
        background-color: white !important;
    }
}

/* 子菜单样式 */
.sidebar .collapse .collapse {
    padding-left: 15px;
}

.copyright {
    margin: 0;
    color: #777;
    font-size: 14px;
}

@media print {
    .no-print {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }

    body {
        background-color: white !important;
    }
}

/* 菜单展开样式 */
.collapse.show {
    display: block;
}

.sidebar .collapse {
    padding-left: 15px;
}

.sidebar a[data-toggle="collapse"] .fa-angle-down {
    transition: transform 0.3s;
}

.sidebar a[data-toggle="collapse"][aria-expanded="true"] .fa-angle-down,
.sidebar a[data-toggle="collapse"].active .fa-angle-down {
    transform: rotate(180deg);
}

/* 防止菜单重叠 - 改进版 */
.sidebar li {
    position: relative;
}

.sidebar .collapse {
    position: relative;
    /* z-index: 1; - 取消低优先级z-index */
    transition: all 0.3s ease-out;
    overflow: hidden;
    height: auto;
}

/* 确保子菜单在父菜单内部 */
.sidebar .collapse .collapse {
    position: relative;
    /* z-index: 2; - 取消低优先级z-index */
}

/* 添加菜单展开/折叠动画 */
.sidebar .collapse:not(.show) {
    display: none;
}

/* 防止菜单重叠的额外样式 */
.sidebar .nav-sidebar>li {
    clear: both;
    margin-bottom: 2px;
    width: 100%;
}

/* 确保菜单项不会被遮挡 */
.sidebar a {
    position: relative;
    /* z-index: 3; - 取消低优先级z-index */
}

/* ========== admin-activity-report-preview 页面样式 ========== */
.report-preview {
    background-color: #fff;
    padding: 30px;
    border: 1px solid #ddd;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.report-preview h1 {
    font-size: 24px;
    margin-bottom: 30px;
}

.report-preview h2 {
    font-size: 18px;
    margin-top: 25px;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

/* ========== admin-all-announcements 页面样式 ========== */

/* 公告卡片样式 */
.announcement-card {
    margin-bottom: 20px;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    transition: all 0.3s ease;
}

.announcement-card:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
    transform: translateY(-2px);
}

.card-header {
    padding: 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
    position: relative;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.card-title {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
    padding-right: 100px;
    display: flex;
    align-items: center;
}

.card-body {
    padding: 15px;
}

.card-footer {
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-top: 1px solid #ddd;
    font-size: 12px;
    color: #666;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
}

.meta-info {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.priority-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    margin-left: 10px;
    color: white;
}

.priority-high {
    background-color: #dc3545;
}

.priority-medium {
    background-color: #fd7e14;
}

.priority-low {
    background-color: #28a745;
}

/* 删除重复的announcement-date样式定义，避免绝对定位导致的叠加问题 */

/* 筛选区域样式 */
.filter-section {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-section .form-group {
    margin-bottom: 0;
}

/* 响应式调整 */

/* ========== admin-announcements 页面样式 ========== */

.announcement-card .card-header {
    padding: 10px 15px;
    border-bottom: 1px solid #ddd;
    background-color: #f5f5f5;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.priority-high {
    background-color: #d9534f;
    color: white;
}

.priority-medium {
    background-color: #f0ad4e;
    color: white;
}

.priority-low {
    background-color: #5bc0de;
    color: white;
}

.status-badge {
    display: inline-block;
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 12px;
    margin-left: 5px;
}

.status-active {
    background-color: #5cb85c;
    color: white;
}

.status-inactive {
    background-color: #777;
    color: white;
}

.status-scheduled {
    background-color: #5bc0de;
    color: white;
}

.status-expired {
    background-color: #d9534f;
    color: white;
}

.announcement-card .card-title {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
}

.announcement-card .card-body {
    padding: 15px;
}

.announcement-card .card-footer {
    padding: 10px 15px;
    background-color: #f9f9f9;
    border-top: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.announcement-card .meta-info {
    color: #777;
    font-size: 12px;
}

.announcement-card .actions {
    display: flex;
    gap: 5px;
}

/* ========== admin-assign-indicators 页面样式 ========== */
.form-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.form-section-title {
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.indicator-list,
.personnel-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    margin-bottom: 10px;
}

.indicator-item,
.personnel-item {
    margin-bottom: 5px;
    padding: 5px;
    border-bottom: 1px solid #eee;
}

.indicator-item:last-child,
.personnel-item:last-child {
    border-bottom: none;
}

.indicator-level-1 {
    font-weight: bold;
    color: #337ab7;
}

.indicator-level-2 {
    font-weight: normal;
    color: #5cb85c;
    margin-left: 20px;
}

.indicator-level-3 {
    font-weight: normal;
    color: #5bc0de;
    margin-left: 40px;
}

.selected-indicator {
    background-color: #dff0d8;
}

.custom-assignment-section {
    display: none;
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f5f5f5;
}

.user-assignment-panel {
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
}

.user-assignment-title {
    font-weight: bold;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

.check-point-list {
    max-height: 300px;
    overflow-y: auto;
    padding: 15px;
    margin-top: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
}

/* 检查要点容器内的复选框样式 */
.check-point-list .checkbox {
    margin-bottom: 8px;
    margin-top: 0;
    padding-left: 0;
}

.check-point-list .checkbox label {
    display: block;
    width: 100%;
    margin-bottom: 0;
    padding: 5px 0;
    font-weight: normal;
    cursor: pointer;
    line-height: 1.4;
}

.check-point-list .checkbox input[type="checkbox"] {
    margin-right: 8px;
    margin-top: 2px;
    vertical-align: top;
}

.check-point-list .text-muted {
    font-size: 12px;
    color: #999;
    margin-left: 5px;
}

/* 日常巡查页面的检查要点列表样式 */
.hazard-check-point-list {
    max-height: 300px !important;
    overflow-y: auto !important;
    padding: 15px !important;
    margin-top: 10px !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    background-color: #f9f9f9 !important;
}

.hazard-check-point-list .check-points-container {
    margin: 0 !important;
}

.hazard-check-point-list .checkbox {
    margin-bottom: 8px !important;
    margin-top: 0 !important;
    padding-left: 0 !important;
}

.hazard-check-point-list .checkbox label {
    display: block !important;
    width: 100% !important;
    margin-bottom: 0 !important;
    padding: 5px 0 !important;
    font-weight: normal !important;
    cursor: pointer !important;
    line-height: 1.4 !important;
}

.hazard-check-point-list .checkbox input[type="checkbox"] {
    margin-right: 8px !important;
    margin-top: 2px !important;
    vertical-align: top !important;
}

.hazard-check-point-list .text-muted {
    font-size: 12px !important;
    color: #999 !important;
    margin-left: 5px !important;
}

.check-point-item {
    margin-bottom: 3px;
    padding: 3px;
}

.tab-content {
    padding: 15px;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
}

/* 第二个 check-point-list 样式已合并到上面 */

/* ========== admin-browse-images 页面样式 ========== */
.image-grid {
    display: -ms-grid;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    grid-gap: 15px;
    gap: 15px;
    margin-top: 20px;
}

.image-item {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    text-align: center;
    transition: all 0.3s;
}

.image-item:hover {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.image-container {
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    overflow: hidden;
}

.image-container img {
    max-width: 100%;
    max-height: 150px;
    -o-object-fit: contain;
       object-fit: contain;
}

.image-filename {
    font-size: 12px;
    color: #666;
    word-break: break-all;
    margin-bottom: 10px;
}

/* ========== admin-category-import-excel 页面样式 ========== */
.mb-3 {
    margin-bottom: 15px;
}

/* ========== admin-category-permission-matrix 页面样式 ========== */
.matrix-container {
    padding: 20px;
    overflow-x: auto;
}

.permission-matrix {
    width: 100%;
    min-width: 1200px;
    border-collapse: collapse;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.permission-matrix th,
.permission-matrix td {
    border: 1px solid #dee2e6;
    padding: 6px;
    text-align: center;
    vertical-align: middle;
    font-size: 12px;
}

.permission-matrix th {
    background: #2e6da4;
    color: white;
    font-weight: bold;
    position: sticky;
    top: 0;
    /* z-index: 10; - 取消低优先级z-index */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.role-header {
    min-width: 120px;
    max-width: 140px;
    padding: 8px 4px;
}

.role-header small {
    font-size: 0.7em;
    opacity: 0.9;
    font-weight: normal;
}

.permission-cell {
    min-width: 120px;
    padding: 4px;
    background: #f8f9fa;
}

.permission-controls {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.permission-controls>div {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
}

.permission-checkbox {
    margin: 0;
    transform: scale(0.8);
}

.permission-controls small {
    font-size: 0.7em;
    white-space: nowrap;
}

/* 权限状态颜色 */
.access-granted {
    background-color: #d4edda !important;
    border-left: 3px solid #28a745;
}

.access-denied {
    background-color: #f8d7da !important;
    border-left: 3px solid #dc3545;
}

.menu-visible {
    border-right: 3px solid #007bff;
}

.menu-hidden {
    border-right: 3px solid #6c757d;
}

.protected-cell {
    background: #fff3cd !important;
    border: 2px solid #ffc107 !important;
    position: relative;
}

.protected-cell::before {
    content: "🛡️";
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 12px;
}

/* 图例 */
.legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9em;
}

.legend-color {
    width: 20px;
    height: 15px;
    border: 1px solid #dee2e6;
    border-radius: 3px;
}

/* 过滤控制 */
.filter-controls {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
}

/* 权限矩阵页面表格容器样式 - 使用最高优先级 */
.table-responsive.permission-matrix-container.permission-matrix-container {
    max-height: 80vh !important;
    overflow: auto !important;
    overflow-x: auto !important;
    overflow-y: auto !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 5px !important;
    position: relative !important;
}

/* 确保权限矩阵表格的sticky定位正常工作 */
.permission-matrix-container .permission-matrix .menu-name {
    position: sticky !important;
    left: 0 !important;
    /* z-index: 5 !important; - 取消低优先级z-index */
    background: #ffffff !important;
}

.permission-matrix-container .permission-matrix th {
    position: sticky !important;
    top: 0 !important;
    /* z-index: 10 !important; - 取消低优先级z-index */
}

/* 批量操作控制 */
.batch-controls {
    margin-bottom: 20px;
    padding: 15px;
    background: #e9ecef;
    border-radius: 5px;
}

/* 样式控制面板 */
.style-controls {
    margin-bottom: 20px;
    padding: 15px;
    background: #f0f8ff;
    border: 1px solid #2e6da4;
    border-radius: 5px;
}

.style-controls .form-group {
    margin-bottom: 10px;
}

.style-controls label {
    font-weight: bold;
    margin-right: 10px;
    min-width: 100px;
    display: inline-block;
}

.style-controls input[type="range"] {
    width: 150px;
    margin: 0 10px;
}

.style-controls .value-display {
    font-weight: bold;
    color: #2e6da4;
    min-width: 50px;
    display: inline-block;
}

.section-title {
    font-size: 1.2em;
    font-weight: bold;
    color: #2e6da4;
    margin: 20px 0 10px 0;
    padding: 10px;
    background: #f8f9fa;
    border-left: 4px solid #2e6da4;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    z-index: var(--z-emergency);
}

.loading-spinner {
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
}

.role-stats {
    background: #e9ecef;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
    font-size: 13px;
}

/* ========== admin-check-point-batch-form 页面样式 ========== */
.check-point-content {
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow: visible;
    height: auto !important;
    min-height: 30px;
    max-height: none;
    line-height: 1.5;
    transition: height 0.2s ease;
}

.template-buttons .btn {
    margin-right: 5px;
    margin-bottom: 5px;
}

.mb-2 {
    margin-bottom: 10px;
}

.mt-4 {
    margin-top: 20px;
}

.template-buttons {
    margin-bottom: 10px;
}

/* ========== admin-check-point-form 页面样式 ========== */
.check-point-content {
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow: visible;
    height: auto !important;
    min-height: 30px;
    max-height: none;
    line-height: 1.5;
    transition: height 0.2s ease;
}

/* ========== admin-check-point-import-excel 页面样式 ========== */
.mb-3 {
    margin-bottom: 15px;
}

/* ========== admin-edit-menu-item 页面样式 ========== */
.form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.icon-preview {
    display: inline-block;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    margin-left: 10px;
}

.icon-selector {
    display: none;
    margin-top: 10px;
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    max-height: 300px;
    overflow-y: auto;
}

.icon-grid {
    display: -ms-grid;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
    grid-gap: 5px;
    gap: 5px;
}

.icon-option {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s;
}

.icon-option:hover {
    background: #007bff;
    color: white;
}

.icon-option.selected {
    background: #007bff;
    color: white;
    border-color: #0056b3;
}

.form-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 5px;
}

.form-section h4 {
    margin-bottom: 15px;
    color: #495057;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

body {
    font-family: Arial, sans-serif;
    padding: 20px;
}

.preview-item {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin: 10px 0;
    background: #f8f9fa;
}

.icon {
    margin-right: 8px;
}

.status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8em;
    margin-left: 10px;
}

/* ========== admin-hazard-detail 页面样式 ========== */
/* 图片查看相关样式 */
.hazard-image,
.rectification-image {
    transition: transform 0.2s ease;
    cursor: pointer;
}

.check-point-item {
    padding: 5px 10px;
    margin-bottom: 5px;
    background-color: #f9f9f9;
    border-left: 3px solid #337ab7;
    border-radius: 3px;
}

.check-point-item:last-child {
    margin-bottom: 0;
}

/* 流程步骤时间线样式 */
.timeline {
    position: relative;
    padding: 20px 0;
    list-style: none;
    margin-bottom: 20px;
}

.timeline:after {
    content: "";
    display: table;
    clear: both;
}

.timeline-line {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 4px;
    margin-left: -2px;
    background: #ddd;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
    clear: both;
}

.timeline-item:after {
    content: "";
    display: table;
    clear: both;
}

.timeline-content {
    position: relative;
    width: 45%;
    padding: 15px;
    border-radius: 5px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.timeline-content h4 {
    margin-top: 0;
    color: #333;
}

.timeline-marker {
    position: absolute;
    top: 15px;
    left: 50%;
    margin-left: -10px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #fff;
    border: 4px solid #ddd;
    /* z-index: 10; - 取消低优先级z-index */
}

/* 当前步骤样式 */
.timeline-item.current .timeline-content {
    background: #d9edf7;
    border-color: #bce8f1;
}

.timeline-item.current .timeline-marker {
    border-color: #31708f;
}

/* 已完成步骤样式 */
.timeline-item.completed .timeline-content {
    background: #dff0d8;
    border-color: #d6e9c6;
}

.timeline-item.completed .timeline-marker {
    border-color: #3c763d;
}

/* ========== admin-hazard-drafts 页面样式 ========== */
.draft-item {
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.draft-item:hover {
    background-color: #f9f9f9;
}

.draft-actions {
    text-align: right;
}

.draft-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.draft-count {
    background-color: #337ab7;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    margin-left: 5px;
}

/* ========== admin-hazard-issue-review 页面样式 ========== */
.hazard-info {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.previous-review {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}

/* ========== admin-hazard-rectification 页面样式 ========== */

/* ========== admin-hazard-rectification-detail 页面样式 ========== */
/* 图片查看相关样式 */
.hazard-image,
.rectification-image {
    transition: transform 0.2s ease;
    cursor: pointer;
}

/* 图片查看相关样式 */
.hazard-image,
.rectification-image {
    transition: transform 0.2s ease;
    cursor: pointer;
}

.thumbnail {
    position: relative;
    overflow: hidden;
}

/* 按钮样式优化 */
.btn.btn-sm.btn-danger.remove-image {
    position: absolute;
    top: 5px;
    right: 5px;
    /* z-index: 10; - 取消低优先级z-index */
}

/* ========== admin-hazard-rectification-detail-new 页面样式 ========== */
/* 图片查看相关样式 */
.hazard-image,
.rectification-image {
    transition: transform 0.2s ease;
    cursor: pointer;
    max-height: 200px;
    width: auto;
    display: block;
    margin: 0 auto;
}

/* ========== admin-hazard-report 页面样式 ========== */
.hazard-form {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 20px;
}

/* 检查依据选择模态框样式 - 使用统一z-index变量 */
#checkCategoryModal {
    z-index: 410 !important;
    z-index: var(--z-modal) !important;
}

#checkCategoryModal .modal-backdrop {
    z-index: 400 !important;
    z-index: var(--z-modal-backdrop) !important;
}

#checkCategoryModal .modal-dialog {
    width: 90%;
    max-width: 1200px;
    z-index: 410 !important;
    z-index: var(--z-modal) !important;
}

#checkCategoryModal .modal-content {
    border-radius: 6px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, .2);
    z-index: 420 !important;
    z-index: var(--z-modal-content) !important;
}

/* 全局模态框层级修复 - 使用统一z-index变量 */
.modal {
    z-index: 410 !important;
    z-index: var(--z-modal) !important;
}

.modal-backdrop {
    z-index: 400 !important;
    z-index: var(--z-modal-backdrop) !important;
}

.modal-dialog {
    z-index: 410 !important;
    z-index: var(--z-modal) !important;
}

.modal-content {
    z-index: 420 !important;
    z-index: var(--z-modal-content) !important;
}

#checkCategoryModal .modal-header {
    background-color: #f8f8f8;
    border-bottom: 2px solid #e7e7e7;
    padding: 15px 20px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

.hazard-item {
    border: 1px solid #ddd;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 5px;
    position: relative;
}

.hazard-item .close {
    position: absolute;
    top: 10px;
    right: 10px;
}

.required-label:after {
    content: " *";
    color: red;
}

.image-preview {
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;
}

.image-preview-item {
    display: inline-block;
    margin-bottom: 15px;
    vertical-align: top;
}

/* 图片查看相关样式 */
.hazard-image,
.rectification-image {
    transition: transform 0.2s ease;
    cursor: pointer;
    max-height: 200px;
    width: auto;
    display: block;
    margin: 0 auto;
}

.hazard-image:hover,
.rectification-image:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.thumbnail {
    position: relative;
    overflow: hidden;
    margin-bottom: 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px;
    background-color: #fff;
    height: 220px;
    /* 固定高度，包含图片和边框 */
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-image {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #ff4d4f;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    transition: all 0.2s ease;
    /* z-index: 10; - 取消低优先级z-index */
    padding: 2px 5px;
    font-size: 12px;
}

.remove-image:hover {
    background: #ff7875;
    transform: scale(1.1);
}

.upload-image-btn {
    background-color: #5cb85c;
    color: white;
    font-weight: bold;
    padding: 6px 12px;
    transition: all 0.3s ease;
}

.upload-image-btn:hover {
    background-color: #4cae4c;
    transform: scale(1.05);
}

.image-upload-container {
    border: 2px dashed #5cb85c;
    padding: 15px;
    border-radius: 5px;
    margin-top: 10px;
    background-color: #f8fff8;
    position: relative;
}

.image-upload-icon {
    font-size: 24px;
    color: #5cb85c;
    margin-right: 10px;
    vertical-align: middle;
}

.upload-status {
    margin-top: 10px;
}

.draft-list {
    margin-top: 20px;
}

/* 已选择的检查要点样式已移除，因为不再有重复显示区域 */

/* 检查要点下拉框样式 */
.check-point-dropdown {
    position: static !important;
    border: 1px solid #ccc !important;
    border-radius: 4px !important;
    margin-top: 5px !important;
    margin-bottom: 10px !important;
    background-color: #fff !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
}

.check-point-item {
    padding: 8px 12px !important;
    cursor: pointer !important;
    border-bottom: 1px solid #eee !important;
}

.check-point-item:hover {
    background-color: #f5f5f5 !important;
}

#checkCategoryModal .modal-title {
    font-weight: bold;
    color: #333;
    font-size: 18px;
}

#checkCategoryModal .modal-body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px;
    background-color: #fff;
}

#checkCategoryModal .modal-footer {
    background-color: #f8f8f8;
    border-top: 1px solid #e7e7e7;
    padding: 15px 20px;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
}

/* 使用更强的选择器优先级 */
.modal-dialog #categoryTabs>li>a,
.modal-content #categoryTabs>li>a,
#checkCategoryModal #categoryTabs>li>a {
    font-weight: bold !important;
    color: white !important;
    background-color: #337ab7 !important;
    border: 1px solid #337ab7 !important;
    border-bottom-color: transparent !important;
    padding: 10px 20px !important;
    margin-right: 5px !important;
    border-top-left-radius: 6px !important;
    border-top-right-radius: 6px !important;
    transition: all 0.3s ease !important;
    -webkit-text-decoration: none !important;
    text-decoration: none !important;
}

.modal-dialog #categoryTabs>li>a:hover,
.modal-content #categoryTabs>li>a:hover,
#checkCategoryModal #categoryTabs>li>a:hover {
    background-color: #286090 !important;
    color: white !important;
    border-color: #286090 !important;
    -webkit-text-decoration: none !important;
    text-decoration: none !important;
}

.modal-dialog #categoryTabs>li.active>a,
.modal-content #categoryTabs>li.active>a,
#checkCategoryModal #categoryTabs>li.active>a {
    background-color: #337ab7 !important;
    color: white !important;
    border: 1px solid #337ab7 !important;
    border-bottom-color: transparent !important;
    -webkit-text-decoration: none !important;
    text-decoration: none !important;
}

/* 选项卡样式 - 与添加专项检查页面保持一致 */
#categoryTabs {
    margin-bottom: 20px !important;
    border-bottom: 2px solid #337ab7 !important;
}

#categoryTabs>li {
    margin-bottom: -2px !important;
}

#categoryTabs>li>a {
    font-weight: bold !important;
    color: #555 !important;
    background-color: #f5f5f5 !important;
    border: 1px solid #ddd !important;
    border-bottom-color: transparent !important;
    padding: 10px 20px !important;
    margin-right: 5px !important;
    border-top-left-radius: 6px !important;
    border-top-right-radius: 6px !important;
    transition: all 0.3s ease !important;
    -webkit-text-decoration: none !important;
    text-decoration: none !important;
}

#categoryTabs>li>a:hover {
    background-color: #e8e8e8 !important;
    color: #337ab7 !important;
    -webkit-text-decoration: none !important;
    text-decoration: none !important;
}

#categoryTabs>li.active>a,
#categoryTabs>li.active>a:hover,
#categoryTabs>li.active>a:focus,
#categoryTabs>li.active>a:link,
#categoryTabs>li.active>a:visited,
#categoryTabs>li.active>a:active {
    background-color: #337ab7 !important;
    color: white !important;
    border: 1px solid #337ab7 !important;
    border-bottom-color: transparent !important;
    -webkit-text-decoration: none !important;
    text-decoration: none !important;
}

/* 模态框中的选项卡样式 - 与添加专项检查页面保持一致 */
#checkCategoryModal .nav-tabs {
    margin-bottom: 20px !important;
    border-bottom: 2px solid #337ab7 !important;
}

#checkCategoryModal .nav-tabs>li {
    margin-bottom: -2px !important;
}

#checkCategoryModal .nav-tabs>li>a {
    font-weight: bold !important;
    color: #555 !important;
    background-color: #f5f5f5 !important;
    border: 1px solid #ddd !important;
    border-bottom-color: transparent !important;
    padding: 10px 20px !important;
    margin-right: 5px !important;
    border-top-left-radius: 6px !important;
    border-top-right-radius: 6px !important;
    transition: all 0.3s ease !important;
    -webkit-text-decoration: none !important;
    text-decoration: none !important;
}

#checkCategoryModal .nav-tabs>li>a:hover {
    background-color: #e8e8e8 !important;
    color: #337ab7 !important;
    -webkit-text-decoration: none !important;
    text-decoration: none !important;
}

#checkCategoryModal .nav-tabs>li.active>a,
#checkCategoryModal .nav-tabs>li.active>a:hover,
#checkCategoryModal .nav-tabs>li.active>a:focus,
#checkCategoryModal .nav-tabs>li.active>a:link,
#checkCategoryModal .nav-tabs>li.active>a:visited,
#checkCategoryModal .nav-tabs>li.active>a:active {
    background-color: #337ab7 !important;
    color: white !important;
    border: 1px solid #337ab7 !important;
    border-bottom-color: transparent !important;
    -webkit-text-decoration: none !important;
    text-decoration: none !important;
}

/* 二级指标面板样式 */
.category-panel {
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, .1);
    overflow: hidden;
}

.category-panel-heading {
    padding: 12px 15px;
    background-color: #337ab7;
    border-bottom: 1px solid #ddd;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    color: white;
}

.category-panel-heading:after {
    content: '\f107';
    font-family: 'FontAwesome';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    transition: all 0.3s ease;
}

.category-panel-heading.active:after {
    transform: translateY(-50%) rotate(180deg);
}

.category-panel-heading:hover {
    background-color: #286090;
}

.category-panel-heading h4 {
    margin: 0;
    font-size: 16px;
    color: white;
    font-weight: bold;
}

.category-panel-body {
    padding: 0;
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
}

/* 表格样式 */
.category-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
    border: 1px solid #ddd;
}

.category-table tr {
    border-bottom: 1px solid #ddd;
}

.category-table tr:last-child {
    border-bottom: none;
}

.category-table td {
    padding: 0;
    vertical-align: middle;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
}

.category-table td:last-child {
    border-right: none;
}

/* 表头样式 */
.category-table-header {
    background-color: #337ab7;
    color: white;
    font-weight: bold;
    text-align: center;
    padding: 10px !important;
}

/* 二级指标表头样式 */
.category-level-2-header {
    background-color: #337ab7;
    color: white;
    font-weight: bold;
    text-align: left;
    padding: 8px 15px !important;
}

/* 三级指标样式 */
.category-item {
    padding: 0;
    box-sizing: border-box;
    border: 1px solid #ddd;
    width: 100%;
    display: block;
    margin-bottom: -1px;
}

/* 颜色区分 */
.category-level-1 {
    background-color: #337ab7;
    color: white;
}

.category-level-2 {
    background-color: #337ab7;
    color: white;
}

.category-level-3 {
    background-color: #ffffff;
    color: #333;
    border: 1px solid #ddd;
}

/* 表格行颜色 */
.category-table tr:nth-child(odd) {
    background-color: #f9f9f9;
}

.category-table tr:hover {
    background-color: #f5f5f5;
}

/* 选中样式 */
.category-item label.selected {
    background-color: #d9edf7;
    color: #31708f;
    font-weight: bold;
    border: 1px solid #bce8f1;
}

.category-radio {
    margin-right: 5px !important;
}

/* 确保所有表格宽度一致 */
.level-2-container {
    width: 100%;
}

/* 确保检查依据选择框在最上层 - 使用统一z-index变量 */
.form-group[style*="z-index: 2000"] {
    position: relative;
    z-index: 2000 !important;
    z-index: var(--z-loading-overlay) !important;
}

/* 搜索结果样式 */
.category-search-result {
    margin-top: 15px;
    padding: 15px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, .1);
}

.category-search-result h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #337ab7;
    font-weight: bold;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.category-search-item {
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
    transition: all 0.2s ease;
}

.category-search-item:last-child {
    border-bottom: none;
}

.category-search-item:hover {
    background-color: #f0f0f0;
}

/* 搜索框样式 */
#categorySearchInput {
    border-radius: 4px 0 0 4px;
    height: 38px;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}

#categorySearchBtn {
    height: 38px;
    background-color: #337ab7;
    color: white;
    border-color: #2e6da4;
}

#categorySearchBtn:hover {
    background-color: #286090;
    border-color: #204d74;
}

/* 确认按钮样式 */
#confirmCategorySelection {
    background-color: #5cb85c;
    border-color: #4cae4c;
    padding: 8px 20px;
    font-weight: bold;
}

#confirmCategorySelection:hover {
    background-color: #449d44;
    border-color: #398439;
}

/* ========== admin-hazard-review 页面样式 ========== */
.hazard-info {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.section-title {
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.image-gallery {
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;
}

.image-item img {
    width: 150px;
    height: 150px;
    -o-object-fit: cover;
       object-fit: cover;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

.review-form {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 5px;
}

.modal-image {
    max-width: 100%;
    max-height: 80vh;
}

/* ========== admin-image-browser 页面样式 ========== */
.image-browser-container {
    padding: 15px;
}

.image-item {
    margin-bottom: 20px;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
    transition: all 0.3s;
}

.image-preview {
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-bottom: 10px;
}

.image-preview img {
    max-width: 100%;
    max-height: 140px;
    -o-object-fit: contain;
       object-fit: contain;
}

.image-info {
    font-size: 12px;
    color: #666;
}

.image-actions {
    margin-top: 10px;
}

.selected {
    border: 2px solid #5cb85c;
    background-color: #f9fff9;
}

#selected-images {
    margin-top: 20px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.selected-image-item {
    display: inline-block;
    margin: 5px;
    position: relative;
}

.selected-image-item img {
    max-width: 100px;
    max-height: 100px;
    border: 1px solid #ddd;
    padding: 2px;
}

.remove-selected {
    position: absolute;
    top: -10px;
    right: -10px;
    background-color: #d9534f;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    cursor: pointer;
}

/* ========== admin-image-selector 页面样式 ========== */
.image-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
}

.image-item {
    width: 150px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s;
}

.image-item:hover {
    border-color: #337ab7;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.image-item.selected {
    border-color: #5cb85c;
    background-color: #f9fff9;
}

.image-preview {
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-bottom: 10px;
}

.image-preview img {
    max-width: 100%;
    max-height: 100px;
    -o-object-fit: contain;
       object-fit: contain;
}

.image-info {
    font-size: 12px;
    color: #666;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.filter-form {
    margin-bottom: 20px;
}

.pagination {
    margin-top: 20px;
}

.image-search {
    margin-bottom: 15px;
}

.image-upload-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px dashed #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.image-upload-section h4 {
    margin-top: 0;
}

.image-size-options {
    margin-top: 15px;
}

.image-size-options label {
    margin-right: 15px;
}

/* ========== admin-inspection-activities - 副本 (2) 页面样式 ========== */
/* 统计卡片样式 */
.stat-card {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

/* 表格样式 */
.table {
    table-layout: fixed;
    width: 100%;
}

/* 操作列按钮样式 */
.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
    line-height: 1.3;
    margin: 2px 1px;
    white-space: nowrap;
    border-radius: 3px;
}

/* 统一操作列中所有按钮的大小 */
.table .col-actions .btn,
.inspection-activities-table td:nth-child(9) .btn,
.action-buttons .btn {
    padding: 4px 8px !important;
    font-size: 12px !important;
    line-height: 1.3 !important;
    margin: 2px 1px !important;
    white-space: nowrap !important;
    border-radius: 3px !important;
    min-height: 26px !important; /* 确保所有按钮高度一致 */
    display: inline-block !important;
    vertical-align: middle !important;
}

/* 确保下拉菜单按钮也统一大小 */
.table .col-actions .btn-group .btn,
.table .col-actions .dropdown-toggle {
    padding: 4px 8px !important;
    font-size: 12px !important;
    line-height: 1.3 !important;
    min-height: 26px !important;
}

/* 操作列样式 */
td:last-child {
    white-space: normal !important;
    line-height: 1.4;
}

/* 草稿徽章样式 */
.draft-badge {
    background-color: #ff6b6b;
    color: white;
    border-radius: 50%;
    padding: 1px 3px;
    font-size: 9px;
    margin-left: 3px;
    min-width: 14px;
    text-align: center;
    display: inline-block;
    line-height: 1.2;
}

/* 下拉菜单样式 */
.btn-group {
    display: inline-block;
    vertical-align: top;
    margin: 2px 1px;
    position: relative;
}

/* 专门针对表格中的下拉菜单 - 使用统一z-index变量 */
.table .btn-group .dropdown-menu,
.table .dropdown-menu,
.activity-table-row .dropdown-menu {
    min-width: 140px !important;
    z-index: 1000 !important;
    z-index: var(--z-dropdown-menu) !important;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175) !important;
    border: 1px solid rgba(0, 0, 0, 0.15) !important;
    background-color: #fff !important;
    border-radius: 4px !important;
    position: fixed !important;
    display: block !important;
}

/* 确保下拉菜单在打开时可见 */
.table .btn-group.open .dropdown-menu,
.table .open .dropdown-menu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 统一的表格容器下拉菜单支持样式 */
.table-responsive:not(.permission-matrix-container):not(.search-results-container) {
    overflow: visible !important;
    position: static !important;
}

/* 确保表格行不会裁剪下拉菜单 */
.table tbody tr {
    position: relative;
}

/* 确保操作列单元格不会裁剪下拉菜单 */
.table th:last-child,
.table td:last-child {
    min-width: 280px;
    position: relative;
    overflow: visible !important;
}

/* 确保整个页面容器不会裁剪下拉菜单 */
.container-fluid {
    overflow: visible !important;
}

/* 强制所有可能影响下拉菜单的容器都不裁剪内容 */
body,
html {
    overflow-x: visible !important;
}

/* 特别针对表格下拉菜单的强制样式 - 使用统一z-index变量 */
.table .btn-group.open .dropdown-menu {
    position: fixed !important;
    z-index: 1000 !important;
    z-index: var(--z-dropdown-menu) !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    transform: none !important;
    clip: none !important;
    clip-path: none !important;
}

/* 表格标题行样式 */
.table-title-row th {
    background-color: #f5f5f5 !important;
    border-bottom: 2px solid #ddd !important;
}

/* 强制下拉菜单显示在最上层 - 使用统一z-index变量 */
.dropdown-menu.show,
.dropdown-menu.open {
    z-index: 1000 !important;
    z-index: var(--z-dropdown-menu) !important;
}

/* ========== 通用下拉菜单样式 ========== */
/* 基础下拉菜单样式 - 适用于所有页面 */
.dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    z-index: 1000 !important;
    z-index: var(--z-dropdown-menu) !important;
    display: none; /* 允许JavaScript控制显示/隐藏 */
    float: left !important;
    min-width: 160px !important;
    padding: 5px 0 !important;
    margin: 2px 0 0 !important;
    font-size: 14px !important;
    text-align: left !important;
    list-style: none !important;
    background-color: #fff !important;
    border: 1px solid #ccc !important;
    border: 1px solid rgba(0, 0, 0, .15) !important;
    border-radius: 4px !important;
    box-shadow: 0 6px 12px rgba(0, 0, 0, .175) !important;
    background-clip: padding-box !important;
}

/* 下拉菜单自动位置修正 - 当菜单超出视口时自动调整 */
.dropdown-menu.auto-position {
    position: fixed !important;
}

/* 下拉菜单强制关闭样式 */
.dropdown-menu.force-hidden {
    display: none !important;
}

/* 下拉菜单显示状态 */
.dropdown-menu.show,
.dropdown-menu.open,
.btn-group.open .dropdown-menu,
.dropdown.open .dropdown-menu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 右对齐的下拉菜单 */
.dropdown-menu-right {
    right: 0 !important;
    left: auto !important;
}

/* 表格整体样式优化 */
.table {
    margin-bottom: 0 !important;
    border-collapse: separate !important;
    border-spacing: 0 !important;
}

/* 确保所有容器都不会裁剪下拉菜单 */

/* ========== admin-inspection-activities - 副本 (3) 页面样式 ========== */

/* ========== admin-inspection-activities - 副本 页面样式 ========== */

/* 确保表格不被任何容器限制 - 但排除搜索结果容器 */
.table-responsive:not(.search-results-container) {
    overflow: visible !important;
    position: static !important;
}

/* ========== admin-inspection-activities 页面样式 ========== */

/* 统计卡片图标样式 */
.stat-card .icon.total-icon {
    background-color: #2e6da4;
}

.stat-card .icon.active-icon {
    background-color: #5cb85c;
}

.stat-card .icon.completed-icon {
    background-color: #5bc0de;
}

.stat-card .icon.cancelled-icon {
    background-color: #d9534f;
}

.stat-card .icon i {
    color: white;
}

/* 表格标题行样式 */
.inspection-activities-table .table-title-row th {
    background-color: #f5f5f5;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    padding: 15px;
    position: relative;
}

/* 回收站入口样式 */
.recycle-bin-entry {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
}

/* 回收站计数徽章样式 */
.recycle-bin-entry .badge {
    background-color: #d9534f;
}

/* 表格列宽样式 */
.inspection-activities-table th.col-index { width: 5%; }
.inspection-activities-table th.col-name { width: 15%; }
.inspection-activities-table th.col-type { width: 10%; }
.inspection-activities-table th.col-target { width: 10%; }
.inspection-activities-table th.col-period { width: 15%; }
.inspection-activities-table th.col-status { width: 8%; }
.inspection-activities-table th.col-report { width: 10%; }
.inspection-activities-table th.col-created { width: 10%; }
.inspection-activities-table th.col-actions { width: 17%; }

/* 检查活动表格特定对齐 */
.inspection-activities-table th,
.inspection-activities-table td {
    vertical-align: middle !important;
    padding: 12px 8px !important;
}

/* 序号列居中 */
.inspection-activities-table td:nth-child(1) {
    text-align: center;
    font-weight: 500;
}

/* 标签列居中 */
.inspection-activities-table td:nth-child(3),
.inspection-activities-table td:nth-child(4),
.inspection-activities-table td:nth-child(6),
.inspection-activities-table td:nth-child(7) {
    text-align: center;
}

/* 日期时间列居中 */
.inspection-activities-table td:nth-child(5),
.inspection-activities-table td:nth-child(8) {
    text-align: center;
    font-family: 'Courier New', monospace; /* 等宽字体让日期更整齐 */
}

/* 活动名称列左对齐 */
.inspection-activities-table td:nth-child(2) {
    text-align: left;
    font-weight: 500;
}

/* 操作列居中 */
.inspection-activities-table td:nth-child(9) {
    text-align: center;
}

/* 确保所有容器都不会裁剪下拉菜单 */

.panel {
    overflow: visible !important;
}

/* 确保表格不被任何容器限制 */

/* 搜索结果容器的特殊样式 */
.table-responsive.search-results-container {
    max-height: 400px !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    background-color: #fff !important;
    box-sizing: border-box !important;
}

/* 面板标题栏样式 - 已删除重复定义，使用第1052行的完整定义 */

.stat-card:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
    transform: translateY(-2px);
}

.stat-card .icon {
    position: absolute;
    top: 0;
    left: 0;
    width: 80px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
}

.stat-card .number {
    font-size: 36px;
    font-weight: bold;
    margin-left: 90px;
    padding-top: 15px;
    padding-right: 15px;
}

.stat-card .title {
    margin-left: 90px;
    padding-bottom: 15px;
    color: #777;
}

.search-box {
    margin-bottom: 15px;
}

.filter-btn {
    margin-left: 5px;
}

.filter-active {
    background-color: #3498db;
    color: white;
}

.table th,
.table td {
    vertical-align: middle; /* 垂直居中 */
    padding: 12px 8px; /* 增加上下内边距 */
    text-align: left; /* 默认左对齐 */
}

/* 特定列的对齐方式 - 使用更高优先级 */
.table th.text-center,
.table td.text-center,
.table .col-id,
.table .col-status,
.table .col-actions,
.table .col-date,
.table .col-count,
.table .col-number,
.table .col-level,
.table .col-type,
.table .col-boolean,
.table .col-name {
    text-align: center !important; /* 居中对齐 */
}

/* 确保所有容器都不会裁剪下拉菜单 */

.panel {
    overflow: visible !important;
}

.panel-body {
    overflow: visible !important;
    position: static !important;
}

.table {
    overflow: visible !important;
}

.row {
    overflow: visible !important;
}

.col-md-12 {
    overflow: visible !important;
}

.content-wrapper,
.main-content,
.page-content {
    overflow: visible !important;
}

/* 确保表格不被任何容器限制 */

.table thead th {
    border-top: none !important;
}

.dropdown-menu li a {
    padding: 6px 12px;
    font-size: 12px;
}

.dropdown-menu li a i {
    margin-right: 6px;
    width: 14px;
}

.dropdown-menu .text-danger {
    color: #d9534f !important;
}

.dropdown-menu .text-danger:hover {
    background-color: #f5f5f5;
    color: #c9302c !important;
}

.panel-title {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
}

/* ========== admin-inspection-activity-form 页面样式 ========== */
/* 日期选择器样式 - 使用统一z-index变量 */
.datepicker {
    z-index: 1000 !important;
    z-index: var(--z-datepicker) !important;
}

/* 模态框背景遮罩层 - 使用统一z-index变量 */
.modal-backdrop {
    z-index: 400 !important;
    z-index: var(--z-modal-backdrop) !important;
}

.required-field label:after {
    content: " *";
    color: red;
}

.tab-content {
    padding: 20px;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    border-radius: 0 0 4px 4px;
}

.nav-tabs {
    margin-bottom: 0;
}

.personnel-list,
.laboratory-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    margin-bottom: 10px;
}

/* 二级指标表格 */
.level-2-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    table-layout: fixed;
}

/* 二级指标标题 */
.level-2-header {
    background-color: #337ab7 !important;
    color: white !important;
    font-weight: bold;
    padding: 8px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

/* 表格单元格通用样式 */
.level-2-table th,
.level-2-table td {
    border: 1px solid #ddd;
}

/* 三级指标容器 */
.level-3-container {
    padding: 0;
}

/* 三级指标项 */
.level-3-item {
    padding: 8px 15px;
    background-color: #fff;
    vertical-align: middle;
}

/* 脉冲动画效果 */
@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(51, 122, 183, 0.4);
    }

    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(51, 122, 183, 0);
    }

    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(51, 122, 183, 0);
    }
}

.pulse-animation {
    animation: pulse 0.5s ease-in-out;
}

.personnel-item,
.laboratory-item {
    margin-bottom: 5px;
    padding: 5px;
    border-bottom: 1px solid #eee;
}

.personnel-item:last-child,
.laboratory-item:last-child {
    border-bottom: none;
}

.filter-section {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
}

/* 检查依据和检查要点样式 */
#selectedCategoriesContainer,
#selectedCheckPointsContainer {
    min-height: 80px;
    max-height: 200px;
    overflow-y: auto;
}

.label {
    display: inline-block;
    margin: 5px;
    padding: 5px 10px;
    font-size: 14px;
}

.label a {
    color: white;
    margin-left: 5px;
    -webkit-text-decoration: none;
    text-decoration: none;
}

.label a:hover {
    color: #f0f0f0;
}

/* ========== admin-inspection-check 页面样式 ========== */
/* 检查依据和检查要点相关样式 */
.selected-check-points-container {
    margin-top: 10px !important;
    border: 1px solid #eee !important;
    border-radius: 4px !important;
    padding: 10px !important;
    background-color: #f9f9f9 !important;
    min-height: 100px !important;
    max-height: 200px !important;
    overflow-y: auto !important;
    display: block !important;
}

/* 检查依据和检查要点相关样式 */
.selected-check-points-container {
    margin-top: 10px !important;
    border: 1px solid #eee !important;
    border-radius: 4px !important;
    padding: 10px !important;
    background-color: #f9f9f9 !important;
    min-height: 100px !important;
    max-height: 200px !important;
    overflow-y: auto !important;
    display: block !important;
}

.selected-check-point {
    display: inline-flex;
    align-items: center;
    margin-right: 10px;
    margin-bottom: 10px;
    padding: 5px 10px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.selected-check-point .badge {
    display: inline-block;
    padding: 3px 7px;
    font-size: 12px;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    background-color: #337ab7;
    border-radius: 10px;
}

.selected-check-point .remove-selected-point {
    margin-left: 5px;
    color: #d9534f;
    cursor: pointer;
}

.selected-check-point .remove-selected-point:hover {
    color: #c9302c;
}

/* 检查依据选择相关样式 */
.selected-category-item {
    display: inline-flex;
    align-items: center;
    margin-right: 10px;
    margin-bottom: 10px;
    padding: 5px 10px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.selected-category-item .badge {
    display: inline-block;
    padding: 3px 7px;
    font-size: 12px;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    background-color: #5bc0de;
    border-radius: 10px;
}

.selected-category-item .remove-selected-category {
    margin-left: 5px;
    color: #d9534f;
    cursor: pointer;
}

.selected-category-item .remove-selected-category:hover {
    color: #c9302c;
}

/* 选项卡样式 */
#categoryTabs {
    margin-bottom: 20px;
    border-bottom: 2px solid #337ab7;
}

#categoryTabs>li {
    margin-bottom: -2px;
}

#categoryTabs>li>a {
    font-weight: bold;
    color: #555;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-bottom-color: transparent;
    padding: 10px 20px;
    margin-right: 5px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    transition: all 0.3s ease;
}

#categoryTabs>li>a:hover {
    background-color: #e8e8e8;
    color: #337ab7;
}

#categoryTabs>li.active>a {
    background-color: #337ab7;
    color: white;
    border: 1px solid #337ab7;
    border-bottom-color: transparent;
}

/* 检查依据模态框内容区域最大高度 */
#categoryTabContent {
    max-height: 400px;
    overflow-y: auto;
}

/* 二级指标容器 */
.level-2-container {
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.level-2-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.level-2-header {
    background-color: #337ab7 !important;
    color: white !important;
    font-weight: bold;
    padding: 10px 15px;
    text-align: left;
}

.level-3-item {
    padding: 8px 15px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s ease;
}

.level-3-item:hover {
    background-color: #f5f5f5;
}

.level-3-item label {
    display: block;
    margin: 0;
    cursor: pointer;
    font-weight: normal;
    width: 100%;
}

.level-3-item label.selected {
    font-weight: bold;
    color: #337ab7;
}

/* ========== admin-inspection-clauses 页面样式 ========== */
.check-points-container {
    padding: 0;
    margin: 0;
}

.check-point-item {
    margin-bottom: 5px;
}

.check-point-item .input-group {
    width: 100%;
}

.check-point-item .input-group-addon {
    background-color: #f8f8f8;
    color: #5cb85c;
}

.check-point-item .form-control {
    height: auto;
    min-height: 30px;
    padding: 5px 10px;
    background-color: #f9f9f9;
    border-color: #ddd;
}

.mt-1 {
    margin-top: 5px;
}

.mt-2 {
    margin-top: 10px;
}

/* ========== admin-issue-review 页面样式 ========== */

/* 筛选样式 */
.bootstrap-select .dropdown-menu {
    max-height: 400px !important;
}

.bootstrap-select .dropdown-menu li a {
    white-space: normal;
    word-wrap: break-word;
}

#advancedSearch .form-group {
    margin-bottom: 10px;
}

/* 修复下拉框重叠问题 - 使用统一z-index变量 */
.bootstrap-select .dropdown-menu {
    z-index: 1010 !important;
    z-index: var(--z-select) !important;
    /* 确保下拉菜单在最上层 */
    position: fixed !important;
    /* 使用固定定位，不受父元素影响 */
    max-height: 300px !important;
    /* 限制最大高度 */
    overflow-y: auto !important;
    /* 允许垂直滚动 */
    width: auto !important;
    /* 自适应宽度 */
    min-width: 200px !important;
    /* 最小宽度 */
}

/* 确保下拉框内容不被截断 */
.bootstrap-select .dropdown-menu li a {
    white-space: normal !important;
    word-wrap: break-word !important;
    padding: 8px 10px !important;
    line-height: 1.4 !important;
}

/* 确保下拉框有足够的空间展开 */
#filterForm .form-group {
    margin-bottom: 30px !important;
    position: relative !important;
}

/* 确保下拉框按钮样式正确 */
.bootstrap-select .btn {
    white-space: normal !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* 修复下拉框位置问题 */
.bootstrap-select.btn-group .dropdown-menu {
    margin-top: 5px !important;
}

.nav-tabs {
    margin-bottom: 20px;
}

#filterForm {
    margin-bottom: 15px;
}

/* 排序图标 */
th[data-sort] {
    cursor: pointer;
}

/* ========== admin-laboratories 页面样式 ========== */
/* 实验室属性下拉多选框样式 */
.property-dropdown {
    width: 100%;
    position: relative;
}

.property-dropdown .dropdown-toggle {
    text-align: left;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.property-dropdown-menu {
    padding: 10px;
    width: 100%;
    max-height: none;
    overflow: visible;
    position: absolute;
    left: 0;
    right: 0;
    box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, .15);
    border-radius: 4px;
    z-index: 1000;
    z-index: var(--z-dropdown-menu);
}

/* 确保下拉框在打开状态下正确显示 */
.property-dropdown.open .property-dropdown-menu {
    display: block;
}

/* 确保下拉框在关闭状态下不显示 */
.property-dropdown:not(.open) .property-dropdown-menu {
    display: none;
}

.property-items-wrapper {
    max-height: 250px;
    overflow-y: auto;
    padding: 5px;
    width: 100%;
}

/* 修复复选框内容左对齐 */
.property-items-wrapper .checkbox {
    padding-left: 0;
    margin-left: 0;
}

.property-items-wrapper .checkbox label {
    padding-left: 20px;
    margin-left: 0;
    width: 100%;
}

.dropdown-header {
    padding: 5px;
    font-size: 14px;
    color: #333;
}

.dropdown-header .checkbox {
    margin: 0;
}

.property-dropdown-menu .checkbox {
    margin-top: 5px;
    margin-bottom: 5px;
    display: block;
}

.property-dropdown-menu label {
    font-weight: normal;
    cursor: pointer;
    display: block;
    padding: 3px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
}

.property-dropdown-menu label:hover {
    background-color: #eaf2fa;
    border-radius: 3px;
}

/* 添加一些间距和边框效果 */
.form-group {
    margin-bottom: 15px;
}

/* ========== 统一复选框样式 - 全项目通用 ========== */
/* 基础复选框容器样式 */
.checkbox {
    position: relative;
    display: block;
    margin-top: 10px;
    margin-bottom: 10px;
    padding-left: 20px;
}

/* 复选框输入框样式 */
.checkbox input[type="checkbox"] {
    position: absolute;
    margin-top: 4px;
    margin-left: -20px;
}

/* 复选框标签样式 */
.checkbox label {
    min-height: 20px;
    padding-left: 0;
    margin-bottom: 0;
    font-weight: normal;
    cursor: pointer;
    display: inline;
}

/* 选中状态的高亮效果 */
.property-dropdown .btn-has-selection {
    border-color: #66afe9;
    background-color: #f0f7fd;
}

/* 改进表单控件样式 */
.form-control:focus {
    border-color: #66afe9;
    outline: 0;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
}

/* 防止点击复选框关闭下拉菜单 */
.dropdown-menu .checkbox {
    position: relative;
    /* z-index: 10; - 取消低优先级z-index */
}

/* ========== admin-laboratory-info-card 页面样式 ========== */
.nav-tabs-custom {
    margin-bottom: 20px;
    background: #fff;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.nav-tabs-custom>.nav-tabs {
    margin: 0;
    border-bottom-color: #f4f4f4;
}

.nav-tabs-custom>.nav-tabs>li {
    margin-right: 5px;
}

.nav-tabs-custom>.nav-tabs>li>a {
    color: #444;
    border-radius: 0;
}

.nav-tabs-custom>.nav-tabs>li.active {
    border-top-color: #3c8dbc;
}

.nav-tabs-custom>.nav-tabs>li.active>a {
    border-top-color: transparent;
    border-left-color: #f4f4f4;
    border-right-color: #f4f4f4;
}

.nav-tabs-custom>.tab-content {
    background: #fff;
    padding: 10px;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
}

.template-item.active .panel {
    border-color: #3c8dbc;
    box-shadow: 0 0 8px rgba(60, 141, 188, 0.5);
}

/* ========== admin-laboratory-info-card-preview 页面样式 ========== */
/* 预览容器样式 */
.preview-container {
    background-color: white;
    padding: 20px;
    border: 1px solid #ddd;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    font-family: '宋体小四号等宽', '宋体', 'SimSun', 'Times New Roman', serif;
    font-size: 12pt;
    line-height: 1.5;
    color: #000;
    width: 210mm;
    /* A4纸宽度 */
    min-height: 297mm;
    /* A4纸高度 */
    margin-left: auto;
    margin-right: auto;
    overflow: auto;
}

/* 字体大小 */
.preview-container span[style*="font-size:12.0000pt"],
.preview-container span[style*="font-size:12pt"] {
    font-size: 12pt !important;
}

/* 加粗 */
.preview-container b,
.preview-container span[style*="font-weight:bold"] {
    font-weight: bold !important;
}

/* 其他样式覆盖 */
.preview-container span[style*="font-family"] {
    font-family: inherit !important;
}

/* 表格内容垂直居中 */
.preview-container td[style*="vertical-align: middle"] {
    vertical-align: middle !important;
}

/* 表格内容水平居中 */
.preview-container td[style*="text-align: center"],
.preview-container p[style*="text-align:center"] {
    text-align: center !important;
}

/* 表格内容加粗 */
.preview-container td[style*="font-weight: bold"] {
    font-weight: bold !important;
}

/* 表格内容字体大小 */
.preview-container td[style*="font-size"] {
    font-size: inherit !important;
}

/* 预览容器样式 */
.preview-container {
    background-color: white;
    padding: 20px;
    border: 1px solid #ddd;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    font-family: '宋体小四号等宽', '宋体', 'SimSun', 'Times New Roman', serif;
    font-size: 12pt;
    line-height: 1.5;
    color: #000;
    width: 210mm;
    /* A4纸宽度 */
    min-height: 297mm;
    /* A4纸高度 */
    margin-left: auto;
    margin-right: auto;
    overflow: auto;
}

/* 表格样式 */
.preview-container table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
    table-layout: fixed;
    border: 1px solid #000;
}

.preview-container table,
.preview-container th,
.preview-container td {
    border: 1px solid #000;
}

.preview-container th,
.preview-container td {
    padding: 8px;
    word-wrap: break-word;
    word-wrap: break-word;
    position: relative;
}

/* 表格单元格内容垂直居中 */
.preview-container td {
    vertical-align: middle;
}

/* 表格单元格内容水平居中 */
.preview-container td p {
    text-align: center;
    margin: 0;
}

/* 图片样式 */
.preview-container img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
}

/* 段落样式 */
.preview-container p {
    margin: 0 0 10px 0;
    padding: 0;
}

/* 标题样式 */
.preview-container h1,
.preview-container h2,
.preview-container h3,
.preview-container h4,
.preview-container h5,
.preview-container h6 {
    font-weight: bold;
    margin: 10px 0;
    text-align: center;
}

.preview-container h1 {
    font-size: 24pt;
}

.preview-container h2 {
    font-size: 18pt;
}

.preview-container h3 {
    font-size: 16pt;
}

.preview-container h4 {
    font-size: 14pt;
}

.preview-container h5 {
    font-size: 12pt;
}

.preview-container h6 {
    font-size: 10pt;
}

/* 打印样式 */
@media print {
    .no-print {
        display: none !important;
    }

    body {
        margin: 0;
        padding: 0;
        background-color: white;
    }

    .preview-container {
        border: none;
        box-shadow: none;
        padding: 0;
        margin: 0;
        width: 210mm;
        height: 297mm;
        font-size: 12pt;
        overflow: hidden;
    }

    /* A4纸张大小设置 */
    @page {
        size: A4 portrait;
        margin: 1cm;
    }

    /* 每页显示2个信息牌 */
    .preview-container {
        page-break-inside: avoid;
        page-break-after: always;
    }

    /* 确保表格不会被分页 */
    .preview-container table {
        page-break-inside: avoid;
    }
}

/* 实验室信息牌特定样式 */
/* 蓝色背景单元格 - 四级风险 */
.preview-container .blue-bg,
.preview-container td[style*="background:rgb(0,112,192)"],
.preview-container td[style*="background-color:rgb(0,112,192)"] {
    background-color: #0070C0 !important;
    color: white !important;
    font-weight: bold !important;
}

/* 深蓝色背景单元格 - 标题行 */
.preview-container .dark-blue-bg,
.preview-container td[style*="background:rgb(47,49,139)"],
.preview-container td[style*="background-color:rgb(47,49,139)"] {
    background-color: #2F318B !important;
    color: white !important;
    font-weight: bold !important;
}

/* 绿色背景单元格 - 紧急电话 */
.preview-container .green-bg,
.preview-container td[style*="background:rgb(90,165,114)"],
.preview-container td[style*="background-color:rgb(90,165,114)"] {
    background-color: #5AA572 !important;
    color: white !important;
    font-weight: bold !important;
}

/* 红色背景单元格 - 危险类别 */
.preview-container .red-bg,
.preview-container td[style*="background:rgb(255,0,0)"],
.preview-container td[style*="background-color:rgb(255,0,0)"] {
    background-color: #FF0000 !important;
    color: white !important;
    font-weight: bold !important;
}

/* 橙色背景单元格 - 二级风险 */
.preview-container .orange-bg,
.preview-container td[style*="background:rgb(237,125,49)"],
.preview-container td[style*="background-color:rgb(237,125,49)"] {
    background-color: #ED7D31 !important;
    color: white !important;
    font-weight: bold !important;
}

/* 黄色背景单元格 - 三级风险 */
.preview-container .yellow-bg,
.preview-container td[style*="background:rgb(255,255,0)"],
.preview-container td[style*="background-color:rgb(255,255,0)"] {
    background-color: #FFFF00 !important;
    color: black !important;
    font-weight: bold !important;
}

/* 双线边框 */
.preview-container td[style*="border-left:1.0000pt double"],
.preview-container td[style*="border-left:1pt double"] {
    border-left: 3px double #000 !important;
}

.preview-container td[style*="border-right:1.0000pt double"],
.preview-container td[style*="border-right:1pt double"] {
    border-right: 3px double #000 !important;
}

.preview-container td[style*="border-top:1.0000pt double"],
.preview-container td[style*="border-top:1pt double"] {
    border-top: 3px double #000 !important;
}

.preview-container td[style*="border-bottom:1.0000pt double"],
.preview-container td[style*="border-bottom:1pt double"] {
    border-bottom: 3px double #000 !important;
}

/* 字间距 */
.preview-container span[style*="letter-spacing:1.5000pt"],
.preview-container span[style*="letter-spacing:1.5pt"] {
    letter-spacing: 1.5pt !important;
}

/* 占位符样式 */
.preview-container span[style*="color:rgb(199,37,78)"],
.preview-container span[style*="background:rgb(249,242,244)"] {
    color: #C7254E !important;
    background-color: #F9F2F4 !important;
    font-family: Consolas, monospace !important;
    font-size: 9pt !important;
}

/* 微软雅黑字体 */
.preview-container span[style*="font-family:微软雅黑"],
.preview-container span[style*="font-family:微软雅黑"] {
    font-family: '微软雅黑', 'Microsoft YaHei', sans-serif !important;
}

/* 宋体小四号等宽字体 */
.preview-container span[style*="font-family:宋体小四号等宽"],
.preview-container span[style*="font-family:宋体小四号等宽"] {
    font-family: '宋体小四号等宽', '宋体', 'SimSun', serif !important;
}

.preview-container span[style*="font-size:10.5000pt"],
.preview-container span[style*="font-size:10.5pt"] {
    font-size: 10.5pt !important;
}

.preview-container span[style*="font-size:9.0000pt"],
.preview-container span[style*="font-size:9pt"] {
    font-size: 9pt !important;
}

/* ========== admin-laboratory-properties 页面样式 ========== */
.checkbox-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
}

/* ========== admin-lab-safety-card 页面样式 ========== */
@page {
    size: A4;
    margin: 0;
}

.risk-level-1 {
    background-color: #f44336;
    /* 红色 - 一级 */
    color: white;
}

.risk-level-2 {
    background-color: #ff9800;
    /* 橙色 - 二级 */
    color: black;
}

.risk-level-3 {
    background-color: #ffeb3b;
    /* 黄色 - 三级 */
    color: black;
}

.risk-level-4 {
    background-color: #2196f3;
    /* 蓝色 - 四级 */
    color: white;
}

@page {
    size: A4;
    margin: 0;
}

body {
    margin: 0;
    padding: 0;
    font-family: "Microsoft YaHei", "SimHei", sans-serif;
}

.page {
    width: 210mm;
    min-height: 297mm;
    padding: 5mm;
    margin: 0 auto;
    background: white;
    box-sizing: border-box;
    page-break-after: always;
}

.card-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}

.safety-card {
    width: 100%;
    margin-bottom: 10mm;
    border: 2px solid #333;
}

.card-header {
    background-color: #2e3192;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 10px;
    height: 80px;
}

.card-title {
    font-size: 28px;
    font-weight: bold;
    text-align: center;
    flex-grow: 1;
}

.card-logo {
    width: 60px;
    height: 60px;
}

.room-number {
    border: 2px solid white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 18px;
    width: 80px;
    text-align: center;
}

.info-table {
    width: 100%;
    border-collapse: collapse;
}

.info-table td {
    border: 1px solid #333;
    padding: 5px;
    text-align: center;
    height: 35px;
}

.info-table .label {
    background-color: #2e3192;
    color: white;
    width: 25%;
}

.info-table .value {
    width: 25%;
    background-color: white;
}

.emergency-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: -1px;
}

.emergency-table td {
    border: 1px solid #333;
    padding: 5px;
    text-align: center;
    height: 35px;
}

.emergency-table .label {
    background-color: #4caf50;
    color: white;
    width: 12.5%;
}

.emergency-table .value {
    width: 12.5%;
    background-color: white;
}

.hazard-container {
    display: flex;
    width: 100%;
    margin-top: -1px;
}

.hazard-left {
    width: 60%;
}

.hazard-right {
    width: 40%;
}

.hazard-table {
    width: 100%;
    border-collapse: collapse;
    height: 100%;
}

.hazard-table td {
    border: 1px solid #333;
    padding: 5px;
    height: 60px;
    vertical-align: middle;
}

.hazard-table tr {
    height: 33.33%;
}

.hazard-label {
    background-color: #f44336;
    color: white;
    width: 20%;
    text-align: center;
    font-weight: bold;
}

.hazard-content {
    width: 80%;
    background-color: white;
}

.precaution-label {
    background-color: #ffeb3b;
    color: black;
    width: 20%;
    text-align: center;
    font-weight: bold;
}

.protection-label {
    background-color: #4caf50;
    color: white;
    width: 20%;
    text-align: center;
    font-weight: bold;
}

.fire-points-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.fire-points {
    background-color: #ffeb3b;
    color: black;
    border: 1px solid #333;
    padding: 5px;
    flex-grow: 1;
    margin-top: -1px;
}

.fire-points-title {
    background-color: #f44336;
    color: white;
    text-align: center;
    padding: 5px;
    font-weight: bold;
    border: 1px solid #333;
    margin: 0;
}

.fire-points-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.fire-points-list li {
    margin: 8px 0;
    display: flex;
    align-items: center;
}

/* 删除了特定页面的复选框样式，使用统一的全局样式 */

.card-footer {
    background-color: #2e3192;
    color: white;
    text-align: center;
    padding: 5px;
    font-size: 14px;
    margin-top: -1px;
}

/* 打印样式 */
@media print {
    .no-print {
        display: none;
    }

    body {
        margin: 0;
        padding: 0;
    }

    .page {
        margin: 0;
        padding: 5mm;
        box-shadow: none;
    }
}

/* ========== admin-management-button-config 页面样式 ========== */
.page-selector {
    margin-bottom: 20px;
}

.button-config-panel {
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 15px;
    padding: 15px;
    background: #f9f9f9;
}

.button-config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.button-preview {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    margin: 10px 0;
    background: white;
}

.drag-handle {
    cursor: move;
    color: #999;
    margin-right: 10px;
}

.button-controls {
    display: flex;
    gap: 5px;
}

.color-selector {
    display: inline-block;
    width: 30px;
    height: 30px;
    border-radius: 3px;
    cursor: pointer;
    margin: 2px;
    border: 2px solid transparent;
}

.color-selector.selected {
    border-color: #333;
}

.sortable-placeholder {
    background: #f0f0f0;
    border: 2px dashed #ccc;
    height: 100px;
    margin: 10px 0;
}

/* ========== admin-menu-management 页面样式 ========== */
/* 菜单管理页面样式 */
.menu-management-container {
    padding: 20px;
}

.menu-items-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    table-layout: auto !important; /* 确保表格自动布局 */
}

/* 一级菜单表格特定样式 */
#categoryOrderContainer .menu-items-table {
    table-layout: auto !important;
}

#categoryOrderContainer .menu-items-table th,
#categoryOrderContainer .menu-items-table td {
    padding: 8px !important;
    border: 1px solid #dee2e6 !important;
    vertical-align: middle !important;
    text-align: left !important;
    white-space: nowrap;
}

/* 优化一级菜单表格列宽度 */
#categoryOrderContainer .menu-items-table th:nth-child(1),
#categoryOrderContainer .menu-items-table td:nth-child(1) { width: 60px; } /* 拖拽 */
#categoryOrderContainer .menu-items-table th:nth-child(2),
#categoryOrderContainer .menu-items-table td:nth-child(2) { width: 60px; } /* 选择 */
#categoryOrderContainer .menu-items-table th:nth-child(3),
#categoryOrderContainer .menu-items-table td:nth-child(3) { width: 60px; } /* 图标 */
#categoryOrderContainer .menu-items-table th:nth-child(4),
#categoryOrderContainer .menu-items-table td:nth-child(4) { width: 150px; min-width: 120px; } /* 分类名称 */
#categoryOrderContainer .menu-items-table th:nth-child(5),
#categoryOrderContainer .menu-items-table td:nth-child(5) { width: 120px; } /* 菜单数量 */
#categoryOrderContainer .menu-items-table th:nth-child(6),
#categoryOrderContainer .menu-items-table td:nth-child(6) { width: 80px; } /* 排序 */
#categoryOrderContainer .menu-items-table th:nth-child(7),
#categoryOrderContainer .menu-items-table td:nth-child(7) { width: 100px; } /* 显示状态 */
#categoryOrderContainer .menu-items-table th:nth-child(8),
#categoryOrderContainer .menu-items-table td:nth-child(8) { width: 100px; } /* 启用状态 */ /* 操作 */
#categoryOrderContainer .menu-items-table th:nth-child(6),
#categoryOrderContainer .menu-items-table td:nth-child(6) { width: 80px; }
#categoryOrderContainer .menu-items-table th:nth-child(7),
#categoryOrderContainer .menu-items-table td:nth-child(7) { width: 80px; }
#categoryOrderContainer .menu-items-table th:nth-child(8),
#categoryOrderContainer .menu-items-table td:nth-child(8) { width: 80px; }
#categoryOrderContainer .menu-items-table th:nth-child(9),
#categoryOrderContainer .menu-items-table td:nth-child(9) { width: 120px; }

/* 完整拖拽样式 */
.menu-item.sortable-ghost {
    opacity: 0.5;
    background: #007bff;
    color: white;
    transform: rotate(2deg);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

/* 拖拽手柄样式 */
.drag-handle {
    cursor: move;
    margin-right: 10px;
    color: #6c757d;
    transition: color 0.2s;
}

/* 移动端优化 */

/* 拖拽状态动画 */
.menu-item {
    transition: all 0.2s ease;
}

/* 拖拽提示动画 */
.drag-hint {
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 一级菜单管理样式 - 完全模仿二级菜单容器样式 */
.category-order-container {
    min-height: 50px;
    padding: 0;
    background: #fff;
}

/* 移除冲突的flex样式，让表格正常显示 */
.category-order-item {
    /* 移除所有flex相关样式，使用表格行的默认显示 */
    cursor: move;
    transition: all 0.3s ease;
}

.category-order-item:hover {
    background: #f8f9fa;
}

.category-order-item.sortable-ghost {
    opacity: 0.5;
    background: #007bff;
    color: white;
    transform: rotate(2deg);
}

.category-order-item.sortable-chosen {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    transform: scale(1.02);
    z-index: 999;
    z-index: var(--z-drag);
}

/* 保留拖拽手柄样式 */
.category-drag-handle {
    color: #6c757d;
    cursor: move;
}

.category-drag-handle:hover {
    color: #007bff;
}

/* 保护样式 */
.protected-category {
    border: 2px solid #ffc107 !important;
    background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%) !important;
    position: relative;
}

/* 菜单管理页面样式 */
.menu-management-container {
    padding: 20px;
}

.role-selector {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
}

.menu-category {
    margin-bottom: 30px;
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
}

.category-header {
    background: #007bff;
    color: white;
    padding: 10px 15px;
    font-weight: bold;
    cursor: pointer;
}

.category-header:hover {
    background: #0056b3;
}

.menu-items-container {
    min-height: 50px;
    padding: 0;
    background: #fff;
}

.menu-item {
    cursor: move;
    transition: all 0.3s ease;
}

.menu-item:hover {
    background: #f8f9fa;
}

.menu-item td {
    padding: 8px;
    border: 1px solid #dee2e6;
    vertical-align: middle;
    text-align: left;
}

.menu-table-header {
    background: #007bff;
    color: white;
    font-weight: bold;
}

.menu-table-header th {
    padding: 10px 8px;
    border: 1px solid #0056b3;
    text-align: center;
}

.menu-item.sortable-chosen {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transform: scale(1.02);
    z-index: 999;
    z-index: var(--z-drag);
}

.menu-item.sortable-drag {
    transform: rotate(5deg);
    opacity: 0.8;
}

.sortable-placeholder {
    border: 2px dashed #007bff;
    background: rgba(0, 123, 255, 0.1);
    height: 60px;
    margin: 5px 0;
    border-radius: 5px;
    position: relative;
}

.sortable-placeholder::before {
    content: "拖拽到此处";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #007bff;
    font-weight: bold;
}

.menu-items-container.drag-target {
    background: rgba(0, 123, 255, 0.05);
    border: 2px dashed #007bff;
    border-radius: 5px;
}

.menu-item.drag-over {
    border-top: 3px solid #007bff;
}

.drag-handle:hover {
    color: #007bff;
}

/* 移动端优化 */

.menu-items-container.drag-target {
    transition: all 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.td-drag-handle {
    text-align: center;
    width: 30px;
    cursor: move;
}

.td-drag-handle .drag-handle {
    color: #6c757d;
    font-size: 14px;
}

.td-drag-handle .drag-handle:hover {
    color: #007bff;
}

.td-checkbox {
    text-align: center;
    width: 40px;
}

.td-icon {
    text-align: center;
    width: 40px;
}

.td-name {
    font-weight: 500;
    min-width: 150px;
}

.td-description {
    min-width: 120px;
}

.menu-item-description {
    background: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8em;
    display: inline-block;
}

.td-url {
    color: #6c757d;
    font-size: 0.9em;
    min-width: 200px;
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.td-order {
    text-align: center;
    width: 60px;
}

.menu-item-order {
    background: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8em;
    display: inline-block;
}

.td-status {
    text-align: center;
    width: 80px;
}

.td-actions {
    text-align: center;
    width: 120px;
}

.status-badge {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8em;
    display: inline-block;
}

.status-visible {
    background: #28a745;
    color: white;
}

.status-hidden {
    background: #dc3545;
    color: white;
}

.status-enabled {
    background: #17a2b8;
    color: white;
}

.status-disabled {
    background: #6c757d;
    color: white;
}

.batch-operations {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
}

.preview-panel {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    z-index: 1000;
    z-index: var(--z-preview);
    overflow-y: auto;
}

.preview-panel.show {
    right: 0;
}

.preview-header {
    padding: 15px;
    background: #007bff;
    color: white;
    font-weight: bold;
}

.preview-content {
    padding: 15px;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    z-index: var(--z-loading-overlay);
}

.loading-spinner {
    background: white;
    padding: 20px;
    border-radius: 5px;
    text-align: center;
}

.protected-category::before {
    content: "🛡️";
    position: absolute;
    top: 5px;
    right: 5px;
    font-size: 16px;
    /* z-index: 10; - 取消低优先级z-index */
}

.protected-menu {
    background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%) !important;
    border-left: 4px solid #ffc107 !important;
}

.protected-btn {
    opacity: 0.6;
    cursor: not-allowed !important;
    position: relative;
}

.protected-btn:hover {
    opacity: 0.8;
    transform: none !important;
    box-shadow: none !important;
}

.protected-btn::after {
    content: "🛡️";
    position: absolute;
    top: -2px;
    right: -2px;
    font-size: 10px;
    /* z-index: 10; - 取消低优先级z-index */
}

/* ========== admin-menu-management-help 页面样式 ========== */
.help-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.help-section {
    margin-bottom: 40px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.help-section h3 {
    color: #007bff;
    margin-bottom: 15px;
}

.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    padding: 8px 0;
    border-bottom: 1px solid #dee2e6;
}

.feature-list li:last-child {
    border-bottom: none;
}

.feature-icon {
    color: #28a745;
    margin-right: 10px;
}

.step-number {
    display: inline-block;
    width: 30px;
    height: 30px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    text-align: center;
    line-height: 30px;
    margin-right: 10px;
    font-weight: bold;
}

.screenshot {
    max-width: 100%;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    margin: 10px 0;
}

.tip-box {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
}

.warning-box {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
}

/* ========== admin-menu-permission-matrix 页面样式 ========== */
.matrix-container {
    padding: 20px;
    overflow-x: auto;
}

/* 组合状态的特殊样式 */
.permission-cell.access-granted.menu-visible {
    background: #e8f5e8;
    border: 2px solid #28a745;
    box-shadow: inset 0 0 0 1px #007bff;
}

/* 优化横向显示 */
.permission-matrix .menu-name {
    min-width: 250px;
    max-width: 300px;
}

.permission-matrix {
    width: 100%;
    min-width: 1200px;
    border-collapse: collapse;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.permission-matrix th,
.permission-matrix td {
    border: 1px solid #dee2e6;
    padding: 8px;
    text-align: center;
    vertical-align: middle;
}

.permission-matrix th {
    background: #2e6da4;
    color: white;
    font-weight: bold;
    position: sticky;
    top: 0;
    /* z-index: 10; - 取消低优先级z-index */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    font-size: 14px;
}

.permission-matrix .menu-name {
    background: #ffffff;
    font-weight: bold;
    text-align: left;
    min-width: 200px;
    position: sticky;
    left: 0;
    /* z-index: 5; - 取消低优先级z-index */
    border-right: 2px solid #dee2e6;
    color: #333;
}

.permission-matrix .category-header {
    background: #1c5380;
    color: white;
    font-weight: bold;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    font-size: 14px;
}

.permission-checkbox {
    transform: scale(1.3);
    cursor: pointer;
    margin: 2px;
}

.permission-cell {
    position: relative;
    min-width: 140px;
    text-align: center;
    vertical-align: middle;
    padding: 12px 8px;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
    min-height: 70px;
}

.permission-cell:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.permission-controls {
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 5px;
}

.permission-controls>div {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.permission-controls small {
    font-size: 0.9em;
    font-weight: 600;
    color: #333;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.9);
    line-height: 1.2;
}

.permission-cell.access-granted {
    background: #e8f5e8;
    border-left: 3px solid #28a745;
}

.permission-cell.access-denied {
    background: #ffeaea;
    border-left: 3px solid #dc3545;
}

.permission-cell.menu-visible {
    background: #e6f3ff;
    border-right: 3px solid #007bff;
}

.permission-cell.menu-hidden {
    background: #f8f9fa;
    border-right: 3px solid #6c757d;
}

.permission-cell.access-denied.menu-hidden {
    background: #f8f9fa;
    border: 2px solid #6c757d;
    opacity: 0.7;
}

.permission-cell.access-granted.menu-hidden {
    background: #e8f5e8;
    border-left: 3px solid #28a745;
    border-right: 3px solid #6c757d;
}

.permission-cell.access-denied.menu-visible {
    background: #ffeaea;
    border-left: 3px solid #dc3545;
    border-right: 3px solid #007bff;
}

.role-header {
    min-width: 120px;
    text-align: center;
    font-weight: bold;
}

.batch-controls {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
}

.legend {
    margin: 20px 0;
    padding: 15px;
    background: #e9ecef;
    border-radius: 5px;
}

.legend-item {
    display: inline-block;
    margin: 5px 15px 5px 0;
}

.legend-color {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-right: 5px;
    vertical-align: middle;
    border: 1px solid #ccc;
}

.loading-cell {
    background: #fff3cd;
}

/* .table-responsive {
    max-height: 75vh;
    overflow: auto;
    border: 1px solid #dee2e6;
    border-radius: 5px;
} - 注释掉冲突的样式，使用统一的overflow: visible */

.permission-matrix {
    margin-bottom: 0;
}

.role-header {
    min-width: 140px;
    max-width: 160px;
    padding: 10px 5px;
}

.role-header small {
    font-size: 0.8em;
    opacity: 0.9;
    font-weight: normal;
}

/* ========== admin-my-hazards 页面样式 ========== */

.status-rectifying {
    background-color: #ffc107;
    color: #212529;
}

.nav-tabs .badge {
    background-color: #337ab7;
    color: white;
    margin-left: 5px;
}

/* ========== admin-new-role-management 页面样式 ========== */
.role-table th,
.role-table td {
    vertical-align: middle !important;
}

.role-actions {
    white-space: nowrap;
}

.role-actions .btn {
    margin-right: 5px;
}

.role-actions .btn:last-child {
    margin-right: 0;
}

.system-role {
    font-weight: bold;
    color: #337ab7;
}

/* ========== admin-pending-notify-hazards 页面样式 ========== */

.lab-panel {
    margin-bottom: 20px;
}

.lab-panel .panel-heading {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.lab-panel .panel-title {
    margin: 0;
}

.preview-modal-body {
    max-height: 500px;
    overflow-y: auto;
}

/* ========== admin-rectification-review 页面样式 ========== */

.status-rectifying {
    background-color: #ffc107;
    color: #212529;
}

/* 优化筛选区域布局 */
#filterForm .row {
    display: flex;
    flex-wrap: wrap;
}

/* 美化下拉框样式 */
.bootstrap-select .dropdown-toggle {
    border-radius: 3px;
    border: 1px solid #ccc;
    background-color: #fff;
}

.status-closed {
    background-color: #28a745;
    color: white;
}

.status-submitted {
    background-color: #17a2b8;
    color: white;
}

.status-rejected {
    background-color: #dc3545;
    color: white;
}

.level-high {
    color: #dc3545;
    font-weight: bold;
}

.level-medium {
    color: #fd7e14;
}

.level-low {
    color: #28a745;
}

.bootstrap-select .dropdown-toggle .filter-option {
    white-space: normal;
    word-wrap: break-word;
}

/* 高级筛选面板样式 */
#advancedSearch {
    margin-bottom: 15px;
}

#advancedSearch .panel-title a {
    display: block;
    -webkit-text-decoration: none;
    text-decoration: none;
}

/* 表格式筛选样式 */
.table-filter {
    border: none;
    margin-bottom: 0;
}

.table-filter td {
    border: none;
    padding: 8px 10px;
    vertical-align: middle;
}

.table-filter .filter-label {
    text-align: right;
    padding-right: 5px;
}

.table-filter .filter-label label {
    margin-bottom: 0;
    font-weight: 600;
    color: #555;
    white-space: nowrap;
}

.table-filter .filter-label label i {
    margin-right: 5px;
    color: #337ab7;
}

#advancedSearch .form-group label {
    font-weight: 600;
    color: #555;
    margin-bottom: 5px;
}

#advancedSearch .form-group label i {
    margin-right: 5px;
    color: #337ab7;
}

#advancedSearch .btn {
    margin-left: 5px;
    border-radius: 3px;
}

#advancedSearch .btn-primary {
    background-color: #337ab7;
    border-color: #2e6da4;
}

#advancedSearch .btn-primary:hover {
    background-color: #286090;
    border-color: #204d74;
}

#advancedSearch .btn-default {
    background-color: #f5f5f5;
    border-color: #ddd;
}

#advancedSearch .btn-default:hover {
    background-color: #e6e6e6;
    border-color: #adadad;
}

.date-picker {
    background-color: #fff;
    cursor: pointer;
}

#filterForm .col-md-6 {
    padding: 0 10px;
}

.bootstrap-select .dropdown-toggle:hover,
.bootstrap-select .dropdown-toggle:focus {
    border-color: #66afe9;
    outline: 0;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
}

/* ========== 统一表格边框样式 - 现代简洁风格 ========== */

/* 统一表格边框样式 */
.table-bordered {
    border: 1px solid #e9ecef !important;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #e9ecef !important;
}

/* 表头加强边框和样式 - 确保所有表头居中 */
.table thead th,
.table th {
    border-bottom: 2px solid #dee2e6 !important;
    background-color: #f8f9fa !important;
    font-weight: 600 !important;
    text-align: center !important;
}

/* ========== 统一表格数据对齐规则 ========== */

/* 居中对齐的数据类型 */
.table th.text-center,
.table td.text-center,
.table .col-id,           /* 序号/ID列 */
.table .col-status,       /* 状态列 */
.table .col-actions,      /* 操作列 */
.table .col-date,         /* 日期列 */
.table .col-time,         /* 时间列 */
.table .col-count,        /* 计数列 */
.table .col-number,       /* 数字列 */
.table .col-level,        /* 等级列 */
.table .col-type,         /* 类型标签列 */
.table .col-boolean,      /* 是/否列 */
.table .col-name {        /* 简短名称列 */
    text-align: center !important;
}

/* 左对齐的数据类型 */
.table .col-title,        /* 标题列 */
.table .col-description,  /* 描述列 */
.table .col-content,      /* 内容列 */
.table .col-email,        /* 邮箱列 */
.table .col-phone,        /* 电话列 */
.table .col-address,      /* 地址列 */
.table .col-path,         /* 路径列 */
.table .col-text {        /* 文本列 */
    text-align: left !important;
}

/* 名称列根据内容长度决定对齐方式 */
.table .col-name {        /* 名称列 - 默认居中（适合简短名称如校区、楼宇等） */
    text-align: center !important;
}

/* 长名称列左对齐（如实验室名称、用户姓名等） */
.table .col-long-name {   /* 长名称列 */
    text-align: left !important;
}

/* 基于表头内容的智能对齐 - 居中对齐 */
.table tbody tr td:nth-child(1) {
    text-align: center !important; /* 第一列通常是序号/ID */
}

/* 操作列（最后一列）居中 */
.table tbody tr td:last-child {
    text-align: center !important;
}

/* 基于表头文字内容的智能对齐 */
.table thead th:contains("ID") ~ tbody tr td:nth-child(1),
.table thead th:contains("序号") ~ tbody tr td:nth-child(1),
.table thead th:contains("编号") ~ tbody tr td:nth-child(1),
.table thead th:contains("状态") ~ tbody tr td,
.table thead th:contains("等级") ~ tbody tr td,
.table thead th:contains("类型") ~ tbody tr td,
.table thead th:contains("数量") ~ tbody tr td,
.table thead th:contains("计数") ~ tbody tr td,
.table thead th:contains("时间") ~ tbody tr td,
.table thead th:contains("日期") ~ tbody tr td,
.table thead th:contains("校区") ~ tbody tr td,
.table thead th:contains("楼宇") ~ tbody tr td,
.table thead th:contains("部门") ~ tbody tr td,
.table thead th:contains("房间号") ~ tbody tr td,
.table thead th:contains("容纳人数") ~ tbody tr td,
.table thead th:contains("联系电话") ~ tbody tr td,
.table thead th:contains("操作") ~ tbody tr td {
    text-align: center !important;
}

/* ========== 智能表格对齐规则 - 无需页面设置CSS类 ========== */

/* 所有表头居中 - 最高优先级 */
.table thead th {
    text-align: center !important;
}

/* 通用表格数据对齐规则 */
.table tbody tr td:nth-child(1) {
    text-align: center !important; /* 第一列：ID/序号 */
}

.table tbody tr td:last-child {
    text-align: center !important; /* 最后一列：操作按钮 */
}

/* 根据表格列数智能对齐 */
/* 3列表格：ID + 名称 + 操作 */
.table tbody tr:has(td:nth-child(3):last-child) td:nth-child(2) {
    text-align: center !important; /* 名称居中 */
}

/* 4列表格：ID + 名称 + 描述 + 操作 */
.table tbody tr:has(td:nth-child(4):last-child) td:nth-child(2) {
    text-align: center !important; /* 名称居中 */
}
.table tbody tr:has(td:nth-child(4):last-child) td:nth-child(3) {
    text-align: left !important; /* 描述左对齐 */
}

/* 5列表格：ID + 名称1 + 名称2 + 描述 + 操作 */
.table tbody tr:has(td:nth-child(5):last-child) td:nth-child(2),
.table tbody tr:has(td:nth-child(5):last-child) td:nth-child(3) {
    text-align: center !important; /* 名称居中 */
}
.table tbody tr:has(td:nth-child(5):last-child) td:nth-child(4) {
    text-align: left !important; /* 描述左对齐 */
}

/* 6列表格：ID + 名称1 + 名称2 + 名称3 + 描述 + 操作 */
.table tbody tr:has(td:nth-child(6):last-child) td:nth-child(2),
.table tbody tr:has(td:nth-child(6):last-child) td:nth-child(3),
.table tbody tr:has(td:nth-child(6):last-child) td:nth-child(4) {
    text-align: center !important; /* 名称居中 */
}
.table tbody tr:has(td:nth-child(6):last-child) td:nth-child(5) {
    text-align: left !important; /* 描述左对齐 */
}

/* 备用方案：如果浏览器不支持:has()选择器 */
.table tbody tr td:nth-child(2) {
    text-align: center !important; /* 第二列默认居中 */
}
.table tbody tr td:nth-child(3) {
    text-align: center !important; /* 第三列默认居中 */
}
.table tbody tr td:nth-child(4) {
    text-align: left !important; /* 第四列默认左对齐（通常是描述） */
}
.table tbody tr td:nth-child(5) {
    text-align: left !important; /* 第五列默认左对齐 */
}

/* 移除内联样式后的CSS类 */
.select-all-label {
    font-weight: bold !important;
    color: #337ab7 !important;
    padding-left: 20px !important;
    margin-left: 0 !important;
}

.property-label {
    padding-left: 20px !important;
    margin-left: 0 !important;
}

.checkbox {
    padding-left: 0 !important;
    margin-left: 0 !important;
}

.caret.pull-right {
    margin-top: 8px !important;
}

/* 移除内联样式后的新CSS类 */
.role-actions-header {
    margin-bottom: 15px !important;
}

.filter-panel {
    margin-top: 15px !important;
}

.filter-table {
    margin-bottom: 10px !important;
}

.filter-label {
    width: 15% !important;
}

.filter-input {
    width: 35% !important;
}

.date-separator {
    padding-top: 6px !important;
}

.filter-actions {
    margin-top: 15px !important;
}

.debug-panel {
    margin-top: 15px !important;
}

/* 表格样式优化 */
.table th {
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: #f0f7fd;
}

/* 排序图标 */
th[data-sort] {
    cursor: pointer;
    position: relative;
}

th[data-sort]:after {
    content: '\f0dc';
    font-family: FontAwesome;
    margin-left: 5px;
    color: #ccc;
}

th.sort-asc:after {
    content: '\f0de';
    color: #333;
}

th.sort-desc:after {
    content: '\f0dd';
    color: #333;
}

/* 当下拉框打开时，增加其父元素的z-index */
.bootstrap-select.open {
    z-index: 1010 !important;
    z-index: var(--z-select) !important;
}

/* 确保日期选择器在上层 */
.datepicker {
    z-index: 1000 !important;
    z-index: var(--z-datepicker) !important;
}

/* 调整表单布局，防止重叠 */
#filterForm .row {
    margin-bottom: 25px !important;
}

/* 自定义下拉框位置样式 */
.custom-dropdown-position {
    box-shadow: 0 6px 12px rgba(0, 0, 0, .175) !important;
    border: 1px solid rgba(0, 0, 0, .15) !important;
    border-radius: 4px !important;
}

/* 确保下拉框选项可见 - 使用统一z-index变量 */
.bootstrap-select .dropdown-menu li {
    position: relative !important;
    z-index: 1010 !important;
    z-index: var(--z-select) !important;
}

/* 修复下拉框搜索框样式 */
.bootstrap-select .bs-searchbox {
    padding: 8px !important;
}

/* 确保下拉框在滚动时仍然可见 */
.bootstrap-select.open .dropdown-menu {
    display: block !important;
}

/* 防止下拉框被其他元素遮挡 - 使用统一z-index变量 */
body>.dropdown-menu {
    z-index: 1000 !important;
    z-index: var(--z-dropdown-menu) !important;
}

/* ========== admin-safety-card-images 页面样式 ========== */
.image-grid {
    display: flex !important;
    flex-wrap: wrap !important;
    margin-top: 15px;
    margin-left: -20px;
    /* 抵消第一列的左边距 */
    width: 100%;
}

.image-card {
    width: 180px !important;
    margin: 0 0 20px 20px !important;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    transition: transform 0.2s;
    flex-shrink: 0 !important;
    flex-grow: 0 !important;
    display: inline-block !important;
    /* 防止卡片被压缩 */
}

.image-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.image-card .image-container {
    height: 180px !important;
    width: 180px !important;
    overflow: hidden;
    display: flex !important;
    align-items: center;
    justify-content: center;
    background-color: #f8f8f8;
}

.image-card .image-container img {
    max-width: 160px;
    max-height: 160px;
    -o-object-fit: contain;
       object-fit: contain;
}

.image-card .image-info {
    padding: 8px;
    width: 180px !important;
    box-sizing: border-box !important;
}

.image-card .image-title {
    font-weight: bold;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
}

.image-card .image-category {
    display: inline-block;
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 12px;
    margin-bottom: 5px;
}

.image-card .image-category.danger {
    background-color: #f8d7da;
    color: #721c24;
}

.image-card .image-category.notice {
    background-color: #fff3cd;
    color: #856404;
}

.image-card .image-category.protection {
    background-color: #d4edda;
    color: #155724;
}

.image-card .image-actions {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
}

.image-card .image-actions .btn {
    padding: 2px 5px;
    font-size: 12px;
}

.pagination-container {
    margin-top: 20px;
    text-align: center;
}

/* 确保在小屏幕上也能显示多列 */

/* ========== admin-safety-card-preview 页面样式 ========== */
/* 其他样式 */
.mapping-info {
    margin-bottom: 20px;
}

/* 图片网格样式 */
.image-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 3px;
    margin-top: 10px;
    max-height: 600px;
    /* 增加高度，确保能显示更多图片 */
    overflow-y: auto;
    padding-right: 5px;
    padding-bottom: 10px;
}

.image-item .image-name {
    font-size: 12px;
    margin-top: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #333;
    max-width: 150px;
    /* 限制宽度 */
    line-height: 1.2;
    display: block;
    /* 显示名称 */
}

/* 复选框样式 */
.image-item .checkbox {
    margin: 0;
    padding: 0;
}

.image-item input[type="checkbox"] {
    margin: 0;
    padding: 0;
    transform: scale(1.5);
    /* 增大复选框 */
    position: absolute;
    top: 5px;
    left: 5px;
}

/* 选中图片预览样式 */
.selected-images-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
    margin-bottom: 15px;
    justify-content: flex-start;
}

/* 其他样式 */
.mapping-info {
    margin-bottom: 20px;
}

.mapping-table {
    width: 100%;
    border-collapse: collapse;
}

.mapping-table th,
.mapping-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.mapping-table th {
    background-color: #f5f5f5;
}

.image-item:hover {
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
    transform: scale(1.02);
}

.image-item img {
    max-width: 100%;
    height: 120px;
    width: 120px;
    -o-object-fit: contain;
       object-fit: contain;
    display: block;
    margin: 0 auto;
    margin-top: 5px;
}



.image-item .checkbox label {
    display: block;
    cursor: pointer;
    width: 100%;
    height: 100%;
    padding: 0;
    position: relative;
}



.image-item input[type="checkbox"]:checked+img {
    border: 1px solid #28a745;
    box-shadow: 0 0 2px rgba(40, 167, 69, 0.5);
}

.category-title {
    margin-top: 15px;
    font-weight: bold;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.selected-image {
    width: 160px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px;
    text-align: center;
}

.selected-image img {
    max-width: 100%;
    max-height: 120px;
    width: 120px;
    height: 120px;
    -o-object-fit: contain;
       object-fit: contain;
}

.selected-image .image-name {
    font-size: 12px;
    margin-top: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
    line-height: 1.2;
}

/* 添加选中图片的高亮样式 */
.image-item.selected {
    border: 1px solid #28a745;
    box-shadow: 0 0 2px rgba(40, 167, 69, 0.5);
    background-color: #f0fff0;
}

/* 添加图片网格容器样式 */
#danger-tab .image-grid,
#notice-tab .image-grid,
#protection-tab .image-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 10px;
    padding: 10px;
}

/* 图片项样式 */
.image-item {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px;
    margin-bottom: 10px;
    background-color: #fff;
    transition: all 0.2s ease;
}

.image-item:hover {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
}

.image-item .image-name {
    font-size: 12px;
    text-align: center;
    margin-top: 5px;
    word-break: break-word;
}

/* 选项卡激活状态样式 */
.nav-tabs>li.active>a {
    font-weight: bold;
}

/* 左侧预览区域样式 */
.selected-images-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.selected-image {
    margin-bottom: 10px;
    text-align: center;
    width: 160px;
}

.selected-image .image-name {
    font-size: 12px;
    margin-top: 5px;
    word-break: break-word;
}

/* ========== admin-safety-ledger-preview 页面样式 ========== */

    /* ========== admin-special-inspection-check-new 页面样式 ========== */
    /* 检查依据和检查要点相关样式 */
    .selected-check-points-container {
        margin-top: 10px !important;
        border: 1px solid #eee !important;
        border-radius: 4px !important;
        padding: 10px !important;
        background-color: #f9f9f9 !important;
        min-height: 100px !important;
        max-height: 200px !important;
        overflow-y: auto !important;
        display: block !important;
    }

    /* ========== admin-statistics-by-activity 页面样式 ========== */
    .search-box {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }

    /* ========== admin-statistics-by-hazard-status 页面样式 ========== */
    .search-box {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }

    /* ========== admin-statistics-by-hazard-type 页面样式 ========== */
    .search-box {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }

    .table-responsive {
        margin-top: 20px;
    }

    .sort-icon {
        cursor: pointer;
        margin-left: 5px;
    }

    /* ========== admin-unified-permission-matrix 页面样式 ========== */
    .matrix-container {
        padding: 20px;
        overflow-x: auto;
    }

    .permission-matrix {
        width: 100%;
        min-width: 1500px;
        border-collapse: collapse;
        background: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }

    .permission-cell {
        min-width: 120px;
        padding: 4px;
    }

    /* 一级菜单权限样式 */
    .category-permission-section {
        border-bottom: 3px solid #28a745;
        margin-bottom: 20px;
    }

    .category-permission-section .permission-cell {
        background: #f0fff0;
    }

    .category-permission-section th {
        background: #28a745;
    }

    /* 页面权限样式 */
    .page-permission-section {
        border-bottom: 3px solid #2e6da4;
        margin-bottom: 20px;
    }

    .page-permission-section .permission-cell {
        background: #f8f9fa;
    }

    /* 功能权限样式 */
    .operation-permission-section {
        margin-top: 20px;
    }

    .operation-permission-section .permission-cell {
        background: #fff8f0;
    }

    .operation-permission-section th {
        background: #d68910;
    }

    /* 分类行样式 */
    .category-row td {
        background: #e9ecef;
        font-weight: bold;
        color: #495057;
        text-align: left;
        padding: 10px;
    }

    .resource-category-row td {
        background: #fff3cd;
        font-weight: bold;
        color: #856404;
        text-align: left;
        padding: 10px;
    }

    .operation-allowed {
        background-color: #d1ecf1 !important;
        border-bottom: 2px solid #17a2b8;
    }

    .operation-denied {
        background-color: #f5c6cb !important;
        border-bottom: 2px solid #dc3545;
    }

    /* .table-responsive {
        max-height: 80vh;
        overflow: auto;
        border: 1px solid #dee2e6;
        border-radius: 5px;
    } - 注释掉冲突的样式，使用统一的overflow: visible */

    .operation-section-title {
        color: #d68910;
        border-left-color: #d68910;
    }

    /* ========== admin-user-form 页面样式 ========== */
    .checkbox-group {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid #ddd;
        padding: 10px;
        border-radius: 4px;
    }

    .checkbox {
        margin-bottom: 5px;
    }

    .checkbox input[type="checkbox"] {
        margin-right: 5px;
    }

    /* ========== emails-rectification-notification 页面样式 ========== */
    .email-notification-body {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }

    .header {
        text-align: center;
        margin-bottom: 30px;
    }

    .header h1 {
        color: #1a5276;
        margin-bottom: 10px;
    }

    .content {
        margin-bottom: 30px;
    }

    .lab-info {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }

    .hazard-list {
        margin-bottom: 30px;
    }

    .hazard-item {
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
    }

    .hazard-level {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 12px;
        margin-right: 10px;
    }

    .level-high {
        background-color: #dc3545;
        color: white;
    }

    .level-medium {
        background-color: #fd7e14;
        color: white;
    }

    .level-low {
        background-color: #28a745;
        color: white;
    }

    .footer {
        margin-top: 30px;
        text-align: center;
        font-size: 12px;
        color: #777;
    }

    /* ========== inspection-check-form 页面样式 ========== */
    .check-item-panel .panel-title a {
        display: block;
        padding: 5px 0;
    }

    .check-item-panel .panel-title a:hover {
        -webkit-text-decoration: none;
        text-decoration: none;
    }

    .check-item-panel .panel-title a:focus {
        -webkit-text-decoration: none;
        text-decoration: none;
    }

    .check-item-panel .panel-title a .fa-chevron-right {
        transition: transform 0.3s;
    }

    .check-item-panel .panel-title a:not(.collapsed) .fa-chevron-right {
        transform: rotate(90deg);
    }

    .item-path {
        color: #777;
        font-weight: normal;
    }

    .check-points-container {
        margin-top: 10px;
        border: 1px solid #eee;
        border-radius: 4px;
        padding: 15px;
        background-color: #f9f9f9;
    }

    .check-point-item {
        margin-bottom: 10px;
    }

    .check-point-item .checkbox label {
        font-weight: normal;
        display: block;
        padding: 10px;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    /* ========== inspection-select-check-points 页面样式 ========== */

    .check-point-item {
        margin-bottom: 10px;
    }

    .check-point-item .checkbox {
        margin: 0;
    }

    .check-point-item .checkbox label {
        font-weight: normal;
        display: block;
        padding: 10px;
        background-color: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .check-point-item .checkbox label:hover {
        background-color: #f0f0f0;
    }

    .check-all-container {
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

    /* ========== inspection-select-laboratory 页面样式 ========== */
    .ml-2 {
        margin-left: 10px;
    }

    /* ========== main-admin-dashboard 页面样式 ========== */

    /* ========== main-new-admin-dashboard 页面样式 ========== */
    /* 页面标题区域 - 保持蓝色主题 */
    .page-header-section {
        background: #2e6da4;
        color: white;
        padding: 20px 25px;
        border-radius: 4px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(46, 109, 164, 0.2);
    }

    /* ========== main-template-chooser 页面样式 ========== */
    .template-option {
        margin-bottom: 30px;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .template-option:hover {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transform: translateY(-5px);
    }

    .template-header {
        background-color: #f5f5f5;
        padding: 15px;
        border-bottom: 1px solid #ddd;
    }

    .template-title {
        margin: 0;
        font-size: 18px;
        font-weight: bold;
    }

    .template-description {
        margin-top: 5px;
        color: #777;
    }

    .template-preview {
        padding: 15px;
        background-color: #fff;
        text-align: center;
    }

    .template-preview img {
        max-width: 100%;
        height: auto;
        border: 1px solid #eee;
    }

    .template-actions {
        padding: 15px;
        background-color: #f9f9f9;
        text-align: center;
    }

    .btn-select {
        padding: 8px 20px;
    }

    /* ========== 静态CSS文件完全迁移到Webpack ========== */

    /* 原 custom.css - Bootstrap Select 下拉框修复 - 使用统一z-index变量 */
    .bootstrap-select .dropdown-menu {
        z-index: 1010 !important;
        z-index: var(--z-select) !important;
        position: fixed !important;
        max-height: 300px !important;
        overflow-y: auto !important;
        width: auto !important;
        min-width: 200px !important;
    }

    .bootstrap-select .bs-searchbox {
        padding: 8px !important;
        position: relative !important;
        /* z-index: 1 !important; - 取消低优先级z-index */
    }

    .bootstrap-select .bs-searchbox .form-control {
        position: relative !important;
        /* z-index: 2 !important; - 取消低优先级z-index */
        width: 100% !important;
        margin: 0 !important;
        padding: 6px 12px !important;
        border: 1px solid #ccc !important;
        border-radius: 4px !important;
        box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075) !important;
        transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s !important;
    }

/*# sourceMappingURL=app.821aee80a78779892534.css.map*/