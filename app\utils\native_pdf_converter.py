#!/usr/bin/env python3
"""
本地化PDF转换模块
使用纯Python实现，不依赖外部应用程序
"""

import os
import tempfile
from io import BytesIO
from flask import current_app
from docx import Document

# 尝试导入weasyprint，如果失败则使用reportlab
try:
    import weasyprint
    WEASYPRINT_AVAILABLE = True
except (ImportError, OSError) as e:
    WEASYPRINT_AVAILABLE = False
    print(f"weasyprint不可用，将使用reportlab作为备用方案: {str(e)}")

# 导入reportlab
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import cm
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("reportlab不可用")


class NativePdfConverter:
    """本地化PDF转换器"""
    
    @staticmethod
    def docx_to_html(docx_file):
        """将Word文档转换为HTML
        
        Args:
            docx_file: Word文档的BytesIO对象或文件路径
            
        Returns:
            str: HTML内容
        """
        try:
            # 读取Word文档
            if isinstance(docx_file, (str, bytes)):
                doc = Document(docx_file)
            else:
                doc = Document(docx_file)
            
            # 构建HTML内容
            html_parts = []
            html_parts.append("""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>文档</title>
    <style>
        @page {
            size: A4;
            margin: 2cm;
        }
        body {
            font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        h1 {
            text-align: center;
            font-size: 18pt;
            font-weight: bold;
            margin: 20px 0;
            color: #000;
        }
        h2 {
            font-size: 14pt;
            font-weight: bold;
            margin: 15px 0 10px 0;
            color: #000;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }
        h3 {
            font-size: 13pt;
            font-weight: bold;
            margin: 12px 0 8px 0;
            color: #333;
        }
        p {
            margin: 8px 0;
            text-align: justify;
        }
        .meta-info {
            margin: 15px 0;
            padding: 10px;
            background-color: #f9f9f9;
            border-left: 4px solid #007acc;
        }
        .meta-info p {
            margin: 5px 0;
        }
        .chart-placeholder {
            text-align: center;
            margin: 20px 0;
            padding: 40px;
            border: 2px dashed #ccc;
            background-color: #f9f9f9;
            color: #666;
        }
        .signature {
            text-align: right;
            margin-top: 40px;
            font-style: italic;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
""")
            
            # 处理文档内容
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if not text:
                    continue
                
                # 根据样式判断标题级别
                style_name = paragraph.style.name if paragraph.style else ""
                
                if "Heading 1" in style_name or "标题 1" in style_name:
                    html_parts.append(f"<h1>{text}</h1>")
                elif "Heading 2" in style_name or "标题 2" in style_name:
                    html_parts.append(f"<h2>{text}</h2>")
                elif "Heading 3" in style_name or "标题 3" in style_name:
                    html_parts.append(f"<h3>{text}</h3>")
                else:
                    # 检查是否是特殊格式的段落
                    if "：" in text and any(keyword in text for keyword in ["检查活动", "检查时间", "检查类型", "实验室名称", "房间号", "部门", "负责人", "联系电话"]):
                        if not any("meta-info" in part for part in html_parts[-3:]):
                            html_parts.append('<div class="meta-info">')
                        html_parts.append(f"<p><strong>{text.split('：')[0]}：</strong>{text.split('：', 1)[1] if '：' in text else ''}</p>")
                    elif "注：" in text or "图表" in text:
                        html_parts.append(f'<div class="chart-placeholder">{text}</div>')
                    elif text.startswith("报告生成时间：") or text.startswith("生成时间："):
                        html_parts.append(f'<div class="signature">{text}</div>')
                    else:
                        html_parts.append(f"<p>{text}</p>")
            
            # 关闭meta-info div（如果有的话）
            if any("meta-info" in part for part in html_parts):
                html_parts.append("</div>")
            
            html_parts.append("</body></html>")
            
            return "\n".join(html_parts)
            
        except Exception as e:
            print(f"Word转HTML失败: {str(e)}")
            # 返回一个基本的HTML模板
            return f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>文档转换错误</title>
    <style>
        body {{ font-family: "Microsoft YaHei", Arial, sans-serif; margin: 2cm; }}
        h1 {{ text-align: center; }}
    </style>
</head>
<body>
    <h1>文档转换错误</h1>
    <p>文档转换过程中发生错误：{str(e)}</p>
    <p>请检查Word文档格式是否正确。</p>
</body>
</html>
"""
    
    @staticmethod
    def html_to_pdf(html_content):
        """将HTML转换为PDF

        Args:
            html_content: HTML内容字符串

        Returns:
            BytesIO: PDF文档的内存文件对象
        """
        if WEASYPRINT_AVAILABLE:
            try:
                # 使用weasyprint转换HTML为PDF
                pdf_bytes = weasyprint.HTML(string=html_content).write_pdf()

                # 返回BytesIO对象
                pdf_buffer = BytesIO(pdf_bytes)
                pdf_buffer.seek(0)
                return pdf_buffer

            except Exception as e:
                print(f"weasyprint HTML转PDF失败: {str(e)}")
                # 如果weasyprint失败，尝试reportlab
                pass

        # 使用reportlab作为备用方案
        if REPORTLAB_AVAILABLE:
            try:
                return NativePdfConverter.html_to_pdf_reportlab(html_content)
            except Exception as e:
                print(f"reportlab HTML转PDF失败: {str(e)}")
                raise Exception(f"HTML转PDF失败: {str(e)}")
        else:
            raise Exception("没有可用的PDF转换库")

    @staticmethod
    def html_to_pdf_reportlab(html_content):
        """使用reportlab将HTML转换为PDF（简化版）

        Args:
            html_content: HTML内容字符串

        Returns:
            BytesIO: PDF文档的内存文件对象
        """
        try:
            # 注册中文字体（复用逻辑）
            try:
                import platform
                system = platform.system()

                if system == "Windows":
                    font_paths = [
                        "C:/Windows/Fonts/simsun.ttc",
                        "C:/Windows/Fonts/msyh.ttc",
                        "C:/Windows/Fonts/simhei.ttf",
                    ]
                elif system == "Darwin":
                    font_paths = [
                        "/System/Library/Fonts/PingFang.ttc",
                        "/Library/Fonts/Arial Unicode MS.ttf",
                    ]
                else:
                    font_paths = [
                        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                        "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                    ]

                chinese_font = None
                for font_path in font_paths:
                    if os.path.exists(font_path):
                        try:
                            pdfmetrics.registerFont(TTFont('ChineseFont2', font_path))
                            chinese_font = 'ChineseFont2'
                            break
                        except Exception:
                            continue

                if not chinese_font:
                    chinese_font = 'Helvetica'

            except Exception:
                chinese_font = 'Helvetica'

            # 创建PDF缓冲区
            buffer = BytesIO()

            # 创建PDF文档
            doc = SimpleDocTemplate(buffer, pagesize=A4,
                                  rightMargin=2*cm, leftMargin=2*cm,
                                  topMargin=2*cm, bottomMargin=2*cm)

            # 获取样式
            styles = getSampleStyleSheet()

            # 创建自定义样式（使用中文字体）
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1,  # 居中
                fontName=chinese_font
            )

            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                spaceBefore=20,
                fontName=chinese_font
            )

            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=12,
                fontName=chinese_font
            )

            # 解析HTML内容（简化版）
            story = []

            # 简单的HTML解析
            import re

            # 提取标题
            title_match = re.search(r'<h1[^>]*>(.*?)</h1>', html_content, re.DOTALL)
            if title_match:
                title_text = re.sub(r'<[^>]+>', '', title_match.group(1)).strip()
                story.append(Paragraph(title_text, title_style))
                story.append(Spacer(1, 20))

            # 提取二级标题和段落
            content_parts = re.split(r'<h2[^>]*>(.*?)</h2>', html_content)

            for i, part in enumerate(content_parts):
                if i % 2 == 1:  # 这是标题
                    heading_text = re.sub(r'<[^>]+>', '', part).strip()
                    if heading_text:
                        story.append(Paragraph(heading_text, heading_style))
                elif i % 2 == 0 and i > 0:  # 这是内容
                    # 提取段落
                    paragraphs = re.findall(r'<p[^>]*>(.*?)</p>', part, re.DOTALL)
                    for para in paragraphs:
                        para_text = re.sub(r'<[^>]+>', '', para).strip()
                        if para_text and '注：' not in para_text:
                            story.append(Paragraph(para_text, normal_style))
                            story.append(Spacer(1, 6))

            # 如果没有解析到内容，添加默认内容
            if len(story) <= 2:
                story.append(Paragraph("文档内容", title_style))
                story.append(Spacer(1, 20))
                story.append(Paragraph("本文档由系统自动生成", normal_style))

            # 构建PDF
            doc.build(story)

            # 返回PDF数据
            buffer.seek(0)
            return buffer

        except Exception as e:
            print(f"reportlab HTML转PDF失败: {str(e)}")
            raise Exception(f"reportlab HTML转PDF失败: {str(e)}")
    
    @staticmethod
    def docx_to_pdf(docx_file):
        """直接将Word文档转换为PDF
        
        Args:
            docx_file: Word文档的BytesIO对象或文件路径
            
        Returns:
            BytesIO: PDF文档的内存文件对象
        """
        try:
            # 第一步：Word转HTML
            html_content = NativePdfConverter.docx_to_html(docx_file)
            
            # 第二步：HTML转PDF
            pdf_buffer = NativePdfConverter.html_to_pdf(html_content)
            
            return pdf_buffer
            
        except Exception as e:
            print(f"Word转PDF失败: {str(e)}")
            raise Exception(f"Word转PDF失败: {str(e)}")
    
    @staticmethod
    def create_report_pdf_reportlab(title, activity_data, statistics_data, content_data):
        """使用reportlab直接创建通报PDF

        Args:
            title: 通报标题
            activity_data: 活动信息
            statistics_data: 统计数据
            content_data: 内容数据

        Returns:
            BytesIO: PDF文档的内存文件对象
        """
        try:
            # 注册中文字体
            try:
                # 尝试注册系统中文字体
                import platform
                system = platform.system()

                if system == "Windows":
                    # Windows系统字体路径
                    font_paths = [
                        "C:/Windows/Fonts/simsun.ttc",  # 宋体
                        "C:/Windows/Fonts/msyh.ttc",    # 微软雅黑
                        "C:/Windows/Fonts/simhei.ttf",  # 黑体
                    ]
                elif system == "Darwin":  # macOS
                    font_paths = [
                        "/System/Library/Fonts/PingFang.ttc",
                        "/Library/Fonts/Arial Unicode MS.ttf",
                    ]
                else:  # Linux
                    font_paths = [
                        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                        "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                    ]

                # 尝试注册字体
                chinese_font = None
                for font_path in font_paths:
                    if os.path.exists(font_path):
                        try:
                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                            chinese_font = 'ChineseFont'
                            print(f"成功注册中文字体: {font_path}")
                            break
                        except Exception as e:
                            print(f"注册字体失败 {font_path}: {str(e)}")
                            continue

                # 如果没有找到字体，使用Helvetica（可能不支持中文）
                if not chinese_font:
                    chinese_font = 'Helvetica'
                    print("警告: 未找到中文字体，使用Helvetica，可能无法正确显示中文")

            except Exception as e:
                chinese_font = 'Helvetica'
                print(f"字体注册过程出错: {str(e)}")

            # 创建PDF缓冲区
            buffer = BytesIO()

            # 创建PDF文档
            doc = SimpleDocTemplate(buffer, pagesize=A4,
                                  rightMargin=2*cm, leftMargin=2*cm,
                                  topMargin=2*cm, bottomMargin=2*cm)

            # 获取样式
            styles = getSampleStyleSheet()

            # 创建自定义样式（使用中文字体）
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1,  # 居中
                fontName=chinese_font
            )

            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                spaceBefore=20,
                fontName=chinese_font
            )

            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=12,
                fontName=chinese_font
            )

            meta_style = ParagraphStyle(
                'MetaInfo',
                parent=styles['Normal'],
                fontSize=11,
                spaceAfter=6,
                fontName=chinese_font,
                leftIndent=20
            )

            # 构建PDF内容
            story = []

            # 标题
            story.append(Paragraph(title, title_style))
            story.append(Spacer(1, 20))

            # 活动信息
            story.append(Paragraph(f"<b>检查活动：</b>{activity_data.get('name', '')}", meta_style))
            story.append(Paragraph(f"<b>检查时间：</b>{activity_data.get('date_range', '')}", meta_style))
            story.append(Paragraph(f"<b>检查类型：</b>{activity_data.get('type', '')}", meta_style))
            story.append(Spacer(1, 20))

            # 一、检查概述
            story.append(Paragraph("一、检查概述", heading_style))
            story.append(Paragraph(content_data.get('overview', ''), normal_style))
            story.append(Spacer(1, 15))

            # 统计表格
            if statistics_data:
                table_data = [
                    ['统计项目', '数量'],
                    ['检查实验室总数', str(statistics_data.get('total_labs', 0))],
                    ['正常实验室', str(statistics_data.get('normal_labs', 0))],
                    ['优秀实验室', str(statistics_data.get('excellent_labs', 0))],
                    ['存在问题实验室', str(statistics_data.get('issue_labs', 0))],
                    ['发现隐患总数', str(statistics_data.get('total_hazards', 0))],
                ]

                table = Table(table_data, colWidths=[8*cm, 4*cm])
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, -1), chinese_font),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('FONTSIZE', (0, 1), (-1, -1), 11),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                story.append(table)
                story.append(Spacer(1, 20))

            # 二、隐患统计
            story.append(Paragraph("二、隐患统计", heading_style))
            story.append(Paragraph(content_data.get('hazard_summary', ''), normal_style))
            story.append(Spacer(1, 15))

            # 隐患统计表格
            if statistics_data:
                hazard_table_data = [
                    ['隐患等级', '数量'],
                    ['低风险隐患', str(statistics_data.get('low_hazards', 0))],
                    ['中风险隐患', str(statistics_data.get('medium_hazards', 0))],
                    ['高风险隐患', str(statistics_data.get('high_hazards', 0))],
                ]

                hazard_table = Table(hazard_table_data, colWidths=[8*cm, 4*cm])
                hazard_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, -1), chinese_font),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('FONTSIZE', (0, 1), (-1, -1), 11),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                story.append(hazard_table)
                story.append(Spacer(1, 20))

            # 三、整改情况
            story.append(Paragraph("三、整改情况", heading_style))
            story.append(Paragraph(content_data.get('rectification_summary', ''), normal_style))
            story.append(Spacer(1, 15))

            # 整改统计表格
            if statistics_data:
                rectification_table_data = [
                    ['整改状态', '数量'],
                    ['已完成整改', str(statistics_data.get('rectified_hazards', 0))],
                    ['正在整改', str(statistics_data.get('rectifying_hazards', 0))],
                    ['待整改', str(statistics_data.get('pending_hazards', 0))],
                ]

                rectification_table = Table(rectification_table_data, colWidths=[8*cm, 4*cm])
                rectification_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, -1), chinese_font),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('FONTSIZE', (0, 1), (-1, -1), 11),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                story.append(rectification_table)
                story.append(Spacer(1, 20))

            # 四、结论和建议
            story.append(Paragraph("四、结论和建议", heading_style))
            story.append(Paragraph(content_data.get('conclusion', ''), normal_style))
            story.append(Spacer(1, 30))

            # 生成时间
            signature_style = ParagraphStyle(
                'Signature',
                parent=styles['Normal'],
                fontSize=10,
                alignment=2,  # 右对齐
                fontName=chinese_font,
                textColor=colors.grey
            )
            story.append(Paragraph(f"报告生成时间：{activity_data.get('generated_time', '')}", signature_style))

            # 构建PDF
            doc.build(story)

            # 返回PDF数据
            buffer.seek(0)
            return buffer

        except Exception as e:
            print(f"reportlab创建PDF失败: {str(e)}")
            raise Exception(f"reportlab创建PDF失败: {str(e)}")

    @staticmethod
    def create_report_html(title, activity_data, statistics_data, content_data):
        """创建格式化的通报HTML
        
        Args:
            title: 通报标题
            activity_data: 活动信息
            statistics_data: 统计数据
            content_data: 内容数据
            
        Returns:
            str: 格式化的HTML内容
        """
        html_template = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{title}</title>
    <style>
        @page {{
            size: A4;
            margin: 2cm;
        }}
        body {{
            font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }}
        h1 {{
            text-align: center;
            font-size: 18pt;
            font-weight: bold;
            margin: 20px 0;
            color: #000;
        }}
        h2 {{
            font-size: 14pt;
            font-weight: bold;
            margin: 15px 0 10px 0;
            color: #000;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }}
        .meta-info {{
            margin: 15px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border-left: 4px solid #007acc;
            border-radius: 5px;
        }}
        .meta-info p {{
            margin: 5px 0;
        }}
        .statistics {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }}
        .stat-box {{
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }}
        .stat-box h3 {{
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 13pt;
        }}
        .stat-number {{
            font-size: 24pt;
            font-weight: bold;
            color: #007acc;
            margin: 5px 0;
        }}
        .chart-placeholder {{
            text-align: center;
            margin: 20px 0;
            padding: 40px;
            border: 2px dashed #ccc;
            background-color: #f9f9f9;
            color: #666;
            border-radius: 5px;
        }}
        .signature {{
            text-align: right;
            margin-top: 40px;
            font-style: italic;
            color: #666;
        }}
        p {{
            margin: 8px 0;
            text-align: justify;
        }}
    </style>
</head>
<body>
    <h1>{title}</h1>
    
    <div class="meta-info">
        <p><strong>检查活动：</strong>{activity_data.get('name', '')}</p>
        <p><strong>检查时间：</strong>{activity_data.get('date_range', '')}</p>
        <p><strong>检查类型：</strong>{activity_data.get('type', '')}</p>
    </div>
    
    <h2>一、检查概述</h2>
    <p>{content_data.get('overview', '')}</p>
    
    <div class="statistics">
        <div class="stat-box">
            <h3>实验室检查情况</h3>
            <div class="stat-number">{statistics_data.get('total_labs', 0)}</div>
            <p>检查实验室总数</p>
        </div>
        <div class="stat-box">
            <h3>隐患发现情况</h3>
            <div class="stat-number">{statistics_data.get('total_hazards', 0)}</div>
            <p>发现隐患总数</p>
        </div>
    </div>
    
    <h2>二、隐患统计</h2>
    <p>{content_data.get('hazard_summary', '')}</p>
    
    <div class="chart-placeholder">
        <p><strong>隐患等级分布图</strong></p>
        <p>（实际报告中将包含图表）</p>
    </div>
    
    <h2>三、整改情况</h2>
    <p>{content_data.get('rectification_summary', '')}</p>
    
    <h2>四、结论和建议</h2>
    <p>{content_data.get('conclusion', '')}</p>
    
    <div class="signature">
        <p>报告生成时间：{activity_data.get('generated_time', '')}</p>
    </div>
</body>
</html>
"""
        return html_template


# 便捷函数
def convert_docx_to_pdf_native(docx_file):
    """便捷函数：将Word文档转换为PDF（本地化实现）
    
    Args:
        docx_file: Word文档的BytesIO对象或文件路径
        
    Returns:
        BytesIO: PDF文档的内存文件对象
    """
    return NativePdfConverter.docx_to_pdf(docx_file)


def create_report_pdf_native(title, activity_data, statistics_data, content_data):
    """便捷函数：创建格式化的通报PDF（本地化实现）

    Args:
        title: 通报标题
        activity_data: 活动信息
        statistics_data: 统计数据
        content_data: 内容数据

    Returns:
        BytesIO: PDF文档的内存文件对象
    """
    # 优先使用reportlab直接生成PDF
    if REPORTLAB_AVAILABLE:
        try:
            return NativePdfConverter.create_report_pdf_reportlab(title, activity_data, statistics_data, content_data)
        except Exception as e:
            print(f"reportlab直接生成PDF失败: {str(e)}")

    # 备用方案：HTML转PDF
    try:
        html_content = NativePdfConverter.create_report_html(title, activity_data, statistics_data, content_data)
        return NativePdfConverter.html_to_pdf(html_content)
    except Exception as e:
        print(f"HTML转PDF失败: {str(e)}")
        raise Exception(f"PDF生成失败: {str(e)}")
