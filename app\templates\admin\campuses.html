{% extends "base.html" %}

{% block title %}校区管理 - 实验室安全检查系统{% endblock %}

{% block content %}
<div class="container">
    <h1>校区管理</h1>
    <div class="mb-3">
        {% if current_user.is_admin() %}
        <a href="{{ url_for('admin.add_campus') }}" class="btn btn-success">新建校区</a>
        {% endif %}
    </div>
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover" id="campusesTable">
            <thead>
                <tr>
                    <th>ID <span class="sort-icon" data-sort="id">⇅</span></th>
                    <th>校区 <span class="sort-icon" data-sort="name">⇅</span></th>
                    <th>描述</th>
                    {% if current_user.is_admin() %}
                    <th>操作</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for campus in campuses %}
                <tr>
                    <td>{{ campus.id }}</td>
                    <td>{{ campus.name }}</td>
                    <td>{{ campus.description }}</td>
                    {% if current_user.is_admin() %}
                    <td>
                        <a href="{{ url_for('admin.edit_campus', id=campus.id) }}" class="btn btn-sm btn-primary">编辑</a>
                        <a href="javascript:void(0)" class="btn btn-sm btn-danger delete-with-captcha"
                            data-url="{{ url_for('admin.delete_campus', id=campus.id) }}" data-title="确认删除校区"
                            data-message="确定要删除校区 &quot;{{ campus.name }}&quot; 吗？此操作不可恢复！">删除</a>
                    </td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<!-- 引入campuses.html页面专用模块化JavaScript -->
{% for js_url in webpack_js('campuses') %}
<script src="{{ js_url }}"></script>
{% endfor %}
<script>
    // 页面加载完成后初始化campuses.html功能
    document.addEventListener('DOMContentLoaded', function () {
        if (window.CampusesPage) {
            window.CampusesPage.init();
        }
    });

    // AJAX加载时重新初始化
    if (window.UnifiedAjaxMenu) {
        window.UnifiedAjaxMenu.registerPageInitializer('/admin/campuses', function () {
            if (window.CampusesPage) {
                window.CampusesPage.init();
            }
        });
    }
</script>
{% endblock %}