/**
 * 检查活动管理页面模块
 */
class InspectionActivitiesPage {
  constructor() {
    this.isInitialized = false;
    this.dataLoaded = false;
  }

  /**
   * 初始化检查活动管理页面
   */
  init() {
    if (this.isInitialized) {
      console.log('检查活动管理页面已初始化，跳过重复初始化');
      return;
    }

    console.log('🔍 初始化检查活动管理页面');

    // 等待DOM准备就绪
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.initializeFeatures());
    } else {
      this.initializeFeatures();
    }

    this.isInitialized = true;
  }

  /**
   * 初始化页面功能
   */
  initializeFeatures() {
    console.log('🔧 初始化检查活动管理页面功能');

    // 初始化各种功能
    this.initActivityActions();
    this.initReportActions();
    this.initRecycleBin();
    this.initDataLoading();
    this.initActionButtons();

    // 定义全局函数（兼容性）
    this.defineGlobalFunctions();

    console.log('✅ 检查活动管理页面功能初始化完成');
  }

  /**
   * 初始化活动操作功能
   */
  initActivityActions() {
    // 停止检查活动函数
    window.stopInspectionActivity = (activityId) => {
      if (confirm('确定要停止这个检查活动吗？')) {
        fetch(`/admin/inspection_activities/${activityId}/stop`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
          }
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            alert('检查活动已停止');
            location.reload();
          } else {
            alert('停止失败：' + data.message);
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('停止失败');
        });
      }
    };

    // 重新激活检查活动函数
    window.reactivateInspectionActivity = (activityId) => {
      if (confirm('确定要重新激活这个检查活动吗？')) {
        fetch(`/admin/inspection_activities/${activityId}/reactivate`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
          }
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            alert('检查活动已重新激活');
            location.reload();
          } else {
            alert('重新激活失败：' + data.message);
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('重新激活失败');
        });
      }
    };

    console.log('✅ 停止检查活动函数已定义');
    console.log('✅ 重新激活检查活动函数已定义');
  }

  /**
   * 初始化报告操作功能
   */
  initReportActions() {
    // 发布通报
    window.publishReport = (activityId) => {
      if (confirm('确定要发布这个通报吗？发布后将无法修改。')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/publish_report/${activityId}`;
        
        // 添加CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrfToken) {
          const csrfInput = document.createElement('input');
          csrfInput.type = 'hidden';
          csrfInput.name = 'csrf_token';
          csrfInput.value = csrfToken;
          form.appendChild(csrfInput);
        }
        
        document.body.appendChild(form);
        form.submit();
      }
    };

    // 取消发布通报
    window.unpublishReport = (activityId) => {
      if (confirm('确定要取消发布这个通报吗？')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/unpublish_report/${activityId}`;
        
        // 添加CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrfToken) {
          const csrfInput = document.createElement('input');
          csrfInput.type = 'hidden';
          csrfInput.name = 'csrf_token';
          csrfInput.value = csrfToken;
          form.appendChild(csrfInput);
        }
        
        document.body.appendChild(form);
        form.submit();
      }
    };

    // 删除通报草稿
    window.deleteReportDraft = (activityId) => {
      if (confirm('确定要删除这个通报草稿吗？此操作不可恢复。')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/delete_report_draft/${activityId}`;
        
        // 添加CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrfToken) {
          const csrfInput = document.createElement('input');
          csrfInput.type = 'hidden';
          csrfInput.name = 'csrf_token';
          csrfInput.value = csrfToken;
          form.appendChild(csrfInput);
        }
        
        document.body.appendChild(form);
        form.submit();
      }
    };
  }

  /**
   * 初始化回收站功能
   */
  initRecycleBin() {
    // 加载回收站计数
    this.loadRecycleCount();
  }

  /**
   * 加载回收站计数
   */
  async loadRecycleCount() {
    try {
      const response = await fetch('/admin/api/recycle_bin/count');
      const data = await response.json();
      
      const recycleCountElement = document.getElementById('recycleCount');
      if (recycleCountElement && data.count > 0) {
        recycleCountElement.textContent = data.count;
        recycleCountElement.style.display = 'inline';
      } else if (recycleCountElement) {
        recycleCountElement.style.display = 'none';
      }
    } catch (error) {
      console.error('加载回收站计数失败:', error);
      const recycleCountElement = document.getElementById('recycleCount');
      if (recycleCountElement) {
        recycleCountElement.style.display = 'none';
      }
    }
  }

  /**
   * 初始化数据加载
   */
  initDataLoading() {
    this.dataLoaded = true;
    console.log('检查活动数据已标记为加载完成');
  }

  /**
   * 初始化操作按钮功能
   */
  initActionButtons() {
    // 定义toggleActionButtons函数
    window.toggleActionButtons = (container) => {
      console.log('🔄 toggleActionButtons被调用');

      // 阻止事件冒泡
      if (event) {
        event.stopPropagation();
      }

      // 先关闭其他已展开的按钮
      document.querySelectorAll('.hover-action-container.expanded').forEach(function(item) {
        if (item !== container) {
          item.classList.remove('expanded');
        }
      });

      // 切换当前容器的展开状态
      container.classList.toggle('expanded');

      console.log('✅ 操作按钮状态已切换');
    };

    console.log('✅ toggleActionButtons函数已定义');
  }

  /**
   * 删除确认逻辑
   */
  confirmDeleteActivity(activityId, activityName, hasPublishedReport) {
    if (hasPublishedReport) {
      alert('该活动已发布通报，无法直接删除。\n\n请先取消发布通报，然后再删除活动。');
      return false;
    }

    const confirmMessage = `确定要删除检查活动"${activityName}"吗？\n\n删除后该活动将移入回收站，可以在回收站中恢复。`;
    
    if (confirm(confirmMessage)) {
      // 创建表单并提交删除请求
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = `/admin/inspection_activities/${activityId}/delete`;
      
      // 添加CSRF token
      const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
      if (csrfToken) {
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);
      }
      
      document.body.appendChild(form);
      form.submit();
      return true;
    }
    
    return false;
  }

  /**
   * 定义全局函数（兼容性）
   */
  defineGlobalFunctions() {
    console.log('🔧 定义检查活动页面全局函数');

    // 确保全局函数可用
    window.confirmDeleteActivity = this.confirmDeleteActivity.bind(this);
    window.publishReport = window.publishReport || (() => console.warn('publishReport not defined'));
    window.unpublishReport = window.unpublishReport || (() => console.warn('unpublishReport not defined'));
    window.deleteReportDraft = window.deleteReportDraft || (() => console.warn('deleteReportDraft not defined'));

    // 数据加载函数
    window.loadInspectionActivitiesData = () => {
      console.log('全局函数loadInspectionActivitiesData被调用');
      this.initDataLoading();
    };

    // 确保toggleActionButtons函数可用（防御性编程）
    if (!window.toggleActionButtons) {
      console.warn('⚠️ toggleActionButtons未定义，重新初始化');
      this.initActionButtons();
    }

    console.log('✅ 检查活动页面全局函数定义完成');
  }



  /**
   * 销毁检查活动管理页面模块
   */
  destroy() {
    this.isInitialized = false;
    this.dataLoaded = false;
    console.log('检查活动管理页面模块已销毁');
  }
}

// 创建全局实例
window.InspectionActivitiesPage = new InspectionActivitiesPage();

export default InspectionActivitiesPage;
