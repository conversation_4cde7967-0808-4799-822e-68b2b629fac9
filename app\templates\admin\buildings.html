{% extends "base.html" %}

{% block title %}楼宇管理 - 实验室安全检查系统{% endblock %}

{% block content %}
<div class="container">
    <h1>楼宇管理</h1>
    <div class="mb-3">
        {% if current_user.is_admin() %}
        <a href="{{ url_for('admin.add_building') }}" class="btn btn-success">新建楼宇</a>
        {% endif %}
    </div>

    <!-- 搜索表单 -->
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">楼宇搜索</h3>
        </div>
        <div class="panel-body">
            <form id="searchForm" class="form-horizontal">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">楼宇名称</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="searchName" placeholder="输入楼宇名称">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">所属校区</label>
                            <div class="col-sm-8">
                                <select class="form-control" id="searchCampus">
                                    <option value="">-- 全部校区 --</option>
                                    {% for campus in campuses %}
                                    <option value="{{ campus.name }}">{{ campus.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <div class="col-sm-offset-2 col-sm-10">
                                <button type="button" id="searchBtn" class="btn btn-primary">搜索</button>
                                <button type="button" id="resetBtn" class="btn btn-default">重置</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover" id="buildingsTable">
            <thead>
                <tr>
                    <th>ID <span class="sort-icon" data-sort="id">⇅</span></th>
                    <th>楼宇名称 <span class="sort-icon" data-sort="name">⇅</span></th>
                    <th>校区 <span class="sort-icon" data-sort="campus">⇅</span></th>
                    <th>描述</th>
                    {% if current_user.is_admin() %}
                    <th>操作</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for building in buildings %}
                <tr>
                    <td>{{ building.id }}</td>
                    <td>{{ building.name }}</td>
                    <td>{{ building.campus.name }}</td>
                    <td>{{ building.description }}</td>
                    {% if current_user.is_admin() %}
                    <td>
                        <a href="{{ url_for('admin.edit_building', id=building.id) }}"
                            class="btn btn-sm btn-primary">编辑</a>
                        <a href="javascript:void(0)" class="btn btn-sm btn-danger delete-with-captcha"
                            data-url="{{ url_for('admin.delete_building', id=building.id) }}" data-title="确认删除楼宇"
                            data-message="确定要删除楼宇 &quot;{{ building.name }}&quot; 吗？此操作不可恢复！">删除</a>
                    </td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<!-- 引入buildings.html页面专用模块化JavaScript -->
{% for js_url in webpack_js('buildings') %}
<script src="{{ js_url }}"></script>
{% endfor %}
<script>
    // 页面加载完成后初始化buildings.html功能
    document.addEventListener('DOMContentLoaded', function () {
        if (window.BuildingsPage) {
            window.BuildingsPage.init();
        }
    });

    // AJAX加载时重新初始化
    if (window.UnifiedAjaxMenu) {
        window.UnifiedAjaxMenu.registerPageInitializer('/admin/buildings', function () {
            if (window.BuildingsPage) {
                window.BuildingsPage.init();
            }
        });
    }
</script>
{% endblock %}