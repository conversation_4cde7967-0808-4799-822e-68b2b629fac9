/*! For license information please see inspection-activities.81b5b9c48b719a725336.js.LICENSE.txt */
!function(){"use strict";function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(){var t,o,i="function"==typeof Symbol?Symbol:{},r=i.iterator||"@@iterator",c=i.toStringTag||"@@toStringTag";function a(e,i,r,c){var a=i&&i.prototype instanceof l?i:l,s=Object.create(a.prototype);return n(s,"_invoke",function(e,n,i){var r,c,a,l=0,s=i||[],d=!1,f={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(e,n){return r=e,c=0,a=t,f.n=n,u}};function p(e,n){for(c=e,a=n,o=0;!d&&l&&!i&&o<s.length;o++){var i,r=s[o],p=f.p,m=r[2];e>3?(i=m===n)&&(c=r[4]||3,a=r[5]===t?r[3]:r[5],r[4]=3,r[5]=t):r[0]<=p&&((i=e<2&&p<r[1])?(c=0,f.v=n,f.n=r[1]):p<m&&(i=e<3||r[0]>n||n>m)&&(r[4]=e,r[5]=n,f.n=m,c=0))}if(i||e>1)return u;throw d=!0,n}return function(i,s,m){if(l>1)throw TypeError("Generator is already running");for(d&&1===s&&p(s,m),c=s,a=m;(o=c<2?t:a)||!d;){r||(c?c<3?(c>1&&(f.n=-1),p(c,a)):f.n=a:f.v=a);try{if(l=2,r){if(c||(i="next"),o=r[i]){if(!(o=o.call(r,a)))throw TypeError("iterator result is not an object");if(!o.done)return o;a=o.value,c<2&&(c=0)}else 1===c&&(o=r.return)&&o.call(r),c<2&&(a=TypeError("The iterator does not provide a '"+i+"' method"),c=1);r=t}else if((o=(d=f.n<0)?a:e.call(n,f))!==u)break}catch(e){r=t,c=1,a=e}finally{l=1}}return{value:o,done:d}}}(e,r,c),!0),s}var u={};function l(){}function s(){}function d(){}o=Object.getPrototypeOf;var f=[][r]?o(o([][r]())):(n(o={},r,(function(){return this})),o),p=d.prototype=l.prototype=Object.create(f);function m(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,n(t,c,"GeneratorFunction")),t.prototype=Object.create(p),t}return s.prototype=d,n(p,"constructor",d),n(d,"constructor",s),s.displayName="GeneratorFunction",n(d,c,"GeneratorFunction"),n(p),n(p,c,"Generator"),n(p,r,(function(){return this})),n(p,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:a,m:m}})()}function n(t,e,o,i){var r=Object.defineProperty;try{r({},"",{})}catch(t){r=0}n=function(t,e,o,i){if(e)r?r(t,e,{value:o,enumerable:!i,configurable:!i,writable:!i}):t[e]=o;else{function c(e,o){n(t,e,(function(t){return this._invoke(e,o,t)}))}c("next",0),c("throw",1),c("return",2)}},n(t,e,o,i)}function o(t,e,n,o,i,r,c){try{var a=t[r](c),u=a.value}catch(t){return void n(t)}a.done?e(u):Promise.resolve(u).then(o,i)}function i(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,r(o.key),o)}}function r(e){var n=function(e){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,"string");if("object"!=t(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==t(n)?n:n+""}var c=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.isInitialized=!1,this.dataLoaded=!1},n=[{key:"init",value:function(){var t=this;this.isInitialized?console.log("检查活动管理页面已初始化，跳过重复初始化"):(console.log("🔍 初始化检查活动管理页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return t.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化检查活动管理页面功能"),this.initActivityActions(),this.initReportActions(),this.initRecycleBin(),this.initDataLoading(),this.initActionButtons(),this.defineGlobalFunctions(),console.log("✅ 检查活动管理页面功能初始化完成")}},{key:"initActivityActions",value:function(){window.stopInspectionActivity=function(t){confirm("确定要停止这个检查活动吗？")&&fetch("/admin/inspection_activities/".concat(t,"/stop"),{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":document.querySelector("meta[name=csrf-token]").getAttribute("content")}}).then((function(t){return t.json()})).then((function(t){t.success?(alert("检查活动已停止"),location.reload()):alert("停止失败："+t.message)})).catch((function(t){console.error("Error:",t),alert("停止失败")}))},window.reactivateInspectionActivity=function(t){confirm("确定要重新激活这个检查活动吗？")&&fetch("/admin/inspection_activities/".concat(t,"/reactivate"),{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":document.querySelector("meta[name=csrf-token]").getAttribute("content")}}).then((function(t){return t.json()})).then((function(t){t.success?(alert("检查活动已重新激活"),location.reload()):alert("重新激活失败："+t.message)})).catch((function(t){console.error("Error:",t),alert("重新激活失败")}))},console.log("✅ 停止检查活动函数已定义"),console.log("✅ 重新激活检查活动函数已定义")}},{key:"initReportActions",value:function(){window.publishReport=function(t){if(confirm("确定要发布这个通报吗？发布后将无法修改。")){var e,n=document.createElement("form");n.method="POST",n.action="/admin/publish_report/".concat(t);var o=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");if(o){var i=document.createElement("input");i.type="hidden",i.name="csrf_token",i.value=o,n.appendChild(i)}document.body.appendChild(n),n.submit()}},window.unpublishReport=function(t){if(confirm("确定要取消发布这个通报吗？")){var e,n=document.createElement("form");n.method="POST",n.action="/admin/unpublish_report/".concat(t);var o=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");if(o){var i=document.createElement("input");i.type="hidden",i.name="csrf_token",i.value=o,n.appendChild(i)}document.body.appendChild(n),n.submit()}},window.deleteReportDraft=function(t){if(confirm("确定要删除这个通报草稿吗？此操作不可恢复。")){var e,n=document.createElement("form");n.method="POST",n.action="/admin/delete_report_draft/".concat(t);var o=null===(e=document.querySelector('meta[name="csrf-token"]'))||void 0===e?void 0:e.getAttribute("content");if(o){var i=document.createElement("input");i.type="hidden",i.name="csrf_token",i.value=o,n.appendChild(i)}document.body.appendChild(n),n.submit()}}}},{key:"initRecycleBin",value:function(){this.loadRecycleCount()}},{key:"loadRecycleCount",value:(r=e().m((function t(){var n,o,i,r,c;return e().w((function(t){for(;;)switch(t.n){case 0:return t.p=0,t.n=1,fetch("/admin/api/recycle_bin/count");case 1:return n=t.v,t.n=2,n.json();case 2:o=t.v,(i=document.getElementById("recycleCount"))&&o.count>0?(i.textContent=o.count,i.style.display="inline"):i&&(i.style.display="none"),t.n=4;break;case 3:t.p=3,c=t.v,console.error("加载回收站计数失败:",c),(r=document.getElementById("recycleCount"))&&(r.style.display="none");case 4:return t.a(2)}}),t,null,[[0,3]])})),c=function(){var t=this,e=arguments;return new Promise((function(n,i){var c=r.apply(t,e);function a(t){o(c,n,i,a,u,"next",t)}function u(t){o(c,n,i,a,u,"throw",t)}a(void 0)}))},function(){return c.apply(this,arguments)})},{key:"initDataLoading",value:function(){this.dataLoaded=!0,console.log("检查活动数据已标记为加载完成")}},{key:"initActionButtons",value:function(){window.toggleActionButtons=function(t){console.log("🔄 toggleActionButtons被调用"),event&&event.stopPropagation(),document.querySelectorAll(".hover-action-container.expanded").forEach((function(e){e!==t&&e.classList.remove("expanded")})),t.classList.toggle("expanded"),console.log("✅ 操作按钮状态已切换")},console.log("✅ toggleActionButtons函数已定义")}},{key:"confirmDeleteActivity",value:function(t,e,n){if(n)return alert("该活动已发布通报，无法直接删除。\n\n请先取消发布通报，然后再删除活动。"),!1;var o='确定要删除检查活动"'.concat(e,'"吗？\n\n删除后该活动将移入回收站，可以在回收站中恢复。');if(confirm(o)){var i,r=document.createElement("form");r.method="POST",r.action="/admin/inspection_activities/".concat(t,"/delete");var c=null===(i=document.querySelector('meta[name="csrf-token"]'))||void 0===i?void 0:i.getAttribute("content");if(c){var a=document.createElement("input");a.type="hidden",a.name="csrf_token",a.value=c,r.appendChild(a)}return document.body.appendChild(r),r.submit(),!0}return!1}},{key:"defineGlobalFunctions",value:function(){var t=this;console.log("🔧 定义检查活动页面全局函数"),window.confirmDeleteActivity=this.confirmDeleteActivity.bind(this),window.publishReport=window.publishReport||function(){return console.warn("publishReport not defined")},window.unpublishReport=window.unpublishReport||function(){return console.warn("unpublishReport not defined")},window.deleteReportDraft=window.deleteReportDraft||function(){return console.warn("deleteReportDraft not defined")},window.loadInspectionActivitiesData=function(){console.log("全局函数loadInspectionActivitiesData被调用"),t.initDataLoading()},window.toggleActionButtons||(console.warn("⚠️ toggleActionButtons未定义，重新初始化"),this.initActionButtons()),console.log("✅ 检查活动页面全局函数定义完成")}},{key:"destroy",value:function(){this.isInitialized=!1,this.dataLoaded=!1,console.log("检查活动管理页面模块已销毁")}}],n&&i(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r,c}();window.InspectionActivitiesPage=new c}();
//# sourceMappingURL=inspection-activities.81b5b9c48b719a725336.js.map