/**
 * 实验室管理页面模块
 */
class LaboratoriesPage {
  constructor() {
    this.isInitialized = false;
    this.dataLoaded = false;
    this.currentSort = { column: '', direction: 'asc' };
  }

  /**
   * 初始化实验室管理页面
   */
  init() {
    if (this.isInitialized) {
      console.log('实验室管理页面已初始化，跳过重复初始化');
      return;
    }

    console.log('🏢 初始化实验室管理页面');

    // 等待DOM准备就绪
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.initializeFeatures());
    } else {
      this.initializeFeatures();
    }

    this.isInitialized = true;
  }

  /**
   * 初始化页面功能
   */
  initializeFeatures() {
    console.log('🔧 初始化实验室管理页面功能');

    // 检查操作权限并显示相应按钮
    this.checkOperationPermissions();

    // 标记数据已加载
    this.dataLoaded = true;

    // 初始化各种功能
    this.initSortingFeature();
    this.initSearchFeature();
    this.initPropertyDropdown();
    this.initRiskLevelColors();
    this.initLabSelection();
    this.initVisibilityChange();
    this.initActionButtons();

    // 定义全局函数（兼容性）
    this.defineGlobalFunctions();

    console.log('✅ 实验室管理页面功能初始化完成');
  }

  /**
   * 初始化表格排序功能
   */
  initSortingFeature() {
    const sortIcons = document.querySelectorAll('.sort-icon');
    
    sortIcons.forEach(icon => {
      icon.style.cursor = 'pointer';
      icon.style.userSelect = 'none';
      
      icon.addEventListener('click', (e) => {
        const column = e.target.dataset.sort;
        
        // 切换排序方向
        if (this.currentSort.column === column) {
          this.currentSort.direction = this.currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
          this.currentSort.column = column;
          this.currentSort.direction = 'asc';
        }

        // 重置所有排序图标
        sortIcons.forEach(icon => icon.textContent = '⇅');

        // 设置当前排序图标
        e.target.textContent = this.currentSort.direction === 'asc' ? '↑' : '↓';

        // 执行排序
        this.sortTable(column, this.currentSort.direction);
      });
    });
  }

  /**
   * 表格排序函数
   */
  sortTable(column, direction) {
    const table = document.getElementById('laboratoriesTable');
    if (!table) return;

    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    rows.sort((a, b) => {
      let A, B;

      // 根据列名获取对应的单元格索引
      const cellIndexMap = {
        'id': 1, 'campus': 2, 'building': 3, 'department': 4,
        'room': 5, 'name': 6, 'type': 7, 'risk': 8,
        'manager': 9, 'capacity': 11, 'property': 12
      };
      
      const cellIndex = cellIndexMap[column] || 1;

      if (column === 'id' || column === 'capacity') {
        // 数字类型排序
        A = parseInt(a.children[cellIndex]?.textContent.trim()) || 0;
        B = parseInt(b.children[cellIndex]?.textContent.trim()) || 0;
      } else {
        // 文本类型排序
        A = a.children[cellIndex]?.textContent.trim().toLowerCase() || '';
        B = b.children[cellIndex]?.textContent.trim().toLowerCase() || '';
      }

      if (A < B) return direction === 'asc' ? -1 : 1;
      if (A > B) return direction === 'asc' ? 1 : -1;
      return 0;
    });

    // 重新排列行
    rows.forEach(row => tbody.appendChild(row));
  }

  /**
   * 初始化搜索功能
   */
  initSearchFeature() {
    // 校区选择变化时动态加载楼宇选项
    const campusSelect = document.getElementById('search_campus_id');
    if (campusSelect) {
      campusSelect.addEventListener('change', (e) => {
        const campusId = e.target.value;
        if (campusId) {
          this.loadBuildingsForCampus(campusId);
        }
      });
    }

    // 重置表单按钮
    const resetBtn = document.getElementById('reset');
    if (resetBtn) {
      resetBtn.addEventListener('click', (e) => {
        e.preventDefault();
        // 重置所有表单字段
        const form = document.querySelector('form');
        if (form) {
          form.reset();
          // 取消选中所有属性复选框
          const propertyCheckboxes = document.querySelectorAll('input[name="property_ids"]');
          propertyCheckboxes.forEach(cb => cb.checked = false);
          // 更新属性下拉框文本
          this.updatePropertyDropdownText();
          // 提交表单
          form.submit();
        }
      });
    }
  }

  /**
   * 加载校区对应的楼宇选项
   */
  async loadBuildingsForCampus(campusId) {
    try {
      // 这里需要从模板中获取URL，或者通过data属性传递
      const url = `/admin/get_buildings_for_search/${campusId}`;
      const response = await fetch(url);
      const data = await response.json();
      
      const buildingSelect = document.getElementById('search_building_id');
      if (buildingSelect) {
        buildingSelect.innerHTML = '';
        data.forEach(item => {
          const option = document.createElement('option');
          option.value = item[0];
          option.textContent = item[1];
          buildingSelect.appendChild(option);
        });
      }
    } catch (error) {
      console.error('加载楼宇选项失败:', error);
    }
  }

  /**
   * 初始化属性下拉框功能
   */
  initPropertyDropdown() {
    // 防止点击下拉菜单内部元素时关闭菜单
    const dropdownMenus = document.querySelectorAll('.property-dropdown-menu, .property-items-wrapper');
    dropdownMenus.forEach(menu => {
      menu.addEventListener('click', (e) => e.stopPropagation());
    });

    // 属性多选框全选/取消全选
    const selectAllProperties = document.getElementById('selectAllProperties');
    if (selectAllProperties) {
      selectAllProperties.addEventListener('change', (e) => {
        const isChecked = e.target.checked;
        const propertyCheckboxes = document.querySelectorAll('input[name="property_ids"]');
        propertyCheckboxes.forEach(cb => cb.checked = isChecked);
        this.updatePropertyDropdownText();
      });
    }

    // 检查是否所有属性都被选中
    const propertyCheckboxes = document.querySelectorAll('input[name="property_ids"]');
    propertyCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        const total = propertyCheckboxes.length;
        const checked = document.querySelectorAll('input[name="property_ids"]:checked').length;
        
        if (selectAllProperties) {
          selectAllProperties.checked = checked > 0 && checked === total;
        }
        
        this.updatePropertyDropdownText();
      });
    });

    // 初始化下拉框文本
    this.updatePropertyDropdownText();

    // 点击下拉框内的复选框时阻止事件冒泡
    const checkboxes = document.querySelectorAll('.property-dropdown-menu .checkbox');
    checkboxes.forEach(checkbox => {
      checkbox.addEventListener('click', (e) => e.stopPropagation());
    });
  }

  /**
   * 更新属性下拉框显示文本
   */
  updatePropertyDropdownText() {
    const checked = document.querySelectorAll('input[name="property_ids"]:checked');
    const button = document.getElementById('propertyDropdown');
    const selectedText = button?.querySelector('.selected-text');

    if (!selectedText) return;

    if (checked.length === 0) {
      selectedText.textContent = '-- 请选择属性 --';
      button.classList.remove('btn-has-selection');
    } else if (checked.length === 1) {
      selectedText.textContent = checked[0].parentElement.textContent.trim();
      button.classList.add('btn-has-selection');
    } else {
      selectedText.textContent = `已选择 ${checked.length} 项`;
      button.classList.add('btn-has-selection');
    }
  }

  /**
   * 初始化风险等级颜色
   */
  initRiskLevelColors() {
    // 下拉框选项颜色
    const riskOptions = document.querySelectorAll('#risk_level option');
    riskOptions.forEach(option => {
      const code = option.value;
      const colorMap = {
        'red': '#ffcccc',
        'orange': '#ffddcc', 
        'yellow': '#ffffcc',
        'blue': '#ccccff'
      };
      if (colorMap[code]) {
        option.style.backgroundColor = colorMap[code];
      }
    });

    // 表格中的风险等级单元格颜色
    const tableRows = document.querySelectorAll('table tbody tr');
    tableRows.forEach(row => {
      const riskCell = row.children[8]; // 风险等级列
      if (riskCell) {
        const riskLevel = riskCell.textContent.trim();
        const colorMap = {
          '红': '#ffcccc',
          '橙': '#ffddcc',
          '黄': '#ffffcc', 
          '蓝': '#ccccff'
        };
        
        Object.keys(colorMap).forEach(key => {
          if (riskLevel.includes(key)) {
            riskCell.style.backgroundColor = colorMap[key];
          }
        });
      }
    });
  }

  /**
   * 初始化实验室选择功能
   */
  initLabSelection() {
    // 全选/取消全选实验室
    const selectAllLabs = document.getElementById('selectAllLabs');
    if (selectAllLabs) {
      selectAllLabs.addEventListener('change', (e) => {
        const isChecked = e.target.checked;
        const labCheckboxes = document.querySelectorAll('.lab-checkbox');
        labCheckboxes.forEach(cb => cb.checked = isChecked);
        this.updateGenerateButtonState();
      });
    }

    // 单个实验室复选框状态变化
    const labCheckboxes = document.querySelectorAll('.lab-checkbox');
    labCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        this.updateGenerateButtonState();

        // 检查是否所有实验室都被选中
        const allChecked = document.querySelectorAll('.lab-checkbox:checked').length === labCheckboxes.length;
        if (selectAllLabs) {
          selectAllLabs.checked = allChecked;
        }
      });
    });

    // 生成安全信息牌按钮点击事件
    const generateBtn = document.getElementById('generateSafetyCardBtn');
    if (generateBtn) {
      generateBtn.addEventListener('click', () => {
        const selectedLabs = [];
        const checkedBoxes = document.querySelectorAll('.lab-checkbox:checked');
        checkedBoxes.forEach(cb => {
          selectedLabs.push(cb.dataset.id);
        });

        if (selectedLabs.length > 0) {
          if (selectedLabs.length === 1) {
            // 单个实验室
            window.location.href = `/admin/laboratory_info_card/${selectedLabs[0]}`;
          } else {
            // 批量生成
            window.location.href = `/admin/batch_generate_info_card?lab_ids=${selectedLabs.join(',')}`;
          }
        } else {
          alert('请至少选择一个实验室');
        }
      });
    }

    // 初始化按钮状态
    this.updateGenerateButtonState();
  }

  /**
   * 更新生成按钮状态
   */
  updateGenerateButtonState() {
    const selectedCount = document.querySelectorAll('.lab-checkbox:checked').length;
    const generateBtn = document.getElementById('generateSafetyCardBtn');
    
    if (generateBtn) {
      generateBtn.disabled = selectedCount === 0;
      generateBtn.textContent = selectedCount > 0 
        ? `生成安全信息牌 (${selectedCount})` 
        : '生成安全信息牌';
    }
  }

  /**
   * 初始化页面可见性变化检测
   */
  initVisibilityChange() {
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        console.log('页面变为可见，检查数据加载状态');
        this.checkAndReloadData();
      }
    });
  }

  /**
   * 初始化操作按钮功能
   */
  initActionButtons() {
    // 点击页面其他地方时关闭所有展开的按钮
    document.addEventListener('click', function(event) {
      // 检查点击的元素是否在操作按钮容器内
      if (!event.target.closest('.hover-action-container')) {
        // 关闭所有展开的按钮
        document.querySelectorAll('.hover-action-container.expanded').forEach(function(container) {
          container.classList.remove('expanded');
        });
      }
    });

    console.log('✅ 操作按钮事件监听器已设置');
  }

  /**
   * 检查操作权限
   */
  checkOperationPermissions() {
    console.log('权限已在服务器端预加载，无需AJAX检查');
    // 按钮的显示/隐藏已通过模板中的条件判断处理
  }

  /**
   * 检查并重新加载数据
   */
  checkAndReloadData() {
    console.log('检查是否需要重新加载数据');
    
    if (!this.dataLoaded) {
      console.log('标记数据为已加载');
      this.dataLoaded = true;
    } else {
      console.log('数据已加载，无需重新加载');
    }
  }

  /**
   * 定义全局函数（兼容性）
   */
  defineGlobalFunctions() {
    window.loadLaboratoriesData = () => {
      console.log('全局函数loadLaboratoriesData被调用');
      this.checkOperationPermissions();
      this.dataLoaded = true;
    };

    // 定义toggleActionButtons函数
    window.toggleActionButtons = (container) => {
      console.log('🔄 toggleActionButtons被调用 (实验室页面)');

      // 阻止事件冒泡
      if (event) {
        event.stopPropagation();
      }

      // 先关闭其他已展开的按钮
      document.querySelectorAll('.hover-action-container.expanded').forEach(function(item) {
        if (item !== container) {
          item.classList.remove('expanded');
        }
      });

      // 切换当前容器的展开状态
      container.classList.toggle('expanded');
      console.log('✅ 操作按钮状态已切换');
    };

    console.log('✅ toggleActionButtons函数已定义 (实验室页面)');
  }

  /**
   * 销毁实验室管理页面模块
   */
  destroy() {
    this.isInitialized = false;
    this.dataLoaded = false;
    console.log('实验室管理页面模块已销毁');
  }
}

// 创建全局实例
window.LaboratoriesPage = new LaboratoriesPage();

export default LaboratoriesPage;
