{% extends "base.html" %}

{% block title %}安全风险等级管理 - 实验室安全检查系统{% endblock %}

{% block content %}
<div class="container">
    <h1>安全风险等级管理</h1>
    <div class="mb-3">
        <a href="{{ url_for('admin.add_risk_level') }}" class="btn btn-success">
            <i class="fas fa-plus"></i> 新建风险等级
        </a>
    </div>

    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">安全风险等级列表</h3>
        </div>
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover" id="riskLevelsTable">
                <thead>
                    <tr>
                        <th>ID <span class="sort-icon" data-sort="id">⇅</span></th>
                        <th>等级代码 <span class="sort-icon" data-sort="code">⇅</span></th>
                        <th>等级名称 <span class="sort-icon" data-sort="name">⇅</span></th>
                        <th>等级颜色</th>
                        <th>等级描述</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for risk_level in risk_levels %}
                    <tr>
                        <td>{{ risk_level.id }}</td>
                        <td>{{ risk_level.code }}</td>
                        <td>{{ risk_level.name }}</td>
                        <td>
                            {% if risk_level.color == 'blue' %}
                            <span class="label label-primary">蓝色 (低风险)</span>
                            {% elif risk_level.color == 'yellow' %}
                            <span class="label label-warning">黄色 (一般风险)</span>
                            {% elif risk_level.color == 'orange' %}
                            <span class="label" style="background-color: #FF8C00;">橙色 (中风险)</span>
                            {% elif risk_level.color == 'red' %}
                            <span class="label label-danger">红色 (高风险)</span>
                            {% else %}
                            <span class="label label-default">未设置</span>
                            {% endif %}
                        </td>
                        <td>{{ risk_level.description }}</td>
                        <td>
                            <a href="{{ url_for('admin.edit_risk_level', id=risk_level.id) }}"
                                class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i> 编辑
                            </a>
                            <a href="javascript:void(0)" class="btn btn-sm btn-danger delete-with-captcha"
                                data-url="{{ url_for('admin.delete_risk_level', id=risk_level.id) }}"
                                data-title="确认删除安全风险等级"
                                data-message="确定要删除安全风险等级 &quot;{{ risk_level.name }}&quot; 吗？此操作不可恢复！">
                                <i class="fas fa-trash"></i> 删除
                            </a>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="6" class="text-center">暂无安全风险等级数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    $(document).ready(function () {
        // 排序功能
        var currentSort = { column: '', direction: 'asc' };

        $('.sort-icon').click(function () {
            var column = $(this).data('sort');

            // 切换排序方向
            if (currentSort.column === column) {
                currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.column = column;
                currentSort.direction = 'asc';
            }

            // 重置所有排序图标
            $('.sort-icon').text('⇅');

            // 设置当前排序图标
            $(this).text(currentSort.direction === 'asc' ? '↑' : '↓');

            // 执行排序
            sortTable(column, currentSort.direction);
        });

        // 表格排序函数
        function sortTable(column, direction) {
            var rows = $('#riskLevelsTable tbody tr').get();

            rows.sort(function (a, b) {
                var A, B;

                if (column === 'id') {
                    A = parseInt($(a).children('td').eq(0).text().trim());
                    B = parseInt($(b).children('td').eq(0).text().trim());
                } else if (column === 'code') {
                    A = $(a).children('td').eq(1).text().trim().toLowerCase();
                    B = $(b).children('td').eq(1).text().trim().toLowerCase();
                } else if (column === 'name') {
                    A = $(a).children('td').eq(2).text().trim().toLowerCase();
                    B = $(b).children('td').eq(2).text().trim().toLowerCase();
                }

                if (A < B) {
                    return direction === 'asc' ? -1 : 1;
                }
                if (A > B) {
                    return direction === 'asc' ? 1 : -1;
                }
                return 0;
            });

            $.each(rows, function (index, row) {
                $('#riskLevelsTable tbody').append(row);
            });
        }

        // 样式优化
        $('.sort-icon').css({
            'cursor': 'pointer',
            'user-select': 'none'
        });
    });
</script>
{% endblock %}