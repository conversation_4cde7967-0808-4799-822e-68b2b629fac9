{"version": 3, "file": "css/app.821aee80a78779892534.css", "mappings": "AAAA;;;;EAIE;;AAEF,0CAA0C;AAC1C;;;;;;;;;;;EAWE;;AAEF;IACI,SAAS;IACT,eAAe;IACf,gBAAgB;IAChB,cAAc;;IAEd,SAAS;IACT,gBAAgB;IAChB,eAAe;IACf,iBAAiB;;IAEjB,SAAS;IACT,gBAAgB;IAChB,uBAAuB;IACvB,gBAAgB;;IAEhB,SAAS;IACT,gBAAgB;IAChB,gBAAgB;IAChB,qBAAqB;;IAErB,UAAU;IACV,uBAAuB;IACvB,cAAc;IACd,sBAAsB;;IAEtB,SAAS;IACT,aAAa;IACb,oBAAoB;IACpB,iBAAiB;IACjB,yBAAyB;IACzB,eAAe;IACf,mBAAmB;AACvB;;AAEA,WAAW;AACX;IACI,sBAAsB;AAC1B;;AAEA,qCAAqC;AACrC;IACI,cAAc,EAAE,mBAAmB;IACnC,WAAW;AACf;;AAEA,cAAc;AACd;IACI,cAAc;AAClB;;AAEA;IACI,2BAA2B;IAC3B,0BAA0B;IAC1B,2BAA2B;IAC3B,2BAA2B;IAC3B,eAAe;AACnB;;AAEA,eAAe;AACf;IACI,aAAa;IACb,gBAAgB,EAAE,mBAAmB;IACrC,mBAAmB,EAAE,SAAS;IAC9B,oCAAoC;IACpC,YAAY;IACZ,kBAAkB;IAClB,sBAAsB;IACtB,uBAAuB,EAAE,kBAAkB;IAC3C,uBAAkB;IAAlB,kBAAkB,EAAE,YAAY;IAChC,eAAe,EAAE,WAAW;IAC5B,wCAAwC;IACxC,kBAAkB,EAAE,WAAW;AACnC;;AAEA;IACI,iBAAiB;IACjB,kBAAkB;IAClB,2BAA2B;IAC3B,0BAA0B;IAC1B,2BAA2B;IAC3B,2BAA2B;AAC/B;;AAEA,cAAc;AACd;IACI,aAAa;AACjB;;AAEA;IACI,yBAAyB,EAAE,kBAAkB;AACjD;;AAEA,cAAc;AACd;IACI,WAAW,EAAE,YAAY;IACzB,qBAAqB,EAAE,gBAAgB;IACvC,2BAA2B;IAC3B,0BAA0B;IAC1B,2BAA2B;IAC3B,2BAA2B;AAC/B;;AAEA,oCAAoC;AACpC;IACI,6BAA6B,EAAE,WAAW;IAC1C,WAAW;AACf;;AAEA;;IAEI,sBAAsB,EAAE,aAAa;IACrC,mBAAmB,EAAE,WAAW;AACpC;;AAEA,aAAa;AACb;IACI,sBAAsB,EAAE,YAAY;AACxC;;AAEA;IACI,sBAAsB,EAAE,YAAY;AACxC;;AAEA;IACI,uBAAuB,EAAE,YAAY;AACzC;;AAEA;IACI,uBAAuB,EAAE,YAAY;IACrC,kBAAkB;AACtB;;AAEA;IACI,gBAAgB,EAAE,YAAY;IAC9B,gBAAgB,EAAE,YAAY;IAC9B,mBAAmB,EAAE,WAAW;IAChC,sBAAsB,EAAE,UAAU;AACtC;;AAEA,SAAS;AACT;IACI,kBAAkB;AACtB;;AAEA;IACI,WAAW;IACX,kBAAkB;IAClB,QAAQ;IACR,SAAS;IACT,WAAW;IACX,YAAY;IACZ,uBAAuB;IACvB,yBAAyB;IACzB,6BAA6B;IAC7B,kBAAkB;IAClB,kCAAkC;AACtC;;AAEA;IACI;QACI,uBAAuB;IAC3B;;IAEA;QACI,yBAAyB;IAC7B;AACJ;;AAEA,WAAW;AACX;IACI,yBAAyB;IACzB,eAAe;AACnB;;AAEA;IACI,2BAA2B;IAC3B,yCAAyC;AAC7C;;AAEA,WAAW;AACX;IACI,WAAW;IACX,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;IACnB,aAAa;IACb,8BAA8B;IAC9B,mBAAmB;IACnB,eAAe;IACf,SAAS;AACb;;AAEA;IACI,gBAAgB;IAChB,OAAO;AACX;;AAEA;IACI,gBAAgB;IAChB,kBAAkB;AACtB;;AAEA;IACI,aAAa;AACjB;;AAEA;IACI,aAAa;IACb,mBAAmB;IACnB,aAAa;IACb,kBAAkB;IAClB,mBAAmB;AACvB;;AAEA;IACI,iBAAiB;IACjB,cAAc;AAClB;;AAEA,WAAW;AACX;IACI,gBAAgB;IAChB,aAAa;IACb,eAAe;IACf,SAAS;AACb;;AAEA;IACI,aAAa;IACb,sBAAsB;IACtB,mBAAmB;IACnB,aAAa;IACb,sBAAsB;IACtB,kBAAkB;IAClB,mBAAmB;AACvB;;AAEA;IACI,kBAAkB;AACtB;;AAEA;IACI,eAAe;IACf,kBAAkB;IAClB,qBAAqB;AACzB;;AAEA,WAAW;AACX;IACI,qBAAqB;AACzB;;AAEA;IACI,cAAc;AAClB;;AAEA;IACI,gCAAgC;AACpC;;AAEA;IACI,gCAAgC;AACpC;;AAEA,YAAY;AACZ;IACI,kBAAkB;IAClB,yCAAyC;AAC7C;;AAEA;IACI,gCAAgC;IAChC,kBAAkB;AACtB;;AAEA;IACI,aAAa;AACjB;;AAEA;IACI,6BAA6B;IAC7B,kBAAkB;IAClB,iBAAiB;AACrB;;AAEA;IACI,gBAAgB;AACpB;;AAEA,WAAW;AACX;IACI,eAAe;IACf,2BAA2B;AAC/B;;AAEA;IACI,cAAc;IACd,kCAA0B;IAA1B,0BAA0B;AAC9B;;AAEA,YAAY;AACZ;IACI,0BAA0B;IAC1B,iBAAiB;IACjB,kBAAkB;IAClB,uBAAuB;IACvB,wBAAwB;IACxB,oCAAoC;IACpC,uBAAuB;IACvB,uBAAoC;IAApC,oCAAoC;IACpC,2BAA2B;IAC3B,0BAA0B;IAC1B,iBAAiB;IACjB,iBAAiB;IACjB,wCAAwC;AAC5C;;AAEA,YAAY;AACZ;IACI,aAAa;IACb,kBAAkB;IAClB,iDAAiD;AACrD;;AAEA;IACI,gBAAgB;IAChB,iBAAiB;IACjB,cAAc;IACd,cAAc;AAClB;;AAEA;IACI,iBAAiB;IACjB,cAAc;IACd,YAAY;IACZ,eAAe;AACnB;;AAEA,WAAW;AACX;IACI,gBAAgB;IAChB,UAAU;IACV,gBAAgB;AACpB;;AAEA;IACI,SAAS;IACT,UAAU;AACd;;AAEA;IACI,cAAc;IACd,kBAAkB;IAClB,uBAAuB;IACvB,6BAAqB;IAArB,qBAAqB;IACrB,iCAAiC;IACjC,YAAY;AAChB;;AAEA;;IAEI,oCAAoC;IACpC,uBAAuB;IACvB,6BAAqB;IAArB,qBAAqB;IACrB,2BAA2B;AAC/B;;AAEA;IACI,kBAAkB;IAClB,WAAW;IACX,kBAAkB;AACtB;;AAEA,4BAA4B;AAC5B;IACI,wBAAwB;IACxB,6BAA6B;AACjC;;AAEA;IACI,2BAA2B;IAC3B,gBAAgB;IAChB,wBAAwB;AAC5B;;AAEA;IACI,4BAA4B;IAC5B,cAAc;AAClB;;AAEA;IACI,6BAA6B;IAC7B,kBAAkB;AACtB;;AAEA,mBAAmB;AACnB;IACI,6BAA6B;IAC7B,cAAc;AAClB;;AAEA;IACI,kBAAkB;IAClB,YAAY;AAChB;;AAEA;IACI,WAAW;AACf;;AAEA;IACI,aAAa;AACjB;;AAEA;IACI,kBAAkB;IAClB,WAAW;AACf;;AAEA,aAAa;;AAEb,SAAS;AACT;IACI,8BAA8B;AAClC;;AAEA;IACI;QACI,UAAU;IACd;;IAEA;QACI,UAAU;IACd;AACJ;;AAEA,2CAA2C;AAC3C,6BAA6B;AAC7B;IACI,2BAA2B;IAC3B,4BAA4B;IAC5B,+BAA+B,EAAE,cAAc;AACnD;;AAEA;IACI,2BAA2B;IAC3B,2BAA2B;IAC3B,wBAAwB;IACxB,iCAAiC;AACrC;;AAEA;IACI,yBAAyB;IACzB,6BAA6B;AACjC;;AAEA;IACI,2BAA2B;IAC3B,2BAA2B;IAC3B,kBAAkB;IAClB,wBAAwB;AAC5B;;AAEA;IACI,yBAAyB;IACzB,6BAA6B;AACjC;;AAEA,gBAAgB;AAChB;IACI,2BAA2B;IAC3B,2BAA2B;IAC3B,iCAAiC;IACjC,kBAAkB;AACtB;;AAEA,iBAAiB;AACjB;IACI,2BAA2B;AAC/B;;AAEA,gBAAgB;AAChB;IACI,yBAAyB;IACzB,iBAAiB;IACjB,6BAA6B;IAC7B,gBAAgB;IAChB,MAAM;IACN,iCAAiC;AACrC;;AAEA;IACI,yBAAyB;AAC7B;;AAEA,cAAc;AACd;IACI,WAAW;AACf;;AAEA,eAAe;AACf;IACI,qBAAqB;AACzB;;AAEA,kBAAkB;;AAElB,gBAAgB;AAChB;IACI,2BAA2B;AAC/B;;AAEA;IACI,6BAA6B;IAC7B,iBAAiB;IACjB,0BAA0B;AAC9B;;AAEA;IACI,gCAAgC;AACpC;;AAEA;IACI;QACI,4BAA4B;QAC5B,UAAU;IACd;;IAEA;QACI,wBAAwB;QACxB,UAAU;IACd;AACJ;;AAEA,WAAW;AACX;IACI,kBAAkB;IAClB,qBAAqB;AACzB;;AAEA;IACI,kBAAkB;IAClB,YAAY;IACZ,sBAAsB;IACtB,WAAW;IACX,kBAAkB;IAClB,kBAAkB;IAClB,cAAc;IACd,kBAAkB;IAClB,gCAAgC;IAChC,YAAY;IACZ,SAAS;IACT,kBAAkB;IAClB,UAAU;IACV,wBAAwB;AAC5B;;AAEA;IACI,mBAAmB;IACnB,UAAU;AACd;;AAEA,qCAAqC;AACrC,mBAAmB;AACnB;IACI,sBAAsB;IACtB,kBAAkB;IAClB,wEAAwE;IACxE,mBAAmB;IACnB,kBAAkB;IAClB,gBAAgB;IAChB,yBAAyB;AAC7B;;AAEA;IACI,wEAAwE;IACxE,2BAA2B;AAC/B;;AAEA;IACI,kBAAkB;IAClB,MAAM;IACN,OAAO;IACP,WAAW;IACX,YAAY;IACZ,aAAa;IACb,mBAAmB;IACnB,uBAAuB;IACvB,eAAe;AACnB;;AAEA;IACI,eAAe;IACf,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,mBAAmB;AACvB;;AAEA;IACI,iBAAiB;IACjB,oBAAoB;IACpB,WAAW;AACf;;AAEA,iBAAiB;AACjB;IACI,mBAAmB;IACnB,sBAAsB;IACtB,6BAA6B;IAC7B,kBAAkB;IAClB,wEAAwE;AAC5E;;AAEA;IACI,kBAAkB;IAClB,oCAAoC;IACpC,2BAA2B;IAC3B,4BAA4B;IAC5B,aAAa;IACb,8BAA8B;IAC9B,mBAAmB;AACvB;;AAEA;IACI,SAAS;IACT,eAAe;IACf,iBAAiB;AACrB;;AAEA;IACI,aAAa;AACjB;;AAEA,mBAAmB;AACnB;IACI,aAAa;IACb,mBAAmB;IACnB,8BAA8B;IAC9B,yBAAyB;IACzB,aAAa;IACb,8BAA8B;IAC9B,mBAAmB;AACvB;;AAEA;IACI,0BAA0B;AAC9B;;AAEA;IACI,YAAY;AAChB;;AAEA;IACI,iBAAiB;IACjB,kBAAkB;AACtB;;AAEA;IACI,eAAe;IACf,WAAW;AACf;;AAEA;IACI,iBAAiB;AACrB;;AAEA,mBAAmB;AACnB;IACI,aAAa;IACb,eAAe;IACf,SAAS;AACb;;AAEA;IACI,OAAO;IACP,gBAAgB;IAChB,kBAAkB;IAClB,aAAa;IACb,yBAAyB;IACzB,kBAAkB;IAClB,yCAAyC;IACzC,yBAAyB;AAC7B;;AAEA;IACI,yBAAyB;IACzB,yCAAyC;IACzC,2BAA2B;AAC/B;;AAEA;IACI,eAAe;IACf,mBAAmB;AACvB;;AAEA;IACI,iBAAiB;AACrB;;AAEA,oBAAoB;AACpB;IACI,mBAAmB;IACnB,kBAAkB;IAClB,yCAAyC;IACzC,yBAAyB;AAC7B;;AAEA;IACI,yCAAyC;IACzC,2BAA2B;AAC/B;;AAEA;IACI,2BAA2B;IAC3B,4BAA4B;AAChC;;AAEA;IACI,aAAa;AACjB;;AAEA;IACI,gBAAgB;AACpB;;AAEA,iBAAiB;AACjB;IACI,aAAa;IACb,mBAAmB;IACnB,8BAA8B;IAC9B,yBAAyB;IACzB,eAAe;IACf,yBAAyB;AAC7B;;AAEA;IACI,yBAAyB;IACzB,0BAA0B;AAC9B;;AAEA;IACI,iBAAiB;IACjB,kBAAkB;IAClB,cAAc;IACd,aAAa;IACb,8BAA8B;IAC9B,mBAAmB;AACvB;;AAEA;IACI,eAAe;IACf,WAAW;IACX,kBAAkB;AACtB;;AAEA;IACI,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,iBAAiB;IACjB,0BAA0B;AAC9B;;AAEA;IACI,cAAc;AAClB;;AAEA,kBAAkB;AAClB;IACI,8BAA8B;IAC9B,+BAA+B;IAC/B,yBAAyB;AAC7B;;AAEA;IACI,cAAc;AAClB;;AAEA;IACI,cAAc;AAClB;;AAEA;IACI,cAAc;AAClB;;AAEA;IACI,aAAa;IACb,6BAA6B;AACjC;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,iBAAiB;AACrB;;AAEA;IACI,WAAW;IACX,gBAAgB;AACpB;;AAEA;IACI,cAAc;IACd,iBAAiB;AACrB;;AAEA,mCAAmC;AACnC,aAAa;AACb;IACI,mBAAmB;IACnB,0BAA0B;AAC9B;;AAEA;IACI,2BAA2B;IAC3B,wCAAwC;AAC5C;;AAEA;IACI,eAAe;IACf,mBAAmB;IACnB,cAAc;AAClB;;AAEA;IACI,iBAAiB;IACjB,iBAAiB;AACrB;;AAEA;IACI,cAAc;IACd,gBAAgB;AACpB;;AAEA,eAAe;AACf;IACI,yBAAyB;IACzB,aAAa;IACb,kBAAkB;IAClB,mBAAmB;AACvB;;AAEA,gBAAgB;AAChB;IACI,gBAAgB;AACpB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,iBAAiB;AACrB;;AAEA,mCAAmC;AACnC,eAAe;AACf;IACI,mBAAmB;IACnB,aAAa;IACb,yBAAyB;IACzB,sBAAsB;IACtB,kBAAkB;AACtB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,gBAAgB;AACpB;;AAEA;IACI,aAAa;IACb,2BAA2B;IAC3B,4BAA4B;IAC5B,6BAA6B;IAC7B,0BAA0B;AAC9B;;AAEA;IACI,gBAAgB;AACpB;;AAEA;IACI,mBAAmB;AACvB;;AAEA,oCAAoC;AACpC,gBAAgB;AAChB;IACI,qBAAqB;IACrB,aAAa;IACb,mBAAmB;AACvB;;AAEA;IACI,kBAAkB;IAClB,iBAAiB;AACrB;;AAEA;IACI,yBAAyB;IACzB,YAAY;IACZ,qBAAqB;AACzB;;AAEA,WAAW;AACX;IACI,yBAAyB;AAC7B;;AAEA;IACI,2BAA2B;IAC3B,wCAAwC;AAC5C;;AAEA;IACI,wBAAwB;AAC5B;;AAEA,SAAS;AACT;IACI,yBAAyB;IACzB,sBAAsB;IACtB,sDAAsD;AAC1D;;AAEA;IACI,yBAAyB;IACzB,gCAAgC;AACpC;;AAEA,SAAS;AACT;IACI,iBAAiB;IACjB,gBAAgB;IAChB,uBAAuB;IACvB,sBAAsB;AAC1B;;AAEA,UAAU;AACV;IACI,YAAY;IACZ,sBAAsB;IACtB,yBAAyB;AAC7B;;AAEA;IACI,sBAAsB;IACtB,2BAA2B;AAC/B;;AAEA,UAAU;AACV;IACI,yBAAyB;IACzB,yBAAyB;AAC7B;;AAEA;IACI,yBAAyB;AAC7B;;AAEA,SAAS;AACT;IACI,6BAA6B;IAC7B,+BAA+B;IAC/B,gCAAgC;AACpC;;AAEA;IACI,sBAAsB;IACtB,kCAAkC;AACtC;;AAEA,iCAAiC;AACjC,oBAAoB;AACpB;IACI,mBAAmB;IACnB,YAAY;IACZ,kBAAkB;IAClB,kBAAkB;IAClB,mBAAmB;IACnB,6CAA6C;AACjD;;AAEA;IACI,eAAe;IACf,gBAAgB;IAChB,kBAAkB;IAClB,aAAa;IACb,8BAA8B;IAC9B,mBAAmB;AACvB;;AAEA;IACI,eAAe;IACf,YAAY;IACZ,gBAAgB;AACpB;;AAEA;IACI,eAAe;IACf,YAAY;IACZ,qCAAqC;IACrC,iBAAiB;IACjB,kBAAkB;AACtB;;AAEA,YAAY;AACZ;IACI,aAAa;IACb,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,aAAa;IACb,mBAAmB;IACnB,eAAe;IACf,YAAY;AAChB;;AAEA;IACI,UAAU;IACV,WAAW;IACX,kBAAkB;IAClB,iBAAiB;AACrB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;AACvB;;AAEA,sBAAsB;AACtB;IACI,iBAAiB;IACjB,kBAAkB;IAClB,aAAa;IACb,mBAAmB;IACnB,wEAAwE;IACxE,yBAAyB;IACzB,kBAAkB;IAClB,gBAAgB;AACpB;;AAEA;IACI,2BAA2B;IAC3B,wEAAwE;AAC5E;;AAEA;IACI,aAAa;IACb,mBAAmB;IACnB,8BAA8B;IAC9B,mBAAmB;AACvB;;AAEA;IACI,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,aAAa;IACb,mBAAmB;IACnB,uBAAuB;IACvB,eAAe;IACf,YAAY;AAChB;;AAEA;IACI,eAAe;IACf,gBAAgB;IAChB,mBAAmB;IACnB,gBAAgB;IAChB,aAAa;IACb,mBAAmB;IACnB,QAAQ;AACZ;;AAEA;IACI,mBAAmB;IACnB,cAAc;AAClB;;AAEA;IACI,mBAAmB;IACnB,cAAc;AAClB;;AAEA;IACI,mBAAmB;IACnB,cAAc;AAClB;;AAEA;IACI,eAAe;IACf,gBAAgB;IAChB,WAAW;IACX,kBAAkB;IAClB,cAAc;AAClB;;AAEA;IACI,eAAe;IACf,WAAW;IACX,gBAAgB;IAChB,gBAAgB;AACpB;;AAEA,WAAW;AACX;IACI,eAAe;IACf,gCAAgC;IAChC,aAAa;IACb,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,aAAa;IACb,mBAAmB;IACnB,uBAAuB;IACvB,kBAAkB;IAClB,eAAe;IACf,YAAY;AAChB;;AAEA;IACI,OAAO;AACX;;AAEA;IACI,eAAe;IACf,WAAW;IACX,kBAAkB;IAClB,gBAAgB;AACpB;;AAEA;IACI,eAAe;IACf,WAAW;AACf;;AAEA,oBAAoB;AACpB;IACI,mBAAmB;IACnB,sBAAsB;IACtB,6BAA6B;IAC7B,kBAAkB;IAClB,wEAAwE;IACxE,yBAAyB;AAC7B;;AAEA;IACI,wEAAwE;IACxE,2BAA2B;AAC/B;;AAEA;IACI,kBAAkB;IAClB,oCAAoC;IACpC,2BAA2B;IAC3B,4BAA4B;IAC5B,aAAa;IACb,8BAA8B;IAC9B,mBAAmB;AACvB;;AAEA;IACI,SAAS;IACT,eAAe;IACf,gBAAgB;IAChB,aAAa;IACb,mBAAmB;AACvB;;AAEA;IACI,iBAAiB;AACrB;;AAEA;IACI,aAAa;AACjB;;AAEA,WAAW;AACX;IACI,kBAAkB;IAClB,mBAAmB;IACnB,8BAA8B;IAC9B,yBAAyB;IACzB,0BAA0B;IAC1B,aAAa;IACb,8BAA8B;IAC9B,mBAAmB;IACnB,yBAAyB;AAC7B;;AAEA;IACI,yBAAyB;IACzB,0BAA0B;AAC9B;;AAEA;IACI,0BAA0B;AAC9B;;AAEA;IACI,0BAA0B;IAC1B,YAAY;AAChB;;AAEA;IACI,YAAY;AAChB;;AAEA;IACI,gBAAgB;IAChB,kBAAkB;IAClB,eAAe;IACf,WAAW;AACf;;AAEA;IACI,eAAe;IACf,WAAW;AACf;;AAEA;IACI,iBAAiB;AACrB;;AAEA,SAAS;AACT;IACI,aAAa;IACb,mBAAmB;IACnB,8BAA8B;IAC9B,yBAAyB;IACzB,eAAe;IACf,yBAAyB;AAC7B;;AAEA;IACI,yBAAyB;IACzB,0BAA0B;AAC9B;;AAEA;IACI,iBAAiB;IACjB,kBAAkB;IAClB,cAAc;AAClB;;AAEA;IACI,eAAe;IACf,WAAW;IACX,kBAAkB;AACtB;;AAEA;IACI,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,iBAAiB;IACjB,0BAA0B;AAC9B;;AAEA;IACI,cAAc;AAClB;;AAEA,sBAAsB;AACtB;IACI,aAAa;IACb,eAAe;IACf,SAAS;AACb;;AAEA;IACI,OAAO;IACP,gBAAgB;IAChB,kBAAkB;IAClB,kBAAkB;IAClB,yBAAyB;IACzB,kBAAkB;IAClB,yCAAyC;IACzC,yBAAyB;IACzB,6BAAqB;IAArB,qBAAqB;IACrB,WAAW;IACX,yBAAyB;AAC7B;;AAEA;IACI,yBAAyB;IACzB,yCAAyC;IACzC,2BAA2B;IAC3B,6BAAqB;IAArB,qBAAqB;IACrB,WAAW;IACX,qBAAqB;AACzB;;AAEA;IACI,eAAe;IACf,kBAAkB;IAClB,cAAc;AAClB;;AAEA;IACI,gBAAgB;IAChB,eAAe;IACf,gBAAgB;AACpB;;AAEA;IACI,qBAAqB;IACrB,eAAe;IACf,gBAAgB;IAChB,eAAe;IACf,gBAAgB;IAChB,cAAc;IACd,WAAW;IACX,kBAAkB;IAClB,mBAAmB;IACnB,sBAAsB;IACtB,yBAAyB;IACzB,mBAAmB;IACnB,gBAAgB;AACpB;;AAEA,oCAAoC;AACpC;IACI,gBAAgB;IAChB,eAAe;IACf,yBAAyB;IACzB,0BAA0B;AAC9B;;AAEA;IACI;QACI,wBAAwB;IAC5B;;IAEA;QACI,yBAAyB;QACzB,qBAAqB;IACzB;;IAEA;QACI,kCAAkC;IACtC;AACJ;;AAEA,iCAAiC;AACjC;IACI,yBAAyB;AAC7B;;AAEA;IACI,sCAAsC;AAC1C;;AAEA,oCAAoC;AACpC;IACI,+CAA+C;AACnD;;AAEA,0BAA0B;AAC1B;IACI,+CAA+C;AACnD;;AAEA,gDAAgD;AAChD;;;;;;;;;;;;;;;;SAgBS;;QAED;YACI;gBACI,UAAU;gBACV,aAAa;YACjB;;YAEA;gBACI,UAAU;gBACV,iBAAiB;YACrB;QACJ;;AAER;IACI,kBAAkB;IAClB,gCAAgC;IAChC,kEAAkE;IAClE,gBAAgB;IAChB,YAAY;AAChB;;AAEA,mCAAmC;AACnC;IACI,aAAa;AACjB;;AAEA,2CAA2C;AAC3C;IACI,gBAAgB;IAChB,eAAe;IACf,yBAAyB;IACzB,0BAA0B;AAC9B;;AAEA;IACI;QACI,wBAAwB;IAC5B;;IAEA;QACI,yBAAyB;QACzB,qBAAqB;IACzB;;IAEA;QACI,kCAAkC;IACtC;AACJ;;AAEA,UAAU;AACV;IACI,kBAAkB;AACtB;;AAEA;IACI,SAAS;IACT,WAAW;IACX,eAAe;AACnB;;AAEA;IACI;QACI,wBAAwB;IAC5B;;IAEA;QACI,yBAAyB;QACzB,qBAAqB;IACzB;;IAEA;QACI,kCAAkC;IACtC;AACJ;;AAEA,WAAW;AACX;IACI,cAAc;AAClB;;AAEA;IACI,kBAAkB;AACtB;;AAEA;IACI,0BAA0B;AAC9B;;AAEA;;IAEI,yBAAyB;AAC7B;;AAEA,iBAAiB;AACjB;IACI,kBAAkB;AACtB;;AAEA;IACI,kBAAkB;IAClB,gCAAgC;IAChC,6BAA6B;IAC7B,gBAAgB;IAChB,YAAY;AAChB;;AAEA,gBAAgB;AAChB;IACI,kBAAkB;IAClB,gCAAgC;AACpC;;AAEA,gBAAgB;AAChB;IACI,aAAa;AACjB;;AAEA,gBAAgB;AAChB;IACI,WAAW;IACX,kBAAkB;IAClB,WAAW;AACf;;AAEA,eAAe;AACf;IACI,kBAAkB;IAClB,gCAAgC;AACpC;;AAEA,6DAA6D;AAC7D;IACI,sBAAsB;IACtB,aAAa;IACb,sBAAsB;IACtB,wCAAwC;IACxC,gBAAgB;AACpB;;AAEA;IACI,eAAe;IACf,mBAAmB;AACvB;;AAEA;IACI,eAAe;IACf,gBAAgB;IAChB,mBAAmB;IACnB,6BAA6B;IAC7B,mBAAmB;AACvB;;AAEA,uDAAuD;;AAEvD,WAAW;AACX;IACI,mBAAmB;IACnB,kBAAkB;IAClB,sBAAsB;IACtB,wEAAwE;IACxE,yBAAyB;AAC7B;;AAEA;IACI,wEAAwE;IACxE,2BAA2B;AAC/B;;AAEA;IACI,aAAa;IACb,yBAAyB;IACzB,6BAA6B;IAC7B,kBAAkB;IAClB,2BAA2B;IAC3B,4BAA4B;AAChC;;AAEA;IACI,SAAS;IACT,eAAe;IACf,iBAAiB;IACjB,oBAAoB;IACpB,aAAa;IACb,mBAAmB;AACvB;;AAEA;IACI,aAAa;AACjB;;AAEA;IACI,kBAAkB;IAClB,yBAAyB;IACzB,0BAA0B;IAC1B,eAAe;IACf,WAAW;IACX,8BAA8B;IAC9B,+BAA+B;AACnC;;AAEA;IACI,aAAa;IACb,eAAe;IACf,SAAS;AACb;;AAEA;IACI,qBAAqB;IACrB,gBAAgB;IAChB,kBAAkB;IAClB,eAAe;IACf,iBAAiB;IACjB,YAAY;AAChB;;AAEA;IACI,yBAAyB;AAC7B;;AAEA;IACI,yBAAyB;AAC7B;;AAEA;IACI,yBAAyB;AAC7B;;AAEA,6CAA6C;;AAE7C,WAAW;AACX;IACI,yBAAyB;IACzB,kBAAkB;IAClB,aAAa;IACb,mBAAmB;IACnB,wCAAwC;AAC5C;;AAEA;IACI,gBAAgB;AACpB;;AAEA,UAAU;;AAEV,mDAAmD;;AAEnD;IACI,kBAAkB;IAClB,6BAA6B;IAC7B,yBAAyB;IACzB,aAAa;IACb,8BAA8B;IAC9B,mBAAmB;AACvB;;AAEA;IACI,yBAAyB;IACzB,YAAY;AAChB;;AAEA;IACI,yBAAyB;IACzB,YAAY;AAChB;;AAEA;IACI,yBAAyB;IACzB,YAAY;AAChB;;AAEA;IACI,qBAAqB;IACrB,gBAAgB;IAChB,kBAAkB;IAClB,eAAe;IACf,gBAAgB;AACpB;;AAEA;IACI,yBAAyB;IACzB,YAAY;AAChB;;AAEA;IACI,sBAAsB;IACtB,YAAY;AAChB;;AAEA;IACI,yBAAyB;IACzB,YAAY;AAChB;;AAEA;IACI,yBAAyB;IACzB,YAAY;AAChB;;AAEA;IACI,SAAS;IACT,eAAe;IACf,iBAAiB;AACrB;;AAEA;IACI,aAAa;AACjB;;AAEA;IACI,kBAAkB;IAClB,yBAAyB;IACzB,0BAA0B;IAC1B,aAAa;IACb,8BAA8B;IAC9B,mBAAmB;AACvB;;AAEA;IACI,WAAW;IACX,eAAe;AACnB;;AAEA;IACI,aAAa;IACb,QAAQ;AACZ;;AAEA,uDAAuD;AACvD;IACI,mBAAmB;IACnB,aAAa;IACb,sBAAsB;IACtB,kBAAkB;IAClB,yBAAyB;AAC7B;;AAEA;IACI,aAAa;IACb,mBAAmB;IACnB,oBAAoB;IACpB,6BAA6B;AACjC;;AAEA;;IAEI,iBAAiB;IACjB,gBAAgB;IAChB,sBAAsB;IACtB,aAAa;IACb,mBAAmB;AACvB;;AAEA;;IAEI,kBAAkB;IAClB,YAAY;IACZ,6BAA6B;AACjC;;AAEA;;IAEI,mBAAmB;AACvB;;AAEA;IACI,iBAAiB;IACjB,cAAc;AAClB;;AAEA;IACI,mBAAmB;IACnB,cAAc;IACd,iBAAiB;AACrB;;AAEA;IACI,mBAAmB;IACnB,cAAc;IACd,iBAAiB;AACrB;;AAEA;IACI,yBAAyB;AAC7B;;AAEA;IACI,aAAa;IACb,gBAAgB;IAChB,aAAa;IACb,sBAAsB;IACtB,kBAAkB;IAClB,yBAAyB;AAC7B;;AAEA;IACI,mBAAmB;IACnB,aAAa;IACb,sBAAsB;IACtB,kBAAkB;IAClB,sBAAsB;AAC1B;;AAEA;IACI,iBAAiB;IACjB,mBAAmB;IACnB,mBAAmB;IACnB,6BAA6B;AACjC;;AAEA;IACI,iBAAiB;IACjB,gBAAgB;IAChB,aAAa;IACb,gBAAgB;IAChB,sBAAsB;IACtB,kBAAkB;IAClB,yBAAyB;AAC7B;;AAEA,kBAAkB;AAClB;IACI,kBAAkB;IAClB,aAAa;IACb,eAAe;AACnB;;AAEA;IACI,cAAc;IACd,WAAW;IACX,gBAAgB;IAChB,cAAc;IACd,mBAAmB;IACnB,eAAe;IACf,gBAAgB;AACpB;;AAEA;IACI,iBAAiB;IACjB,eAAe;IACf,mBAAmB;AACvB;;AAEA;IACI,eAAe;IACf,WAAW;IACX,gBAAgB;AACpB;;AAEA,oBAAoB;AACpB;IACI,4BAA4B;IAC5B,2BAA2B;IAC3B,wBAAwB;IACxB,2BAA2B;IAC3B,iCAAiC;IACjC,6BAA6B;IAC7B,oCAAoC;AACxC;;AAEA;IACI,oBAAoB;AACxB;;AAEA;IACI,6BAA6B;IAC7B,wBAAwB;IACxB,0BAA0B;AAC9B;;AAEA;IACI,yBAAyB;IACzB,sBAAsB;IACtB,2BAA2B;IAC3B,yBAAyB;IACzB,8BAA8B;IAC9B,0BAA0B;IAC1B,2BAA2B;AAC/B;;AAEA;IACI,4BAA4B;IAC5B,0BAA0B;IAC1B,8BAA8B;AAClC;;AAEA;IACI,0BAA0B;IAC1B,sBAAsB;IACtB,2BAA2B;AAC/B;;AAEA;IACI,kBAAkB;IAClB,YAAY;AAChB;;AAEA;IACI,aAAa;IACb,sBAAsB;IACtB,gBAAgB;IAChB,0BAA0B;AAC9B;;AAEA,kCAAkC;;AAElC,mDAAmD;AACnD;IACI,iBAAa;IAAb,aAAa;IACb,4DAA4D;IAC5D,cAAS;IAAT,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,sBAAsB;IACtB,kBAAkB;IAClB,aAAa;IACb,kBAAkB;IAClB,oBAAoB;AACxB;;AAEA;IACI,uCAAuC;IACvC,2BAA2B;AAC/B;;AAEA;IACI,aAAa;IACb,aAAa;IACb,mBAAmB;IACnB,uBAAuB;IACvB,mBAAmB;IACnB,gBAAgB;AACpB;;AAEA;IACI,eAAe;IACf,iBAAiB;IACjB,sBAAmB;OAAnB,mBAAmB;AACvB;;AAEA;IACI,eAAe;IACf,WAAW;IACX,qBAAqB;IACrB,mBAAmB;AACvB;;AAEA,2DAA2D;AAC3D;IACI,mBAAmB;AACvB;;AAEA,gEAAgE;AAChE;IACI,aAAa;IACb,gBAAgB;AACpB;;AAEA;IACI,WAAW;IACX,iBAAiB;IACjB,yBAAyB;IACzB,iBAAiB;IACjB,wCAAwC;IACxC,mBAAmB;AACvB;;AAEA;;IAEI,yBAAyB;IACzB,YAAY;IACZ,kBAAkB;IAClB,sBAAsB;IACtB,eAAe;AACnB;;AAEA;IACI,mBAAmB;IACnB,YAAY;IACZ,iBAAiB;IACjB,gBAAgB;IAChB,MAAM;IACN,iCAAiC;IACjC,yCAAyC;AAC7C;;AAEA;IACI,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;AACpB;;AAEA;IACI,gBAAgB;IAChB,YAAY;IACZ,mBAAmB;AACvB;;AAEA;IACI,gBAAgB;IAChB,YAAY;IACZ,mBAAmB;AACvB;;AAEA;IACI,aAAa;IACb,sBAAsB;IACtB,QAAQ;AACZ;;AAEA;IACI,aAAa;IACb,mBAAmB;IACnB,uBAAuB;IACvB,QAAQ;AACZ;;AAEA;IACI,SAAS;IACT,qBAAqB;AACzB;;AAEA;IACI,gBAAgB;IAChB,mBAAmB;AACvB;;AAEA,WAAW;AACX;IACI,oCAAoC;IACpC,8BAA8B;AAClC;;AAEA;IACI,oCAAoC;IACpC,8BAA8B;AAClC;;AAEA;IACI,+BAA+B;AACnC;;AAEA;IACI,+BAA+B;AACnC;;AAEA;IACI,8BAA8B;IAC9B,oCAAoC;IACpC,kBAAkB;AACtB;;AAEA;IACI,cAAc;IACd,kBAAkB;IAClB,QAAQ;IACR,UAAU;IACV,eAAe;AACnB;;AAEA,OAAO;AACP;IACI,aAAa;IACb,eAAe;IACf,SAAS;IACT,mBAAmB;IACnB,aAAa;IACb,mBAAmB;IACnB,kBAAkB;AACtB;;AAEA;IACI,aAAa;IACb,mBAAmB;IACnB,QAAQ;IACR,gBAAgB;AACpB;;AAEA;IACI,WAAW;IACX,YAAY;IACZ,yBAAyB;IACzB,kBAAkB;AACtB;;AAEA,SAAS;AACT;IACI,mBAAmB;IACnB,aAAa;IACb,mBAAmB;IACnB,kBAAkB;AACtB;;AAEA,2BAA2B;AAC3B;IACI,2BAA2B;IAC3B,yBAAyB;IACzB,2BAA2B;IAC3B,2BAA2B;IAC3B,oCAAoC;IACpC,6BAA6B;IAC7B,6BAA6B;AACjC;;AAEA,0BAA0B;AAC1B;IACI,2BAA2B;IAC3B,kBAAkB;IAClB,2CAA2C;IAC3C,8BAA8B;AAClC;;AAEA;IACI,2BAA2B;IAC3B,iBAAiB;IACjB,4CAA4C;AAChD;;AAEA,WAAW;AACX;IACI,mBAAmB;IACnB,aAAa;IACb,mBAAmB;IACnB,kBAAkB;AACtB;;AAEA,WAAW;AACX;IACI,mBAAmB;IACnB,aAAa;IACb,mBAAmB;IACnB,yBAAyB;IACzB,kBAAkB;AACtB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,iBAAiB;IACjB,kBAAkB;IAClB,gBAAgB;IAChB,qBAAqB;AACzB;;AAEA;IACI,YAAY;IACZ,cAAc;AAClB;;AAEA;IACI,iBAAiB;IACjB,cAAc;IACd,eAAe;IACf,qBAAqB;AACzB;;AAEA;IACI,gBAAgB;IAChB,iBAAiB;IACjB,cAAc;IACd,qBAAqB;IACrB,aAAa;IACb,mBAAmB;IACnB,8BAA8B;AAClC;;AAEA;IACI,eAAe;IACf,MAAM;IACN,OAAO;IACP,WAAW;IACX,YAAY;IACZ,8BAA8B;IAC9B,aAAa;IACb,uBAAuB;IACvB,mBAAmB;IACnB,aAA2B;IAA3B,2BAA2B;AAC/B;;AAEA;IACI,iBAAiB;IACjB,aAAa;IACb,kBAAkB;IAClB,kBAAkB;AACtB;;AAEA;IACI,mBAAmB;IACnB,aAAa;IACb,kBAAkB;IAClB,cAAc;IACd,eAAe;AACnB;;AAEA,4DAA4D;AAC5D;IACI,qBAAqB;IACrB,qBAAqB;IACrB,iBAAiB;IACjB,uBAAuB;IACvB,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,4BAA4B;AAChC;;AAEA;IACI,iBAAiB;IACjB,kBAAkB;AACtB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,gBAAgB;AACpB;;AAEA;IACI,mBAAmB;AACvB;;AAEA,sDAAsD;AACtD;IACI,qBAAqB;IACrB,qBAAqB;IACrB,iBAAiB;IACjB,uBAAuB;IACvB,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,4BAA4B;AAChC;;AAEA,8DAA8D;AAC9D;IACI,mBAAmB;AACvB;;AAEA,oDAAoD;AACpD;IACI,gBAAgB;IAChB,cAAc;IACd,aAAa;AACjB;;AAEA;IACI,qBAAqB;IACrB,WAAW;IACX,YAAY;IACZ,iBAAiB;IACjB,kBAAkB;IAClB,mBAAmB;IACnB,yBAAyB;IACzB,kBAAkB;IAClB,iBAAiB;AACrB;;AAEA;IACI,aAAa;IACb,gBAAgB;IAChB,aAAa;IACb,mBAAmB;IACnB,yBAAyB;IACzB,kBAAkB;IAClB,iBAAiB;IACjB,gBAAgB;AACpB;;AAEA;IACI,iBAAa;IAAb,aAAa;IACb,2DAA2D;IAC3D,aAAQ;IAAR,QAAQ;AACZ;;AAEA;IACI,WAAW;IACX,YAAY;IACZ,aAAa;IACb,mBAAmB;IACnB,uBAAuB;IACvB,yBAAyB;IACzB,kBAAkB;IAClB,eAAe;IACf,oBAAoB;AACxB;;AAEA;IACI,mBAAmB;IACnB,YAAY;AAChB;;AAEA;IACI,mBAAmB;IACnB,YAAY;IACZ,qBAAqB;AACzB;;AAEA;IACI,mBAAmB;IACnB,aAAa;IACb,mBAAmB;IACnB,kBAAkB;AACtB;;AAEA;IACI,mBAAmB;IACnB,cAAc;IACd,gCAAgC;IAChC,mBAAmB;AACvB;;AAEA;IACI,8BAA8B;IAC9B,aAAa;AACjB;;AAEA;IACI,aAAa;IACb,sBAAsB;IACtB,kBAAkB;IAClB,cAAc;IACd,mBAAmB;AACvB;;AAEA;IACI,iBAAiB;AACrB;;AAEA;IACI,qBAAqB;IACrB,gBAAgB;IAChB,mBAAmB;IACnB,gBAAgB;IAChB,iBAAiB;AACrB;;AAEA,mDAAmD;AACnD,aAAa;AACb;;IAEI,+BAA+B;IAC/B,eAAe;AACnB;;AAEA;IACI,iBAAiB;IACjB,kBAAkB;IAClB,yBAAyB;IACzB,8BAA8B;IAC9B,kBAAkB;AACtB;;AAEA;IACI,gBAAgB;AACpB;;AAEA,cAAc;AACd;IACI,kBAAkB;IAClB,eAAe;IACf,gBAAgB;IAChB,mBAAmB;AACvB;;AAEA;IACI,WAAW;IACX,cAAc;IACd,WAAW;AACf;;AAEA;IACI,kBAAkB;IAClB,MAAM;IACN,SAAS;IACT,SAAS;IACT,UAAU;IACV,iBAAiB;IACjB,gBAAgB;AACpB;;AAEA;IACI,kBAAkB;IAClB,mBAAmB;IACnB,WAAW;AACf;;AAEA;IACI,WAAW;IACX,cAAc;IACd,WAAW;AACf;;AAEA;IACI,kBAAkB;IAClB,UAAU;IACV,aAAa;IACb,kBAAkB;IAClB,mBAAmB;IACnB,sBAAsB;IACtB,wCAAwC;AAC5C;;AAEA;IACI,aAAa;IACb,WAAW;AACf;;AAEA;IACI,kBAAkB;IAClB,SAAS;IACT,SAAS;IACT,kBAAkB;IAClB,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,gBAAgB;IAChB,sBAAsB;IACtB,iCAAiC;AACrC;;AAEA,WAAW;AACX;IACI,mBAAmB;IACnB,qBAAqB;AACzB;;AAEA;IACI,qBAAqB;AACzB;;AAEA,YAAY;AACZ;IACI,mBAAmB;IACnB,qBAAqB;AACzB;;AAEA;IACI,qBAAqB;AACzB;;AAEA,mDAAmD;AACnD;IACI,mBAAmB;IACnB,aAAa;IACb,sBAAsB;IACtB,kBAAkB;AACtB;;AAEA;IACI,yBAAyB;AAC7B;;AAEA;IACI,iBAAiB;AACrB;;AAEA;IACI,aAAa;IACb,8BAA8B;IAC9B,mBAAmB;IACnB,mBAAmB;AACvB;;AAEA;IACI,yBAAyB;IACzB,YAAY;IACZ,gBAAgB;IAChB,mBAAmB;IACnB,eAAe;IACf,gBAAgB;AACpB;;AAEA,yDAAyD;AACzD;IACI,yBAAyB;IACzB,aAAa;IACb,kBAAkB;IAClB,mBAAmB;AACvB;;AAEA;IACI,yBAAyB;IACzB,yBAAyB;IACzB,kBAAkB;IAClB,aAAa;IACb,mBAAmB;AACvB;;AAEA,0DAA0D;;AAE1D,iEAAiE;AACjE,aAAa;AACb;;IAEI,+BAA+B;IAC/B,eAAe;AACnB;;AAEA,aAAa;AACb;;IAEI,+BAA+B;IAC/B,eAAe;AACnB;;AAEA;IACI,kBAAkB;IAClB,gBAAgB;AACpB;;AAEA,WAAW;AACX;IACI,kBAAkB;IAClB,QAAQ;IACR,UAAU;IACV,iCAAiC;AACrC;;AAEA,qEAAqE;AACrE,aAAa;AACb;;IAEI,+BAA+B;IAC/B,eAAe;IACf,iBAAiB;IACjB,WAAW;IACX,cAAc;IACd,cAAc;AAClB;;AAEA,mDAAmD;AACnD;IACI,yBAAyB;IACzB,aAAa;IACb,kBAAkB;IAClB,mBAAmB;AACvB;;AAEA,gCAAgC;AAChC;IACI,uBAAkC;IAAlC,kCAAkC;AACtC;;AAEA;IACI,uBAA2C;IAA3C,2CAA2C;AAC/C;;AAEA;IACI,UAAU;IACV,iBAAiB;IACjB,uBAAkC;IAAlC,kCAAkC;AACtC;;AAEA;IACI,kBAAkB;IAClB,wCAAwC;IACxC,uBAA0C;IAA1C,0CAA0C;AAC9C;;AAEA,8BAA8B;AAC9B;IACI,uBAAkC;IAAlC,kCAAkC;AACtC;;AAEA;IACI,uBAA2C;IAA3C,2CAA2C;AAC/C;;AAEA;IACI,uBAAkC;IAAlC,kCAAkC;AACtC;;AAEA;IACI,uBAA0C;IAA1C,0CAA0C;AAC9C;;AAEA;IACI,yBAAyB;IACzB,gCAAgC;IAChC,kBAAkB;IAClB,2BAA2B;IAC3B,4BAA4B;AAChC;;AAEA;IACI,sBAAsB;IACtB,aAAa;IACb,mBAAmB;IACnB,kBAAkB;IAClB,kBAAkB;AACtB;;AAEA;IACI,kBAAkB;IAClB,SAAS;IACT,WAAW;AACf;;AAEA;IACI,aAAa;IACb,UAAU;AACd;;AAEA;IACI,aAAa;IACb,eAAe;IACf,gBAAgB;AACpB;;AAEA;IACI,qBAAqB;IACrB,mBAAmB;IACnB,mBAAmB;AACvB;;AAEA,aAAa;AACb;;IAEI,+BAA+B;IAC/B,eAAe;IACf,iBAAiB;IACjB,WAAW;IACX,cAAc;IACd,cAAc;AAClB;;AAEA;;IAEI,sBAAsB;IACtB,yCAAyC;AAC7C;;AAEA;IACI,kBAAkB;IAClB,gBAAgB;IAChB,gBAAgB;IAChB,sBAAsB;IACtB,kBAAkB;IAClB,YAAY;IACZ,sBAAsB;IACtB,aAAa;IACb,iBAAiB;IACjB,aAAa;IACb,mBAAmB;IACnB,uBAAuB;AAC3B;;AAEA;IACI,kBAAkB;IAClB,QAAQ;IACR,UAAU;IACV,mBAAmB;IACnB,YAAY;IACZ,kBAAkB;IAClB,eAAe;IACf,iBAAiB;IACjB,wCAAwC;IACxC,yBAAyB;IACzB,iCAAiC;IACjC,gBAAgB;IAChB,eAAe;AACnB;;AAEA;IACI,mBAAmB;IACnB,qBAAqB;AACzB;;AAEA;IACI,yBAAyB;IACzB,YAAY;IACZ,iBAAiB;IACjB,iBAAiB;IACjB,yBAAyB;AAC7B;;AAEA;IACI,yBAAyB;IACzB,sBAAsB;AAC1B;;AAEA;IACI,0BAA0B;IAC1B,aAAa;IACb,kBAAkB;IAClB,gBAAgB;IAChB,yBAAyB;IACzB,kBAAkB;AACtB;;AAEA;IACI,eAAe;IACf,cAAc;IACd,kBAAkB;IAClB,sBAAsB;AAC1B;;AAEA;IACI,gBAAgB;AACpB;;AAEA;IACI,gBAAgB;AACpB;;AAEA,8BAA8B;;AAE9B,cAAc;AACd;IACI,2BAA2B;IAC3B,iCAAiC;IACjC,6BAA6B;IAC7B,0BAA0B;IAC1B,8BAA8B;IAC9B,iCAAiC;IACjC,mDAAmD;AACvD;;AAEA;IACI,4BAA4B;IAC5B,0BAA0B;IAC1B,wCAAwC;AAC5C;;AAEA;IACI,oCAAoC;AACxC;;AAEA;IACI,iBAAiB;IACjB,WAAW;IACX,eAAe;AACnB;;AAEA;IACI,gBAAgB;IAChB,gBAAgB;IAChB,aAAa;IACb,sBAAsB;AAC1B;;AAEA;IACI,yBAAyB;IACzB,6BAA6B;IAC7B,kBAAkB;IAClB,8BAA8B;IAC9B,+BAA+B;AACnC;;AAEA,gBAAgB;AAChB;;;IAGI,4BAA4B;IAC5B,uBAAuB;IACvB,oCAAoC;IACpC,oCAAoC;IACpC,2CAA2C;IAC3C,6BAA6B;IAC7B,4BAA4B;IAC5B,sCAAsC;IACtC,uCAAuC;IACvC,oCAAoC;IACpC,wCAAgC;IAAhC,gCAAgC;AACpC;;AAEA;;;IAGI,oCAAoC;IACpC,uBAAuB;IACvB,gCAAgC;IAChC,wCAAgC;IAAhC,gCAAgC;AACpC;;AAEA;;;IAGI,oCAAoC;IACpC,uBAAuB;IACvB,oCAAoC;IACpC,2CAA2C;IAC3C,wCAAgC;IAAhC,gCAAgC;AACpC;;AAEA,0BAA0B;AAC1B;IACI,8BAA8B;IAC9B,2CAA2C;AAC/C;;AAEA;IACI,8BAA8B;AAClC;;AAEA;IACI,4BAA4B;IAC5B,sBAAsB;IACtB,oCAAoC;IACpC,iCAAiC;IACjC,2CAA2C;IAC3C,6BAA6B;IAC7B,4BAA4B;IAC5B,sCAAsC;IACtC,uCAAuC;IACvC,oCAAoC;IACpC,wCAAgC;IAAhC,gCAAgC;AACpC;;AAEA;IACI,oCAAoC;IACpC,yBAAyB;IACzB,wCAAgC;IAAhC,gCAAgC;AACpC;;AAEA;;;;;;IAMI,oCAAoC;IACpC,uBAAuB;IACvB,oCAAoC;IACpC,2CAA2C;IAC3C,wCAAgC;IAAhC,gCAAgC;AACpC;;AAEA,+BAA+B;AAC/B;IACI,8BAA8B;IAC9B,2CAA2C;AAC/C;;AAEA;IACI,8BAA8B;AAClC;;AAEA;IACI,4BAA4B;IAC5B,sBAAsB;IACtB,oCAAoC;IACpC,iCAAiC;IACjC,2CAA2C;IAC3C,6BAA6B;IAC7B,4BAA4B;IAC5B,sCAAsC;IACtC,uCAAuC;IACvC,oCAAoC;IACpC,wCAAgC;IAAhC,gCAAgC;AACpC;;AAEA;IACI,oCAAoC;IACpC,yBAAyB;IACzB,wCAAgC;IAAhC,gCAAgC;AACpC;;AAEA;;;;;;IAMI,oCAAoC;IACpC,uBAAuB;IACvB,oCAAoC;IACpC,2CAA2C;IAC3C,wCAAgC;IAAhC,gCAAgC;AACpC;;AAEA,aAAa;AACb;IACI,mBAAmB;IACnB,sBAAsB;IACtB,kBAAkB;IAClB,uCAAuC;IACvC,gBAAgB;AACpB;;AAEA;IACI,kBAAkB;IAClB,yBAAyB;IACzB,6BAA6B;IAC7B,eAAe;IACf,yBAAyB;IACzB,kBAAkB;IAClB,YAAY;AAChB;;AAEA;IACI,gBAAgB;IAChB,0BAA0B;IAC1B,kBAAkB;IAClB,WAAW;IACX,QAAQ;IACR,2BAA2B;IAC3B,YAAY;IACZ,yBAAyB;AAC7B;;AAEA;IACI,0CAA0C;AAC9C;;AAEA;IACI,yBAAyB;AAC7B;;AAEA;IACI,SAAS;IACT,eAAe;IACf,YAAY;IACZ,iBAAiB;AACrB;;AAEA;IACI,UAAU;IACV,sBAAsB;IACtB,6BAA6B;AACjC;;AAEA,SAAS;AACT;IACI,WAAW;IACX,yBAAyB;IACzB,gBAAgB;IAChB,sBAAsB;AAC1B;;AAEA;IACI,6BAA6B;AACjC;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,UAAU;IACV,sBAAsB;IACtB,4BAA4B;IAC5B,6BAA6B;AACjC;;AAEA;IACI,kBAAkB;AACtB;;AAEA,SAAS;AACT;IACI,yBAAyB;IACzB,YAAY;IACZ,iBAAiB;IACjB,kBAAkB;IAClB,wBAAwB;AAC5B;;AAEA,aAAa;AACb;IACI,yBAAyB;IACzB,YAAY;IACZ,iBAAiB;IACjB,gBAAgB;IAChB,4BAA4B;AAChC;;AAEA,WAAW;AACX;IACI,UAAU;IACV,sBAAsB;IACtB,sBAAsB;IACtB,WAAW;IACX,cAAc;IACd,mBAAmB;AACvB;;AAEA,SAAS;AACT;IACI,yBAAyB;IACzB,YAAY;AAChB;;AAEA;IACI,yBAAyB;IACzB,YAAY;AAChB;;AAEA;IACI,yBAAyB;IACzB,WAAW;IACX,sBAAsB;AAC1B;;AAEA,UAAU;AACV;IACI,yBAAyB;AAC7B;;AAEA;IACI,yBAAyB;AAC7B;;AAEA,SAAS;AACT;IACI,yBAAyB;IACzB,cAAc;IACd,iBAAiB;IACjB,yBAAyB;AAC7B;;AAEA;IACI,4BAA4B;AAChC;;AAEA,eAAe;AACf;IACI,WAAW;AACf;;AAEA,kCAAkC;AAClC;IACI,kBAAkB;IAClB,wBAA4C;IAA5C,4CAA4C;AAChD;;AAEA,WAAW;AACX;IACI,gBAAgB;IAChB,aAAa;IACb,yBAAyB;IACzB,sBAAsB;IACtB,kBAAkB;IAClB,uCAAuC;AAC3C;;AAEA;IACI,aAAa;IACb,mBAAmB;IACnB,cAAc;IACd,iBAAiB;IACjB,oBAAoB;IACpB,6BAA6B;AACjC;;AAEA;IACI,iBAAiB;IACjB,6BAA6B;IAC7B,yBAAyB;AAC7B;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,yBAAyB;AAC7B;;AAEA,UAAU;AACV;IACI,0BAA0B;IAC1B,YAAY;IACZ,+CAA+C;AACnD;;AAEA;IACI,YAAY;IACZ,yBAAyB;IACzB,YAAY;IACZ,qBAAqB;AACzB;;AAEA;IACI,yBAAyB;IACzB,qBAAqB;AACzB;;AAEA,WAAW;AACX;IACI,yBAAyB;IACzB,qBAAqB;IACrB,iBAAiB;IACjB,iBAAiB;AACrB;;AAEA;IACI,yBAAyB;IACzB,qBAAqB;AACzB;;AAEA,mDAAmD;AACnD;IACI,yBAAyB;IACzB,aAAa;IACb,kBAAkB;IAClB,mBAAmB;AACvB;;AAEA;IACI,6BAA6B;IAC7B,oBAAoB;IACpB,mBAAmB;AACvB;;AAEA;IACI,aAAa;IACb,eAAe;IACf,gBAAgB;AACpB;;AAEA;IACI,YAAY;IACZ,aAAa;IACb,oBAAiB;OAAjB,iBAAiB;IACjB,sBAAsB;IACtB,kBAAkB;IAClB,eAAe;AACnB;;AAEA;IACI,yBAAyB;IACzB,aAAa;IACb,kBAAkB;AACtB;;AAEA;IACI,eAAe;IACf,gBAAgB;AACpB;;AAEA,mDAAmD;AACnD;IACI,aAAa;AACjB;;AAEA;IACI,mBAAmB;IACnB,sBAAsB;IACtB,aAAa;IACb,kBAAkB;IAClB,oBAAoB;AACxB;;AAEA;IACI,aAAa;IACb,aAAa;IACb,mBAAmB;IACnB,uBAAuB;IACvB,gBAAgB;IAChB,mBAAmB;AACvB;;AAEA;IACI,eAAe;IACf,iBAAiB;IACjB,sBAAmB;OAAnB,mBAAmB;AACvB;;AAEA;IACI,eAAe;IACf,WAAW;AACf;;AAEA;IACI,gBAAgB;AACpB;;AAEA;IACI,yBAAyB;IACzB,yBAAyB;AAC7B;;AAEA;IACI,gBAAgB;IAChB,aAAa;IACb,sBAAsB;IACtB,kBAAkB;IAClB,yBAAyB;AAC7B;;AAEA;IACI,qBAAqB;IACrB,WAAW;IACX,kBAAkB;AACtB;;AAEA;IACI,gBAAgB;IAChB,iBAAiB;IACjB,sBAAsB;IACtB,YAAY;AAChB;;AAEA;IACI,kBAAkB;IAClB,UAAU;IACV,YAAY;IACZ,yBAAyB;IACzB,YAAY;IACZ,kBAAkB;IAClB,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,iBAAiB;IACjB,eAAe;AACnB;;AAEA,oDAAoD;AACpD;IACI,aAAa;IACb,eAAe;IACf,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,YAAY;IACZ,sBAAsB;IACtB,kBAAkB;IAClB,aAAa;IACb,eAAe;IACf,oBAAoB;AACxB;;AAEA;IACI,qBAAqB;IACrB,uCAAuC;AAC3C;;AAEA;IACI,qBAAqB;IACrB,yBAAyB;AAC7B;;AAEA;IACI,aAAa;IACb,aAAa;IACb,mBAAmB;IACnB,uBAAuB;IACvB,gBAAgB;IAChB,mBAAmB;AACvB;;AAEA;IACI,eAAe;IACf,iBAAiB;IACjB,sBAAmB;OAAnB,mBAAmB;AACvB;;AAEA;IACI,eAAe;IACf,WAAW;IACX,kBAAkB;IAClB,mBAAmB;IACnB,gBAAgB;IAChB,uBAAuB;AAC3B;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,gBAAgB;AACpB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;IACnB,aAAa;IACb,uBAAuB;IACvB,kBAAkB;IAClB,yBAAyB;AAC7B;;AAEA;IACI,aAAa;AACjB;;AAEA;IACI,gBAAgB;AACpB;;AAEA;IACI,kBAAkB;AACtB;;AAEA,oEAAoE;AACpE,WAAW;AACX;IACI,sBAAsB;IACtB,kBAAkB;IAClB,wEAAwE;IACxE,mBAAmB;IACnB,kBAAkB;IAClB,gBAAgB;IAChB,yBAAyB;AAC7B;;AAEA,SAAS;AACT;IACI,mBAAmB;IACnB,WAAW;AACf;;AAEA,YAAY;AACZ;IACI,gBAAgB;IAChB,eAAe;IACf,gBAAgB;IAChB,eAAe;IACf,mBAAmB;IACnB,kBAAkB;AACtB;;AAEA,kBAAkB;AAClB;;;IAGI,2BAA2B;IAC3B,0BAA0B;IAC1B,2BAA2B;IAC3B,0BAA0B;IAC1B,8BAA8B;IAC9B,6BAA6B;IAC7B,2BAA2B,EAAE,eAAe;IAC5C,gCAAgC;IAChC,iCAAiC;AACrC;;AAEA,kBAAkB;AAClB;;IAEI,2BAA2B;IAC3B,0BAA0B;IAC1B,2BAA2B;IAC3B,2BAA2B;AAC/B;;AAEA,UAAU;AACV;IACI,8BAA8B;IAC9B,gBAAgB;AACpB;;AAEA,WAAW;AACX;IACI,yBAAyB;IACzB,YAAY;IACZ,kBAAkB;IAClB,gBAAgB;IAChB,cAAc;IACd,gBAAgB;IAChB,eAAe;IACf,kBAAkB;IAClB,qBAAqB;IACrB,gBAAgB;AACpB;;AAEA,WAAW;AACX;IACI,qBAAqB;IACrB,mBAAmB;IACnB,eAAe;IACf,kBAAkB;AACtB;;AAEA,iCAAiC;AACjC;;;IAGI,2BAA2B;IAC3B,wBAA0C;IAA1C,0CAA0C;IAC1C,sDAAsD;IACtD,gDAAgD;IAChD,iCAAiC;IACjC,6BAA6B;IAC7B,0BAA0B;IAC1B,yBAAyB;AAC7B;;AAEA,iBAAiB;AACjB;;IAEI,yBAAyB;IACzB,8BAA8B;IAC9B,qBAAqB;AACzB;;AAEA,oBAAoB;AACpB;IACI,4BAA4B;IAC5B,2BAA2B;AAC/B;;AAEA,kBAAkB;AAClB;IACI,kBAAkB;AACtB;;AAEA,qBAAqB;AACrB;;IAEI,gBAAgB;IAChB,kBAAkB;IAClB,4BAA4B;AAChC;;AAEA,qBAAqB;AACrB;IACI,4BAA4B;AAChC;;AAEA,0BAA0B;AAC1B;;IAEI,8BAA8B;AAClC;;AAEA,oCAAoC;AACpC;IACI,0BAA0B;IAC1B,wBAA0C;IAA1C,0CAA0C;IAC1C,yBAAyB;IACzB,8BAA8B;IAC9B,qBAAqB;IACrB,0BAA0B;IAC1B,qBAAqB;IACrB,0BAA0B;AAC9B;;AAEA,YAAY;AACZ;IACI,oCAAoC;IACpC,wCAAwC;AAC5C;;AAEA,iCAAiC;AACjC;;IAEI,wBAA0C;IAA1C,0CAA0C;AAC9C;;AAEA,mCAAmC;AACnC,uBAAuB;AACvB;IACI,6BAA6B;IAC7B,oBAAoB;IACpB,kBAAkB;IAClB,wBAA0C;IAA1C,0CAA0C;IAC1C,aAAa,EAAE,wBAAwB;IACvC,sBAAsB;IACtB,2BAA2B;IAC3B,yBAAyB;IACzB,0BAA0B;IAC1B,0BAA0B;IAC1B,2BAA2B;IAC3B,2BAA2B;IAC3B,iCAAiC;IACjC,iCAAiC;IACjC,+CAA+C;IAC/C,6BAA6B;IAC7B,qDAAqD;IACrD,uCAAuC;AAC3C;;AAEA,8BAA8B;AAC9B;IACI,0BAA0B;AAC9B;;AAEA,eAAe;AACf;IACI,wBAAwB;AAC5B;;AAEA,aAAa;AACb;;;;IAII,yBAAyB;IACzB,8BAA8B;IAC9B,qBAAqB;AACzB;;AAEA,aAAa;AACb;IACI,mBAAmB;IACnB,qBAAqB;AACzB;;AAEA,aAAa;AACb;IACI,2BAA2B;IAC3B,oCAAoC;IACpC,4BAA4B;AAChC;;AAEA,oBAAoB;;AAEpB,oEAAoE;;AAEpE,gEAAgE;;AAEhE,6BAA6B;AAC7B;IACI,4BAA4B;IAC5B,2BAA2B;AAC/B;;AAEA,2DAA2D;;AAE3D,aAAa;AACb;IACI,yBAAyB;AAC7B;;AAEA;IACI,yBAAyB;AAC7B;;AAEA;IACI,yBAAyB;AAC7B;;AAEA;IACI,yBAAyB;AAC7B;;AAEA;IACI,YAAY;AAChB;;AAEA,YAAY;AACZ;IACI,yBAAyB;IACzB,kBAAkB;IAClB,eAAe;IACf,iBAAiB;IACjB,aAAa;IACb,kBAAkB;AACtB;;AAEA,YAAY;AACZ;IACI,kBAAkB;IAClB,WAAW;IACX,QAAQ;IACR,2BAA2B;AAC/B;;AAEA,cAAc;AACd;IACI,yBAAyB;AAC7B;;AAEA,WAAW;AACX,4CAA4C,SAAS,EAAE;AACvD,2CAA2C,UAAU,EAAE;AACvD,2CAA2C,UAAU,EAAE;AACvD,6CAA6C,UAAU,EAAE;AACzD,6CAA6C,UAAU,EAAE;AACzD,6CAA6C,SAAS,EAAE;AACxD,6CAA6C,UAAU,EAAE;AACzD,8CAA8C,UAAU,EAAE;AAC1D,8CAA8C,UAAU,EAAE;;AAE1D,eAAe;AACf;;IAEI,iCAAiC;IACjC,4BAA4B;AAChC;;AAEA,UAAU;AACV;IACI,kBAAkB;IAClB,gBAAgB;AACpB;;AAEA,UAAU;AACV;;;;IAII,kBAAkB;AACtB;;AAEA,YAAY;AACZ;;IAEI,kBAAkB;IAClB,qCAAqC,EAAE,eAAe;AAC1D;;AAEA,aAAa;AACb;IACI,gBAAgB;IAChB,gBAAgB;AACpB;;AAEA,UAAU;AACV;IACI,kBAAkB;AACtB;;AAEA,oBAAoB;;AAEpB;IACI,4BAA4B;AAChC;;AAEA,iBAAiB;;AAEjB,gBAAgB;AAChB;IACI,4BAA4B;IAC5B,2BAA2B;IAC3B,6BAA6B;IAC7B,iCAAiC;IACjC,6BAA6B;IAC7B,iCAAiC;IACjC,iCAAiC;AACrC;;AAEA,oCAAoC;;AAEpC;IACI,wEAAwE;IACxE,2BAA2B;AAC/B;;AAEA;IACI,kBAAkB;IAClB,MAAM;IACN,OAAO;IACP,WAAW;IACX,YAAY;IACZ,aAAa;IACb,mBAAmB;IACnB,uBAAuB;IACvB,eAAe;AACnB;;AAEA;IACI,eAAe;IACf,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,mBAAmB;AACvB;;AAEA;IACI,iBAAiB;IACjB,oBAAoB;IACpB,WAAW;AACf;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,gBAAgB;AACpB;;AAEA;IACI,yBAAyB;IACzB,YAAY;AAChB;;AAEA;;IAEI,sBAAsB,EAAE,SAAS;IACjC,iBAAiB,EAAE,YAAY;IAC/B,gBAAgB,EAAE,UAAU;AAChC;;AAEA,uBAAuB;AACvB;;;;;;;;;;;;IAYI,6BAA6B,EAAE,SAAS;AAC5C;;AAEA,oBAAoB;;AAEpB;IACI,4BAA4B;AAChC;;AAEA;IACI,4BAA4B;IAC5B,2BAA2B;AAC/B;;AAEA;IACI,4BAA4B;AAChC;;AAEA;IACI,4BAA4B;AAChC;;AAEA;IACI,4BAA4B;AAChC;;AAEA;;;IAGI,4BAA4B;AAChC;;AAEA,iBAAiB;;AAEjB;IACI,2BAA2B;AAC/B;;AAEA;IACI,iBAAiB;IACjB,eAAe;AACnB;;AAEA;IACI,iBAAiB;IACjB,WAAW;AACf;;AAEA;IACI,yBAAyB;AAC7B;;AAEA;IACI,yBAAyB;IACzB,yBAAyB;AAC7B;;AAEA;IACI,SAAS;IACT,eAAe;IACf,iBAAiB;AACrB;;AAEA,8DAA8D;AAC9D,4BAA4B;AAC5B;IACI,wBAAuC;IAAvC,uCAAuC;AAC3C;;AAEA,6BAA6B;AAC7B;IACI,uBAA2C;IAA3C,2CAA2C;AAC/C;;AAEA;IACI,aAAa;IACb,UAAU;AACd;;AAEA;IACI,aAAa;IACb,2BAA2B;IAC3B,4BAA4B;IAC5B,6BAA6B;IAC7B,0BAA0B;AAC9B;;AAEA;IACI,gBAAgB;AACpB;;AAEA;;IAEI,iBAAiB;IACjB,gBAAgB;IAChB,sBAAsB;IACtB,aAAa;IACb,mBAAmB;AACvB;;AAEA,WAAW;AACX;IACI,WAAW;IACX,yBAAyB;IACzB,mBAAmB;IACnB,sBAAsB;IACtB,mBAAmB;AACvB;;AAEA,WAAW;AACX;IACI,oCAAoC;IACpC,uBAAuB;IACvB,iBAAiB;IACjB,iBAAiB;IACjB,gBAAgB;IAChB,6BAA6B;AACjC;;AAEA,cAAc;AACd;;IAEI,sBAAsB;AAC1B;;AAEA,WAAW;AACX;IACI,UAAU;AACd;;AAEA,UAAU;AACV;IACI,iBAAiB;IACjB,sBAAsB;IACtB,sBAAsB;AAC1B;;AAEA,WAAW;AACX;IACI;QACI,mBAAmB;QACnB,2CAA2C;IAC/C;;IAEA;QACI,sBAAsB;QACtB,4CAA4C;IAChD;;IAEA;QACI,mBAAmB;QACnB,yCAAyC;IAC7C;AACJ;;AAEA;IACI,iCAAiC;AACrC;;AAEA;;IAEI,kBAAkB;IAClB,YAAY;IACZ,6BAA6B;AACjC;;AAEA;;IAEI,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;IACnB,aAAa;IACb,yBAAyB;IACzB,kBAAkB;AACtB;;AAEA,gBAAgB;AAChB;;IAEI,gBAAgB;IAChB,iBAAiB;IACjB,gBAAgB;AACpB;;AAEA;IACI,qBAAqB;IACrB,WAAW;IACX,iBAAiB;IACjB,eAAe;AACnB;;AAEA;IACI,YAAY;IACZ,gBAAgB;IAChB,6BAAqB;IAArB,qBAAqB;AACzB;;AAEA;IACI,cAAc;AAClB;;AAEA,sDAAsD;AACtD,kBAAkB;AAClB;IACI,2BAA2B;IAC3B,iCAAiC;IACjC,6BAA6B;IAC7B,wBAAwB;IACxB,oCAAoC;IACpC,4BAA4B;IAC5B,4BAA4B;IAC5B,2BAA2B;IAC3B,yBAAyB;AAC7B;;AAEA,kBAAkB;AAClB;IACI,2BAA2B;IAC3B,iCAAiC;IACjC,6BAA6B;IAC7B,wBAAwB;IACxB,oCAAoC;IACpC,4BAA4B;IAC5B,4BAA4B;IAC5B,2BAA2B;IAC3B,yBAAyB;AAC7B;;AAEA;IACI,oBAAoB;IACpB,mBAAmB;IACnB,kBAAkB;IAClB,mBAAmB;IACnB,iBAAiB;IACjB,sBAAsB;IACtB,sBAAsB;IACtB,kBAAkB;AACtB;;AAEA;IACI,qBAAqB;IACrB,gBAAgB;IAChB,eAAe;IACf,gBAAgB;IAChB,cAAc;IACd,WAAW;IACX,kBAAkB;IAClB,mBAAmB;IACnB,sBAAsB;IACtB,yBAAyB;IACzB,mBAAmB;AACvB;;AAEA;IACI,gBAAgB;IAChB,cAAc;IACd,eAAe;AACnB;;AAEA;IACI,cAAc;AAClB;;AAEA,eAAe;AACf;IACI,oBAAoB;IACpB,mBAAmB;IACnB,kBAAkB;IAClB,mBAAmB;IACnB,iBAAiB;IACjB,sBAAsB;IACtB,sBAAsB;IACtB,kBAAkB;AACtB;;AAEA;IACI,qBAAqB;IACrB,gBAAgB;IAChB,eAAe;IACf,gBAAgB;IAChB,cAAc;IACd,WAAW;IACX,kBAAkB;IAClB,mBAAmB;IACnB,sBAAsB;IACtB,yBAAyB;IACzB,mBAAmB;AACvB;;AAEA;IACI,gBAAgB;IAChB,cAAc;IACd,eAAe;AACnB;;AAEA;IACI,cAAc;AAClB;;AAEA,UAAU;AACV;IACI,mBAAmB;IACnB,gCAAgC;AACpC;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,iBAAiB;IACjB,WAAW;IACX,yBAAyB;IACzB,sBAAsB;IACtB,gCAAgC;IAChC,kBAAkB;IAClB,iBAAiB;IACjB,2BAA2B;IAC3B,4BAA4B;IAC5B,yBAAyB;AAC7B;;AAEA;IACI,yBAAyB;IACzB,cAAc;AAClB;;AAEA;IACI,yBAAyB;IACzB,YAAY;IACZ,yBAAyB;IACzB,gCAAgC;AACpC;;AAEA,oBAAoB;AACpB;IACI,iBAAiB;IACjB,gBAAgB;AACpB;;AAEA,WAAW;AACX;IACI,mBAAmB;IACnB,sBAAsB;IACtB,kBAAkB;IAClB,gBAAgB;AACpB;;AAEA;IACI,WAAW;IACX,yBAAyB;IACzB,SAAS;AACb;;AAEA;IACI,oCAAoC;IACpC,uBAAuB;IACvB,iBAAiB;IACjB,kBAAkB;IAClB,gBAAgB;AACpB;;AAEA;IACI,iBAAiB;IACjB,6BAA6B;IAC7B,sCAAsC;AAC1C;;AAEA;IACI,yBAAyB;AAC7B;;AAEA;IACI,cAAc;IACd,SAAS;IACT,eAAe;IACf,mBAAmB;IACnB,WAAW;AACf;;AAEA;IACI,iBAAiB;IACjB,cAAc;AAClB;;AAEA,wDAAwD;AACxD;IACI,UAAU;IACV,SAAS;AACb;;AAEA;IACI,kBAAkB;AACtB;;AAEA;IACI,WAAW;AACf;;AAEA;IACI,yBAAyB;IACzB,cAAc;AAClB;;AAEA;IACI,YAAY;IACZ,gBAAgB;IAChB,iBAAiB;IACjB,yBAAyB;IACzB,kBAAkB;AACtB;;AAEA;IACI,eAAe;AACnB;;AAEA;IACI,gBAAgB;AACpB;;AAEA,kDAAkD;;AAElD,SAAS;AACT;IACI,4BAA4B;AAChC;;AAEA;IACI,mBAAmB;IACnB,qBAAqB;AACzB;;AAEA;IACI,mBAAmB;AACvB;;AAEA,8BAA8B;AAC9B;IACI,wBAAmC;IAAnC,mCAAmC;IACnC,eAAe;IACf,0BAA0B;IAC1B,mBAAmB;IACnB,4BAA4B;IAC5B,WAAW;IACX,2BAA2B;IAC3B,WAAW;IACX,sBAAsB;IACtB,UAAU;IACV,2BAA2B;IAC3B,SAAS;AACb;;AAEA,gBAAgB;AAChB;IACI,8BAA8B;IAC9B,gCAAgC;IAChC,4BAA4B;IAC5B,2BAA2B;AAC/B;;AAEA,kBAAkB;AAClB;IACI,8BAA8B;IAC9B,6BAA6B;AACjC;;AAEA,gBAAgB;AAChB;IACI,8BAA8B;IAC9B,2BAA2B;IAC3B,kCAAkC;AACtC;;AAEA,cAAc;AACd;IACI,0BAA0B;AAC9B;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;AACvB;;AAEA,SAAS;AACT;IACI,eAAe;AACnB;;AAEA,kDAAkD;AAClD,iBAAiB;AACjB;IACI,WAAW;IACX,kBAAkB;AACtB;;AAEA;IACI,gBAAgB;IAChB,WAAW;IACX,gBAAgB;IAChB,uBAAuB;IACvB,mBAAmB;AACvB;;AAEA;IACI,aAAa;IACb,WAAW;IACX,gBAAgB;IAChB,iBAAiB;IACjB,kBAAkB;IAClB,OAAO;IACP,QAAQ;IACR,0CAA0C;IAC1C,sBAAsB;IACtB,oCAAoC;IACpC,kBAAkB;IAClB,aAA+B;IAA/B,+BAA+B;AACnC;;AAEA,oBAAoB;AACpB;IACI,cAAc;AAClB;;AAEA,mBAAmB;AACnB;IACI,aAAa;AACjB;;AAEA;IACI,iBAAiB;IACjB,gBAAgB;IAChB,YAAY;IACZ,WAAW;AACf;;AAEA,eAAe;AACf;IACI,eAAe;IACf,cAAc;AAClB;;AAEA;IACI,kBAAkB;IAClB,cAAc;IACd,WAAW;AACf;;AAEA;IACI,YAAY;IACZ,eAAe;IACf,WAAW;AACf;;AAEA;IACI,SAAS;AACb;;AAEA;IACI,eAAe;IACf,kBAAkB;IAClB,cAAc;AAClB;;AAEA;IACI,mBAAmB;IACnB,eAAe;IACf,cAAc;IACd,YAAY;IACZ,mBAAmB;IACnB,gBAAgB;IAChB,uBAAuB;IACvB,kBAAkB;AACtB;;AAEA;IACI,yBAAyB;IACzB,kBAAkB;AACtB;;AAEA,gBAAgB;AAChB;IACI,mBAAmB;AACvB;;AAEA,0CAA0C;AAC1C,cAAc;AACd;IACI,kBAAkB;IAClB,cAAc;IACd,gBAAgB;IAChB,mBAAmB;IACnB,kBAAkB;AACtB;;AAEA,aAAa;AACb;IACI,kBAAkB;IAClB,eAAe;IACf,kBAAkB;AACtB;;AAEA,YAAY;AACZ;IACI,gBAAgB;IAChB,eAAe;IACf,gBAAgB;IAChB,mBAAmB;IACnB,eAAe;IACf,eAAe;AACnB;;AAEA,cAAc;AACd;IACI,qBAAqB;IACrB,yBAAyB;AAC7B;;AAEA,aAAa;AACb;IACI,qBAAqB;IACrB,UAAU;IACV,gFAAgF;AACpF;;AAEA,kBAAkB;AAClB;IACI,kBAAkB;IAClB,iCAAiC;AACrC;;AAEA,0DAA0D;AAC1D;IACI,mBAAmB;IACnB,gBAAgB;IAChB,wCAAwC;IACxC,kBAAkB;AACtB;;AAEA;IACI,SAAS;IACT,4BAA4B;AAChC;;AAEA;IACI,iBAAiB;AACrB;;AAEA;IACI,WAAW;IACX,gBAAgB;AACpB;;AAEA;IACI,yBAAyB;AAC7B;;AAEA;IACI,6BAA6B;IAC7B,0BAA0B;IAC1B,2BAA2B;AAC/B;;AAEA;IACI,gBAAgB;IAChB,aAAa;IACb,+BAA+B;IAC/B,8BAA8B;AAClC;;AAEA;IACI,qBAAqB;IACrB,2CAA2C;AAC/C;;AAEA,kEAAkE;AAClE,WAAW;AACX;IACI,uBAAuB;IACvB,aAAa;IACb,sBAAsB;IACtB,uCAAuC;IACvC,mBAAmB;IACnB,gEAAgE;IAChE,eAAe;IACf,gBAAgB;IAChB,WAAW;IACX,YAAY;IACZ,UAAU;IACV,iBAAiB;IACjB,UAAU;IACV,iBAAiB;IACjB,kBAAkB;IAClB,cAAc;AAClB;;AAEA,SAAS;AACT;;IAEI,0BAA0B;AAC9B;;AAEA,OAAO;AACP;;IAEI,4BAA4B;AAChC;;AAEA,WAAW;AACX;IACI,+BAA+B;AACnC;;AAEA,aAAa;AACb;IACI,iCAAiC;AACrC;;AAEA,aAAa;AACb;;IAEI,6BAA6B;AACjC;;AAEA,WAAW;AACX;IACI,4BAA4B;AAChC;;AAEA,aAAa;AACb;IACI,6BAA6B;AACjC;;AAEA,WAAW;AACX;IACI,uBAAuB;IACvB,aAAa;IACb,sBAAsB;IACtB,uCAAuC;IACvC,mBAAmB;IACnB,gEAAgE;IAChE,eAAe;IACf,gBAAgB;IAChB,WAAW;IACX,YAAY;IACZ,UAAU;IACV,iBAAiB;IACjB,UAAU;IACV,iBAAiB;IACjB,kBAAkB;IAClB,cAAc;AAClB;;AAEA,SAAS;AACT;IACI,WAAW;IACX,yBAAyB;IACzB,mBAAmB;IACnB,mBAAmB;IACnB,sBAAsB;AAC1B;;AAEA;;;IAGI,sBAAsB;AAC1B;;AAEA;;IAEI,YAAY;IACZ,qBAAqB;IACrB,qBAAyB;IACzB,kBAAkB;AACtB;;AAEA,gBAAgB;AAChB;IACI,sBAAsB;AAC1B;;AAEA,gBAAgB;AAChB;IACI,kBAAkB;IAClB,SAAS;AACb;;AAEA,SAAS;AACT;IACI,eAAe;IACf,YAAY;IACZ,cAAc;IACd,cAAc;AAClB;;AAEA,SAAS;AACT;IACI,kBAAkB;IAClB,UAAU;AACd;;AAEA,SAAS;AACT;;;;;;IAMI,iBAAiB;IACjB,cAAc;IACd,kBAAkB;AACtB;;AAEA;IACI,eAAe;AACnB;;AAEA;IACI,eAAe;AACnB;;AAEA;IACI,eAAe;AACnB;;AAEA;IACI,eAAe;AACnB;;AAEA;IACI,eAAe;AACnB;;AAEA;IACI,eAAe;AACnB;;AAEA,SAAS;AACT;IACI;QACI,wBAAwB;IAC5B;;IAEA;QACI,SAAS;QACT,UAAU;QACV,uBAAuB;IAC3B;;IAEA;QACI,YAAY;QACZ,gBAAgB;QAChB,UAAU;QACV,SAAS;QACT,YAAY;QACZ,aAAa;QACb,eAAe;QACf,gBAAgB;IACpB;;IAEA,aAAa;IACb;QACI,iBAAiB;QACjB,WAAW;IACf;;IAEA,cAAc;IACd;QACI,wBAAwB;QACxB,wBAAwB;IAC5B;;IAEA,cAAc;IACd;QACI,wBAAwB;IAC5B;AACJ;;AAEA,eAAe;AACf,mBAAmB;AACnB;;;IAGI,oCAAoC;IACpC,uBAAuB;IACvB,4BAA4B;AAChC;;AAEA,mBAAmB;AACnB;;;IAGI,oCAAoC;IACpC,uBAAuB;IACvB,4BAA4B;AAChC;;AAEA,mBAAmB;AACnB;;;IAGI,oCAAoC;IACpC,uBAAuB;IACvB,4BAA4B;AAChC;;AAEA,mBAAmB;AACnB;;;IAGI,oCAAoC;IACpC,uBAAuB;IACvB,4BAA4B;AAChC;;AAEA,mBAAmB;AACnB;;;IAGI,oCAAoC;IACpC,uBAAuB;IACvB,4BAA4B;AAChC;;AAEA,mBAAmB;AACnB;;;IAGI,oCAAoC;IACpC,uBAAuB;IACvB,4BAA4B;AAChC;;AAEA,SAAS;AACT;;IAEI,uCAAuC;AAC3C;;AAEA;;IAEI,wCAAwC;AAC5C;;AAEA;;IAEI,sCAAsC;AAC1C;;AAEA;;IAEI,yCAAyC;AAC7C;;AAEA,QAAQ;AACR;;IAEI,gCAAgC;AACpC;;AAEA,UAAU;AACV;;IAEI,yBAAyB;IACzB,oCAAoC;IACpC,2CAA2C;IAC3C,yBAAyB;AAC7B;;AAEA,WAAW;AACX;;IAEI,6DAA6D;AACjE;;AAEA,cAAc;AACd;;IAEI,wDAAwD;AAC5D;;AAEA;;IAEI,4BAA4B;AAChC;;AAEA;;IAEI,yBAAyB;AAC7B;;AAEA,2DAA2D;AAC3D;IACI,iBAAiB;IACjB,gBAAgB;IAChB,sBAAsB;IACtB,aAAa;IACb,kBAAkB;AACtB;;AAEA,qDAAqD;AACrD;IACI,QAAQ;IACR,SAAS;AACb;;AAEA;IACI,yBAAyB;IACzB,YAAY;IACZ,YAAY;AAChB;;AAEA;IACI,yBAAyB;IACzB,YAAY;IACZ,YAAY;AAChB;;AAEA;IACI,yBAAyB;IACzB,YAAY;IACZ,YAAY;AAChB;;AAEA;IACI,yBAAyB;IACzB,YAAY;IACZ,YAAY;AAChB;;AAEA;IACI,QAAQ;IACR,SAAS;AACb;;AAEA;IACI,SAAS;IACT,UAAU;IACV,oDAAoD;AACxD;;AAEA;IACI,YAAY;IACZ,iBAAiB;IACjB,YAAY;IACZ,cAAc;IACd,iBAAiB;IACjB,sBAAsB;IACtB,wBAAwB;AAC5B;;AAEA;IACI,aAAa;IACb,sBAAsB;IACtB,8BAA8B;IAC9B,YAAY;AAChB;;AAEA;IACI,WAAW;IACX,mBAAmB;IACnB,sBAAsB;AAC1B;;AAEA;IACI,yBAAyB;IACzB,YAAY;IACZ,aAAa;IACb,8BAA8B;IAC9B,mBAAmB;IACnB,iBAAiB;IACjB,YAAY;AAChB;;AAEA;IACI,eAAe;IACf,iBAAiB;IACjB,kBAAkB;IAClB,YAAY;AAChB;;AAEA;IACI,WAAW;IACX,YAAY;AAChB;;AAEA;IACI,uBAAuB;IACvB,iBAAiB;IACjB,kBAAkB;IAClB,eAAe;IACf,WAAW;IACX,kBAAkB;AACtB;;AAEA;IACI,WAAW;IACX,yBAAyB;AAC7B;;AAEA;IACI,sBAAsB;IACtB,YAAY;IACZ,kBAAkB;IAClB,YAAY;AAChB;;AAEA;IACI,yBAAyB;IACzB,YAAY;IACZ,UAAU;AACd;;AAEA;IACI,UAAU;IACV,uBAAuB;AAC3B;;AAEA;IACI,WAAW;IACX,yBAAyB;IACzB,gBAAgB;AACpB;;AAEA;IACI,sBAAsB;IACtB,YAAY;IACZ,kBAAkB;IAClB,YAAY;AAChB;;AAEA;IACI,yBAAyB;IACzB,YAAY;IACZ,YAAY;AAChB;;AAEA;IACI,YAAY;IACZ,uBAAuB;AAC3B;;AAEA;IACI,aAAa;IACb,WAAW;IACX,gBAAgB;AACpB;;AAEA;IACI,UAAU;AACd;;AAEA;IACI,UAAU;AACd;;AAEA;IACI,WAAW;IACX,yBAAyB;IACzB,YAAY;AAChB;;AAEA;IACI,sBAAsB;IACtB,YAAY;IACZ,YAAY;IACZ,sBAAsB;AAC1B;;AAEA;IACI,cAAc;AAClB;;AAEA;IACI,yBAAyB;IACzB,YAAY;IACZ,UAAU;IACV,kBAAkB;IAClB,iBAAiB;AACrB;;AAEA;IACI,UAAU;IACV,uBAAuB;AAC3B;;AAEA;IACI,yBAAyB;IACzB,YAAY;IACZ,UAAU;IACV,kBAAkB;IAClB,iBAAiB;AACrB;;AAEA;IACI,yBAAyB;IACzB,YAAY;IACZ,UAAU;IACV,kBAAkB;IAClB,iBAAiB;AACrB;;AAEA;IACI,YAAY;IACZ,aAAa;IACb,sBAAsB;AAC1B;;AAEA;IACI,yBAAyB;IACzB,YAAY;IACZ,sBAAsB;IACtB,YAAY;IACZ,YAAY;IACZ,gBAAgB;AACpB;;AAEA;IACI,yBAAyB;IACzB,YAAY;IACZ,kBAAkB;IAClB,YAAY;IACZ,iBAAiB;IACjB,sBAAsB;IACtB,SAAS;AACb;;AAEA;IACI,qBAAqB;IACrB,UAAU;IACV,SAAS;AACb;;AAEA;IACI,aAAa;IACb,aAAa;IACb,mBAAmB;AACvB;;AAEA,4BAA4B;;AAE5B;IACI,yBAAyB;IACzB,YAAY;IACZ,kBAAkB;IAClB,YAAY;IACZ,eAAe;IACf,gBAAgB;AACpB;;AAEA,SAAS;AACT;IACI;QACI,aAAa;IACjB;;IAEA;QACI,SAAS;QACT,UAAU;IACd;;IAEA;QACI,SAAS;QACT,YAAY;QACZ,gBAAgB;IACpB;AACJ;;AAEA,8DAA8D;AAC9D;IACI,mBAAmB;AACvB;;AAEA;IACI,sBAAsB;IACtB,kBAAkB;IAClB,mBAAmB;IACnB,aAAa;IACb,mBAAmB;AACvB;;AAEA;IACI,aAAa;IACb,8BAA8B;IAC9B,mBAAmB;IACnB,mBAAmB;AACvB;;AAEA;IACI,sBAAsB;IACtB,kBAAkB;IAClB,aAAa;IACb,cAAc;IACd,iBAAiB;AACrB;;AAEA;IACI,YAAY;IACZ,WAAW;IACX,kBAAkB;AACtB;;AAEA;IACI,aAAa;IACb,QAAQ;AACZ;;AAEA;IACI,qBAAqB;IACrB,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,eAAe;IACf,WAAW;IACX,6BAA6B;AACjC;;AAEA;IACI,kBAAkB;AACtB;;AAEA;IACI,mBAAmB;IACnB,uBAAuB;IACvB,aAAa;IACb,cAAc;AAClB;;AAEA,qDAAqD;AACrD,aAAa;AACb;IACI,aAAa;AACjB;;AAEA;IACI,WAAW;IACX,yBAAyB;IACzB,gBAAgB;IAChB,6BAA6B,EAAE,aAAa;AAChD;;AAEA,eAAe;AACf;IACI,6BAA6B;AACjC;;AAEA;;IAEI,uBAAuB;IACvB,oCAAoC;IACpC,iCAAiC;IACjC,2BAA2B;IAC3B,mBAAmB;AACvB;;AAEA,gBAAgB;AAChB;4DAC4D,WAAW,EAAE,EAAE,OAAO;AAClF;4DAC4D,WAAW,EAAE,EAAE,OAAO;AAClF;4DAC4D,WAAW,EAAE,EAAE,OAAO;AAClF;4DAC4D,YAAY,EAAE,gBAAgB,EAAE,EAAE,SAAS;AACvG;4DAC4D,YAAY,EAAE,EAAE,SAAS;AACrF;4DAC4D,WAAW,EAAE,EAAE,OAAO;AAClF;4DAC4D,YAAY,EAAE,EAAE,SAAS;AACrF;4DAC4D,YAAY,EAAE,EAAE,SAAS,EAAE,OAAO;AAC9F;4DAC4D,WAAW,EAAE;AACzE;4DAC4D,WAAW,EAAE;AACzE;4DAC4D,WAAW,EAAE;AACzE;4DAC4D,YAAY,EAAE;;AAE1E,WAAW;AACX;IACI,YAAY;IACZ,mBAAmB;IACnB,YAAY;IACZ,uBAAuB;IACvB,yCAAyC;AAC7C;;AAEA,WAAW;AACX;IACI,YAAY;IACZ,kBAAkB;IAClB,cAAc;IACd,sBAAsB;AAC1B;;AAEA,UAAU;;AAEV,WAAW;AACX;IACI,yBAAyB;AAC7B;;AAEA,WAAW;AACX;IACI,iCAAiC;AACrC;;AAEA;IACI;QACI,2BAA2B;QAC3B,UAAU;IACd;;IAEA;QACI,wBAAwB;QACxB,UAAU;IACd;AACJ;;AAEA,4BAA4B;AAC5B;IACI,gBAAgB;IAChB,UAAU;IACV,gBAAgB;AACpB;;AAEA,wBAAwB;AACxB;IACI,4BAA4B;IAC5B,YAAY;IACZ,yBAAyB;AAC7B;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,YAAY;IACZ,mBAAmB;IACnB,YAAY;IACZ,uBAAuB;AAC3B;;AAEA;IACI,yCAAyC;IACzC,sBAAsB;IACtB,YAAsB;IAAtB,sBAAsB;AAC1B;;AAEA,aAAa;AACb;IACI,cAAc;IACd,YAAY;AAChB;;AAEA;IACI,cAAc;AAClB;;AAEA,SAAS;AACT;IACI,oCAAoC;IACpC,wEAAwE;IACxE,kBAAkB;AACtB;;AAEA,aAAa;AACb;IACI,aAAa;AACjB;;AAEA;IACI,mBAAmB;IACnB,aAAa;IACb,mBAAmB;IACnB,kBAAkB;AACtB;;AAEA;IACI,mBAAmB;IACnB,sBAAsB;IACtB,kBAAkB;IAClB,gBAAgB;AACpB;;AAEA;IACI,mBAAmB;IACnB,YAAY;IACZ,kBAAkB;IAClB,iBAAiB;IACjB,eAAe;AACnB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,gBAAgB;IAChB,UAAU;IACV,gBAAgB;AACpB;;AAEA;IACI,YAAY;IACZ,yBAAyB;AAC7B;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,YAAY;IACZ,yBAAyB;IACzB,sBAAsB;IACtB,gBAAgB;AACpB;;AAEA;IACI,mBAAmB;IACnB,YAAY;IACZ,iBAAiB;AACrB;;AAEA;IACI,iBAAiB;IACjB,yBAAyB;IACzB,kBAAkB;AACtB;;AAEA;IACI,wCAAwC;IACxC,sBAAsB;IACtB,YAAsB;IAAtB,sBAAsB;AAC1B;;AAEA;IACI,uBAAuB;IACvB,YAAY;AAChB;;AAEA;IACI,0BAA0B;IAC1B,kCAAkC;IAClC,YAAY;IACZ,aAAa;IACb,kBAAkB;IAClB,kBAAkB;AACtB;;AAEA;IACI,gBAAgB;IAChB,kBAAkB;IAClB,QAAQ;IACR,SAAS;IACT,gCAAgC;IAChC,cAAc;IACd,iBAAiB;AACrB;;AAEA;IACI,mCAAmC;IACnC,0BAA0B;IAC1B,kBAAkB;AACtB;;AAEA;IACI,6BAA6B;AACjC;;AAEA;IACI,cAAc;AAClB;;AAEA,UAAU;;AAEV;IACI,yBAAyB;AAC7B;;AAEA;IACI;QACI,2BAA2B;QAC3B,UAAU;IACd;;IAEA;QACI,wBAAwB;QACxB,UAAU;IACd;AACJ;;AAEA;IACI,kBAAkB;IAClB,WAAW;IACX,YAAY;AAChB;;AAEA;IACI,cAAc;IACd,eAAe;AACnB;;AAEA;IACI,cAAc;AAClB;;AAEA;IACI,kBAAkB;IAClB,WAAW;AACf;;AAEA;IACI,kBAAkB;IAClB,WAAW;AACf;;AAEA;IACI,gBAAgB;IAChB,gBAAgB;AACpB;;AAEA;IACI,gBAAgB;AACpB;;AAEA;IACI,mBAAmB;IACnB,YAAY;IACZ,gBAAgB;IAChB,mBAAmB;IACnB,gBAAgB;IAChB,qBAAqB;AACzB;;AAEA;IACI,cAAc;IACd,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,uBAAuB;IACvB,mBAAmB;AACvB;;AAEA;IACI,kBAAkB;IAClB,WAAW;AACf;;AAEA;IACI,mBAAmB;IACnB,YAAY;IACZ,gBAAgB;IAChB,mBAAmB;IACnB,gBAAgB;IAChB,qBAAqB;AACzB;;AAEA;IACI,kBAAkB;IAClB,WAAW;AACf;;AAEA;IACI,kBAAkB;IAClB,YAAY;AAChB;;AAEA;IACI,gBAAgB;IAChB,mBAAmB;IACnB,gBAAgB;IAChB,qBAAqB;AACzB;;AAEA;IACI,mBAAmB;IACnB,YAAY;AAChB;;AAEA;IACI,mBAAmB;IACnB,YAAY;AAChB;;AAEA;IACI,mBAAmB;IACnB,YAAY;AAChB;;AAEA;IACI,mBAAmB;IACnB,YAAY;AAChB;;AAEA;IACI,cAAc;IACd,aAAa;IACb,mBAAmB;IACnB,kBAAkB;AACtB;;AAEA;IACI,eAAe;IACf,MAAM;IACN,aAAa;IACb,YAAY;IACZ,aAAa;IACb,iBAAiB;IACjB,0CAA0C;IAC1C,2BAA2B;IAC3B,aAAyB;IAAzB,yBAAyB;IACzB,gBAAgB;AACpB;;AAEA;IACI,QAAQ;AACZ;;AAEA;IACI,aAAa;IACb,mBAAmB;IACnB,YAAY;IACZ,iBAAiB;AACrB;;AAEA;IACI,aAAa;AACjB;;AAEA;IACI,eAAe;IACf,MAAM;IACN,OAAO;IACP,WAAW;IACX,YAAY;IACZ,8BAA8B;IAC9B,aAAa;IACb,uBAAuB;IACvB,mBAAmB;IACnB,aAAiC;IAAjC,iCAAiC;AACrC;;AAEA;IACI,iBAAiB;IACjB,aAAa;IACb,kBAAkB;IAClB,kBAAkB;AACtB;;AAEA;IACI,cAAc;IACd,kBAAkB;IAClB,QAAQ;IACR,UAAU;IACV,eAAe;IACf,iCAAiC;AACrC;;AAEA;IACI,wEAAwE;IACxE,yCAAyC;AAC7C;;AAEA;IACI,YAAY;IACZ,8BAA8B;IAC9B,kBAAkB;AACtB;;AAEA;IACI,YAAY;IACZ,0BAA0B;IAC1B,2BAA2B;AAC/B;;AAEA;IACI,cAAc;IACd,kBAAkB;IAClB,SAAS;IACT,WAAW;IACX,eAAe;IACf,iCAAiC;AACrC;;AAEA,0DAA0D;AAC1D;IACI,iBAAiB;IACjB,cAAc;IACd,aAAa;AACjB;;AAEA;IACI,mBAAmB;IACnB,aAAa;IACb,mBAAmB;IACnB,kBAAkB;IAClB,8BAA8B;AAClC;;AAEA;IACI,cAAc;IACd,mBAAmB;AACvB;;AAEA;IACI,gBAAgB;IAChB,UAAU;AACd;;AAEA;IACI,cAAc;IACd,gCAAgC;AACpC;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,cAAc;IACd,kBAAkB;AACtB;;AAEA;IACI,qBAAqB;IACrB,WAAW;IACX,YAAY;IACZ,mBAAmB;IACnB,YAAY;IACZ,kBAAkB;IAClB,kBAAkB;IAClB,iBAAiB;IACjB,kBAAkB;IAClB,iBAAiB;AACrB;;AAEA;IACI,eAAe;IACf,yBAAyB;IACzB,kBAAkB;IAClB,cAAc;AAClB;;AAEA;IACI,mBAAmB;IACnB,yBAAyB;IACzB,kBAAkB;IAClB,aAAa;IACb,cAAc;AAClB;;AAEA;IACI,mBAAmB;IACnB,yBAAyB;IACzB,kBAAkB;IAClB,aAAa;IACb,cAAc;AAClB;;AAEA,4DAA4D;AAC5D;IACI,aAAa;IACb,gBAAgB;AACpB;;AAEA,cAAc;AACd;IACI,mBAAmB;IACnB,yBAAyB;IACzB,mCAAmC;AACvC;;AAEA,WAAW;AACX;IACI,gBAAgB;IAChB,gBAAgB;AACpB;;AAEA;IACI,WAAW;IACX,iBAAiB;IACjB,yBAAyB;IACzB,iBAAiB;IACjB,wCAAwC;AAC5C;;AAEA;;IAEI,yBAAyB;IACzB,YAAY;IACZ,kBAAkB;IAClB,sBAAsB;AAC1B;;AAEA;IACI,mBAAmB;IACnB,YAAY;IACZ,iBAAiB;IACjB,gBAAgB;IAChB,MAAM;IACN,iCAAiC;IACjC,yCAAyC;IACzC,eAAe;AACnB;;AAEA;IACI,mBAAmB;IACnB,iBAAiB;IACjB,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,OAAO;IACP,gCAAgC;IAChC,+BAA+B;IAC/B,WAAW;AACf;;AAEA;IACI,mBAAmB;IACnB,YAAY;IACZ,iBAAiB;IACjB,kBAAkB;IAClB,yCAAyC;IACzC,eAAe;AACnB;;AAEA;IACI,qBAAqB;IACrB,eAAe;IACf,WAAW;AACf;;AAEA;IACI,kBAAkB;IAClB,gBAAgB;IAChB,kBAAkB;IAClB,sBAAsB;IACtB,iBAAiB;IACjB,yBAAyB;IACzB,yBAAyB;IACzB,gBAAgB;AACpB;;AAEA;IACI,yCAAyC;IACzC,2BAA2B;AAC/B;;AAEA;IACI,aAAa;IACb,sBAAsB;IACtB,QAAQ;IACR,YAAY;AAChB;;AAEA;IACI,aAAa;IACb,mBAAmB;IACnB,uBAAuB;IACvB,QAAQ;AACZ;;AAEA;IACI,gBAAgB;IAChB,gBAAgB;IAChB,WAAW;IACX,+CAA+C;IAC/C,gBAAgB;AACpB;;AAEA;IACI,mBAAmB;IACnB,8BAA8B;AAClC;;AAEA;IACI,mBAAmB;IACnB,8BAA8B;AAClC;;AAEA;IACI,mBAAmB;IACnB,+BAA+B;AACnC;;AAEA;IACI,mBAAmB;IACnB,+BAA+B;AACnC;;AAEA;IACI,mBAAmB;IACnB,yBAAyB;IACzB,YAAY;AAChB;;AAEA;IACI,mBAAmB;IACnB,8BAA8B;IAC9B,+BAA+B;AACnC;;AAEA;IACI,mBAAmB;IACnB,8BAA8B;IAC9B,+BAA+B;AACnC;;AAEA;IACI,gBAAgB;IAChB,kBAAkB;IAClB,iBAAiB;AACrB;;AAEA;IACI,cAAc;IACd,aAAa;IACb,mBAAmB;IACnB,kBAAkB;AACtB;;AAEA;IACI,cAAc;IACd,aAAa;IACb,mBAAmB;IACnB,kBAAkB;AACtB;;AAEA;IACI,qBAAqB;IACrB,sBAAsB;AAC1B;;AAEA;IACI,qBAAqB;IACrB,WAAW;IACX,YAAY;IACZ,iBAAiB;IACjB,sBAAsB;IACtB,sBAAsB;AAC1B;;AAEA;IACI,mBAAmB;AACvB;;AAEA;;;;;qCAKqC;;AAErC;IACI,gBAAgB;AACpB;;AAEA;IACI,gBAAgB;IAChB,gBAAgB;IAChB,iBAAiB;AACrB;;AAEA;IACI,gBAAgB;IAChB,YAAY;IACZ,mBAAmB;AACvB;;AAEA,gDAAgD;;AAEhD;IACI,yBAAyB;IACzB,cAAc;AAClB;;AAEA;IACI,yBAAyB;IACzB,YAAY;IACZ,gBAAgB;AACpB;;AAEA,yDAAyD;AACzD;;IAEI,iCAAiC;AACrC;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,iBAAiB;AACrB;;AAEA;IACI,eAAe;AACnB;;AAEA;IACI,iBAAiB;IACjB,cAAc;AAClB;;AAEA,4DAA4D;;AAE5D;IACI,mBAAmB;AACvB;;AAEA;IACI,aAAa;IACb,8BAA8B;IAC9B,mBAAmB;AACvB;;AAEA;IACI,SAAS;AACb;;AAEA;IACI,iBAAiB;IACjB,gBAAgB;AACpB;;AAEA,0DAA0D;;AAE1D;IACI,yBAAyB;IACzB,cAAc;AAClB;;AAEA,aAAa;AACb;IACI,aAAa;IACb,eAAe;AACnB;;AAEA,YAAY;AACZ;IACI,kBAAkB;IAClB,sBAAsB;IACtB,sBAAsB;AAC1B;;AAEA;IACI,yBAAyB;IACzB,YAAY;AAChB;;AAEA;IACI,yBAAyB;IACzB,YAAY;AAChB;;AAEA;IACI,yBAAyB;IACzB,YAAY;AAChB;;AAEA;IACI,cAAc;IACd,iBAAiB;AACrB;;AAEA;IACI,cAAc;AAClB;;AAEA;IACI,cAAc;AAClB;;AAEA;IACI,mBAAmB;IACnB,qBAAqB;AACzB;;AAEA,aAAa;AACb;IACI,mBAAmB;AACvB;;AAEA;IACI,cAAc;IACd,6BAAqB;IAArB,qBAAqB;AACzB;;AAEA,YAAY;AACZ;IACI,YAAY;IACZ,gBAAgB;AACpB;;AAEA;IACI,YAAY;IACZ,iBAAiB;IACjB,sBAAsB;AAC1B;;AAEA;IACI,iBAAiB;IACjB,kBAAkB;AACtB;;AAEA;IACI,gBAAgB;IAChB,gBAAgB;IAChB,WAAW;IACX,mBAAmB;AACvB;;AAEA;IACI,iBAAiB;IACjB,cAAc;AAClB;;AAEA;IACI,gBAAgB;IAChB,WAAW;IACX,kBAAkB;AACtB;;AAEA;IACI,iBAAiB;IACjB,cAAc;AAClB;;AAEA;IACI,gBAAgB;IAChB,kBAAkB;AACtB;;AAEA;IACI,yBAAyB;IACzB,qBAAqB;AACzB;;AAEA;IACI,yBAAyB;IACzB,qBAAqB;AACzB;;AAEA;IACI,yBAAyB;IACzB,kBAAkB;AACtB;;AAEA;IACI,yBAAyB;IACzB,qBAAqB;AACzB;;AAEA;IACI,sBAAsB;IACtB,eAAe;AACnB;;AAEA;IACI,eAAe;AACnB;;AAEA;;IAEI,qBAAqB;IACrB,UAAU;IACV,gFAAgF;AACpF;;AAEA,4CAA4C;;AAE5C,aAAa;AACb;IACI,oCAAoC;AACxC;;AAEA;;IAEI,oCAAoC;AACxC;;AAEA,yBAAyB;AACzB;;IAEI,2CAA2C;IAC3C,oCAAoC;IACpC,2BAA2B;IAC3B,6BAA6B;AACjC;;AAEA,qCAAqC;;AAErC,cAAc;AACd;;;;;;;;;;;;0BAY0B,UAAU;IAChC,6BAA6B;AACjC;;AAEA,aAAa;AACb;;;;;;;0BAO0B,QAAQ;IAC9B,2BAA2B;AAC/B;;AAEA,oBAAoB;AACpB,0BAA0B,8BAA8B;IACpD,6BAA6B;AACjC;;AAEA,0BAA0B;AAC1B,0BAA0B,SAAS;IAC/B,2BAA2B;AAC/B;;AAEA,uBAAuB;AACvB;IACI,6BAA6B,EAAE,gBAAgB;AACnD;;AAEA,gBAAgB;AAChB;IACI,6BAA6B;AACjC;;AAEA,kBAAkB;AAClB;;;;;;;;;;;;;;;;;IAiBI,6BAA6B;AACjC;;AAEA,gDAAgD;;AAEhD,mBAAmB;AACnB;IACI,6BAA6B;AACjC;;AAEA,eAAe;AACf;IACI,6BAA6B,EAAE,cAAc;AACjD;;AAEA;IACI,6BAA6B,EAAE,cAAc;AACjD;;AAEA,eAAe;AACf,sBAAsB;AACtB;IACI,6BAA6B,EAAE,SAAS;AAC5C;;AAEA,2BAA2B;AAC3B;IACI,6BAA6B,EAAE,SAAS;AAC5C;AACA;IACI,2BAA2B,EAAE,UAAU;AAC3C;;AAEA,kCAAkC;AAClC;;IAEI,6BAA6B,EAAE,SAAS;AAC5C;AACA;IACI,2BAA2B,EAAE,UAAU;AAC3C;;AAEA,wCAAwC;AACxC;;;IAGI,6BAA6B,EAAE,SAAS;AAC5C;AACA;IACI,2BAA2B,EAAE,UAAU;AAC3C;;AAEA,2BAA2B;AAC3B;IACI,6BAA6B,EAAE,YAAY;AAC/C;AACA;IACI,6BAA6B,EAAE,YAAY;AAC/C;AACA;IACI,2BAA2B,EAAE,oBAAoB;AACrD;AACA;IACI,2BAA2B,EAAE,aAAa;AAC9C;;AAEA,iBAAiB;AACjB;IACI,4BAA4B;IAC5B,yBAAyB;IACzB,6BAA6B;IAC7B,yBAAyB;AAC7B;;AAEA;IACI,6BAA6B;IAC7B,yBAAyB;AAC7B;;AAEA;IACI,0BAA0B;IAC1B,yBAAyB;AAC7B;;AAEA;IACI,0BAA0B;AAC9B;;AAEA,kBAAkB;AAClB;IACI,8BAA8B;AAClC;;AAEA;IACI,2BAA2B;AAC/B;;AAEA;IACI,8BAA8B;AAClC;;AAEA;IACI,qBAAqB;AACzB;;AAEA;IACI,qBAAqB;AACzB;;AAEA;IACI,2BAA2B;AAC/B;;AAEA;IACI,2BAA2B;AAC/B;;AAEA;IACI,2BAA2B;AAC/B;;AAEA,WAAW;AACX;IACI,yBAAyB;AAC7B;;AAEA;IACI,yBAAyB;AAC7B;;AAEA,SAAS;AACT;IACI,eAAe;IACf,kBAAkB;AACtB;;AAEA;IACI,gBAAgB;IAChB,wBAAwB;IACxB,gBAAgB;IAChB,WAAW;AACf;;AAEA;IACI,gBAAgB;IAChB,WAAW;AACf;;AAEA;IACI,gBAAgB;IAChB,WAAW;AACf;;AAEA,2BAA2B;AAC3B;IACI,wBAAmC;IAAnC,mCAAmC;AACvC;;AAEA,eAAe;AACf;IACI,wBAAuC;IAAvC,uCAAuC;AAC3C;;AAEA,gBAAgB;AAChB;IACI,8BAA8B;AAClC;;AAEA,eAAe;AACf;IACI,qDAAqD;IACrD,+CAA+C;IAC/C,6BAA6B;AACjC;;AAEA,8BAA8B;AAC9B;IACI,6BAA6B;IAC7B,wBAAmC;IAAnC,mCAAmC;AACvC;;AAEA,eAAe;AACf;IACI,uBAAuB;AAC3B;;AAEA,kBAAkB;AAClB;IACI,yBAAyB;AAC7B;;AAEA,iCAAiC;AACjC;IACI,wBAA0C;IAA1C,0CAA0C;AAC9C;;AAEA,wDAAwD;AACxD;IACI,wBAAwB;IACxB,0BAA0B;IAC1B,gBAAgB;IAChB,kBAAkB;IAClB,cAAc;IACd,WAAW;AACf;;AAEA;IACI,uBAAuB;IACvB,gCAAgC;IAChC,sBAAsB;IACtB,kBAAkB;IAClB,gBAAgB;IAChB,0BAA0B;IAC1B,yBAAyB;IACzB,uBAAuB;IACvB,gCAAgC;IAChC,YAAY;AAChB;;AAEA;IACI,2BAA2B;IAC3B,yCAAyC;AAC7C;;AAEA;IACI,wBAAwB;IACxB,uBAAuB;IACvB,gBAAgB;IAChB,wBAAwB;IACxB,mBAAmB;IACnB,uBAAuB;IACvB,yBAAyB;AAC7B;;AAEA;IACI,gBAAgB;IAChB,iBAAiB;IACjB,sBAAmB;OAAnB,mBAAmB;AACvB;;AAEA;IACI,YAAY;IACZ,uBAAuB;IACvB,iCAAiC;AACrC;;AAEA;IACI,iBAAiB;IACjB,kBAAkB;IAClB,mBAAmB;IACnB,gBAAgB;IAChB,uBAAuB;IACvB,eAAe;AACnB;;AAEA;IACI,qBAAqB;IACrB,gBAAgB;IAChB,kBAAkB;IAClB,eAAe;IACf,kBAAkB;AACtB;;AAEA;IACI,yBAAyB;IACzB,cAAc;AAClB;;AAEA;IACI,yBAAyB;IACzB,cAAc;AAClB;;AAEA;IACI,yBAAyB;IACzB,cAAc;AAClB;;AAEA;IACI,eAAe;IACf,aAAa;IACb,8BAA8B;AAClC;;AAEA;IACI,gBAAgB;IAChB,eAAe;AACnB;;AAEA;IACI,gBAAgB;IAChB,kBAAkB;AACtB;;AAEA,kBAAkB;;AAElB,yDAAyD;AACzD,SAAS;AACT;IACI,mBAAmB;AACvB;;AAEA,WAAW;AACX;IACI,aAAa;IACb,eAAe;IACf,QAAQ;IACR,gBAAgB;IAChB,iBAAiB;IACjB,mBAAmB;IACnB,gBAAgB;IAChB,kBAAkB;IAClB,oBAAoB;AACxB;;AAEA;IACI,eAAe;IACf,eAAe;IACf,mBAAmB;IACnB,gBAAgB;IAChB,uBAAuB;IACvB,WAAW;IACX,gBAAgB;IAChB,SAAS;IACT,gBAAgB;IAChB,cAAc;IACd,SAAS;AACb;;AAEA,UAAU;AACV;IACI,SAAS;IACT,UAAU;AACd;;AAEA;IACI,SAAS;IACT,UAAU;IACV,qBAAqB;IACrB,UAAU;IACV,kBAAkB;IAClB,QAAQ;IACR,SAAS;AACb;;AAEA,aAAa;AACb;IACI,aAAa;IACb,eAAe;IACf,SAAS;IACT,gBAAgB;IAChB,mBAAmB;IACnB,2BAA2B;AAC/B;;AAEA,SAAS;AACT;IACI,mBAAmB;AACvB;;AAEA;IACI,WAAW;IACX,yBAAyB;AAC7B;;AAEA;;IAEI,sBAAsB;IACtB,YAAY;IACZ,gBAAgB;AACpB;;AAEA;IACI,yBAAyB;AAC7B;;AAEA;IACI,0CAA0C;IAC1C,sBAAsB;AAC1B;;AAEA;IACI,eAAe;IACf,aAAa;IACb,YAAY;IACZ,sBAAmB;OAAnB,mBAAmB;IACnB,cAAc;IACd,cAAc;IACd,eAAe;AACnB;;;;AAIA;IACI,cAAc;IACd,eAAe;IACf,WAAW;IACX,YAAY;IACZ,UAAU;IACV,kBAAkB;AACtB;;;;AAIA;IACI,yBAAyB;IACzB,0CAA0C;AAC9C;;AAEA;IACI,gBAAgB;IAChB,iBAAiB;IACjB,6BAA6B;IAC7B,mBAAmB;AACvB;;AAEA;IACI,YAAY;IACZ,sBAAsB;IACtB,kBAAkB;IAClB,YAAY;IACZ,kBAAkB;AACtB;;AAEA;IACI,eAAe;IACf,iBAAiB;IACjB,YAAY;IACZ,aAAa;IACb,sBAAmB;OAAnB,mBAAmB;AACvB;;AAEA;IACI,eAAe;IACf,eAAe;IACf,mBAAmB;IACnB,gBAAgB;IAChB,uBAAuB;IACvB,gBAAgB;IAChB,gBAAgB;AACpB;;AAEA,gBAAgB;AAChB;IACI,yBAAyB;IACzB,0CAA0C;IAC1C,yBAAyB;AAC7B;;AAEA,eAAe;AACf;;;IAGI,aAAa;IACb,eAAe;IACf,2BAA2B;IAC3B,uBAAuB;IACvB,SAAS;IACT,aAAa;AACjB;;AAEA,UAAU;AACV;IACI,sBAAsB;IACtB,kBAAkB;IAClB,YAAY;IACZ,mBAAmB;IACnB,sBAAsB;IACtB,yBAAyB;AAC7B;;AAEA;IACI,sCAAsC;AAC1C;;AAEA;IACI,eAAe;IACf,kBAAkB;IAClB,eAAe;IACf,sBAAsB;AAC1B;;AAEA,cAAc;AACd;IACI,iBAAiB;AACrB;;AAEA,aAAa;AACb;IACI,aAAa;IACb,eAAe;IACf,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,mBAAmB;IACnB,kBAAkB;IAClB,YAAY;AAChB;;AAEA;IACI,eAAe;IACf,eAAe;IACf,sBAAsB;AAC1B;;AAEA,2DAA2D;;IAEvD,kEAAkE;IAClE,kBAAkB;IAClB;QACI,2BAA2B;QAC3B,iCAAiC;QACjC,6BAA6B;QAC7B,wBAAwB;QACxB,oCAAoC;QACpC,4BAA4B;QAC5B,4BAA4B;QAC5B,2BAA2B;QAC3B,yBAAyB;IAC7B;;IAEA,4DAA4D;IAC5D;QACI,yBAAyB;QACzB,aAAa;QACb,kBAAkB;QAClB,mBAAmB;IACvB;;IAEA,iEAAiE;IACjE;QACI,yBAAyB;QACzB,aAAa;QACb,kBAAkB;QAClB,mBAAmB;IACvB;;IAEA,+DAA+D;IAC/D;QACI,yBAAyB;QACzB,aAAa;QACb,kBAAkB;QAClB,mBAAmB;IACvB;;IAEA;QACI,gBAAgB;IACpB;;IAEA;QACI,eAAe;QACf,gBAAgB;IACpB;;IAEA,+DAA+D;IAC/D;QACI,aAAa;QACb,gBAAgB;IACpB;;IAEA;QACI,WAAW;QACX,iBAAiB;QACjB,yBAAyB;QACzB,iBAAiB;QACjB,wCAAwC;QACxC,mBAAmB;IACvB;;IAEA;QACI,gBAAgB;QAChB,YAAY;IAChB;;IAEA,aAAa;IACb;QACI,gCAAgC;QAChC,mBAAmB;IACvB;;IAEA;QACI,mBAAmB;IACvB;;IAEA;QACI,mBAAmB;IACvB;;IAEA,WAAW;IACX;QACI,gCAAgC;QAChC,mBAAmB;IACvB;;IAEA;QACI,mBAAmB;IACvB;;IAEA,WAAW;IACX;QACI,gBAAgB;IACpB;;IAEA;QACI,mBAAmB;IACvB;;IAEA;QACI,mBAAmB;IACvB;;IAEA,UAAU;IACV;QACI,mBAAmB;QACnB,iBAAiB;QACjB,cAAc;QACd,gBAAgB;QAChB,aAAa;IACjB;;IAEA;QACI,mBAAmB;QACnB,iBAAiB;QACjB,cAAc;QACd,gBAAgB;QAChB,aAAa;IACjB;;IAEA;QACI,oCAAoC;QACpC,gCAAgC;IACpC;;IAEA;QACI,oCAAoC;QACpC,gCAAgC;IACpC;;IAEA;;;;;yCAKqC;;IAErC;QACI,cAAc;QACd,0BAA0B;IAC9B;;IAEA,+CAA+C;IAC/C;QACI,iBAAiB;QACjB,gBAAgB;QAChB,sBAAsB;QACtB,aAAa;QACb,kBAAkB;IACtB;;IAEA;QACI,kBAAkB;IACtB;;IAEA;QACI,iBAAiB;IACrB;;IAEA,iEAAiE;IACjE;QACI,8BAA8B;QAC9B,gBAAgB;QAChB,WAAW;QACX,gBAAgB;QAChB,cAAc;QACd,aAAa;IACjB;;IAEA;QACI,kBAAkB;QAClB,mBAAmB;IACvB;;IAEA;QACI,cAAc;QACd,mBAAmB;IACvB;;IAEA;QACI,mBAAmB;IACvB;;IAEA;QACI,yBAAyB;QACzB,aAAa;QACb,kBAAkB;QAClB,mBAAmB;IACvB;;IAEA;QACI,mBAAmB;IACvB;;IAEA;QACI,mBAAmB;QACnB,oBAAoB;QACpB,6BAA6B;IACjC;;IAEA;QACI,qBAAqB;QACrB,gBAAgB;QAChB,kBAAkB;QAClB,eAAe;QACf,kBAAkB;IACtB;;IAEA;QACI,yBAAyB;QACzB,YAAY;IAChB;;IAEA;QACI,yBAAyB;QACzB,YAAY;IAChB;;IAEA;QACI,yBAAyB;QACzB,YAAY;IAChB;;IAEA;QACI,gBAAgB;QAChB,kBAAkB;QAClB,eAAe;QACf,WAAW;IACf;;IAEA,qDAAqD;IACrD;QACI,cAAc;QACd,cAAc;IAClB;;IAEA;QACI,6BAAqB;QAArB,qBAAqB;IACzB;;IAEA;QACI,6BAAqB;QAArB,qBAAqB;IACzB;;IAEA;QACI,0BAA0B;IAC9B;;IAEA;QACI,wBAAwB;IAC5B;;IAEA;QACI,WAAW;QACX,mBAAmB;IACvB;;IAEA;QACI,gBAAgB;QAChB,sBAAsB;QACtB,kBAAkB;QAClB,aAAa;QACb,yBAAyB;IAC7B;;IAEA;QACI,mBAAmB;IACvB;;IAEA;QACI,mBAAmB;QACnB,cAAc;QACd,aAAa;QACb,sBAAsB;QACtB,sBAAsB;QACtB,kBAAkB;IACtB;;IAEA,8DAA8D;;IAE9D;QACI,mBAAmB;IACvB;;IAEA;QACI,SAAS;IACb;;IAEA;QACI,mBAAmB;QACnB,cAAc;QACd,aAAa;QACb,yBAAyB;QACzB,sBAAsB;QACtB,kBAAkB;IACtB;;IAEA;QACI,yBAAyB;IAC7B;;IAEA;QACI,mBAAmB;QACnB,oBAAoB;QACpB,6BAA6B;IACjC;;IAEA,4DAA4D;IAC5D;QACI,iBAAiB;IACrB;;IAEA,oDAAoD;;IAEpD,wDAAwD;IACxD,oBAAoB;IACpB;QACI,mBAAmB;QACnB,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,mBAAmB;QACnB,6CAA6C;IACjD;;IAEA,qDAAqD;IACrD;QACI,mBAAmB;QACnB,sBAAsB;QACtB,kBAAkB;QAClB,gBAAgB;QAChB,yBAAyB;IAC7B;;IAEA;QACI,yCAAyC;QACzC,2BAA2B;IAC/B;;IAEA;QACI,yBAAyB;QACzB,aAAa;QACb,6BAA6B;IACjC;;IAEA;QACI,SAAS;QACT,eAAe;QACf,iBAAiB;IACrB;;IAEA;QACI,eAAe;QACf,WAAW;IACf;;IAEA;QACI,aAAa;QACb,sBAAsB;QACtB,kBAAkB;IACtB;;IAEA;QACI,eAAe;QACf,YAAY;QACZ,sBAAsB;IAC1B;;IAEA;QACI,aAAa;QACb,yBAAyB;QACzB,kBAAkB;IACtB;;IAEA;QACI,iBAAiB;IACrB;;IAEA,8CAA8C;;IAE9C,0DAA0D;IAC1D;QACI,wBAAmC;QAAnC,mCAAmC;QACnC,0BAA0B;QAC1B,4BAA4B;QAC5B,2BAA2B;QAC3B,sBAAsB;QACtB,2BAA2B;IAC/B;;IAEA;QACI,uBAAuB;QACvB,6BAA6B;QAC7B,2CAA2C;IAC/C;;IAEA;QACI,6BAA6B;QAC7B,2CAA2C;QAC3C,sBAAsB;QACtB,oBAAoB;QACpB,4BAA4B;QAC5B,iCAAiC;QACjC,6BAA6B;QAC7B,0DAA0D;QAC1D,iFAAiF;IACrF,C", "sources": ["webpack://lab-safety-frontend/./src/css/app.css"], "sourcesContent": ["/**\r\n * 主应用样式文件\r\n * 包含全局样式和组件样式\r\n * 备份时间: 2025.1.6 - 删除重复样式前备份\r\n */\r\n\r\n/* ========== 全局 Z-INDEX 层级规范 ========== */\r\n/*\r\n * 统一的z-index层级管理，避免层级冲突\r\n *\r\n * 层级分配：\r\n * 1-99:     基础元素（工具提示、下拉菜单等）\r\n * 100-199:  浮动元素（固定导航、侧边栏等）\r\n * 200-299:  弹出层（下拉菜单、选择器等）\r\n * 300-399:  覆盖层（加载遮罩、通知等）\r\n * 400-499:  模态框（对话框、弹窗等）\r\n * 500-999:  特殊用途（调试、开发工具等）\r\n * 1000+:    紧急修复（临时使用，需要重构）\r\n */\r\n\r\n:root {\r\n    /* 基础层级 */\r\n    --z-tooltip: 10;\r\n    --z-dropdown: 20;\r\n    --z-sticky: 30;\r\n\r\n    /* 浮动层级 */\r\n    --z-sidebar: 100;\r\n    --z-navbar: 110;\r\n    --z-floating: 120;\r\n\r\n    /* 弹出层级 */\r\n    --z-popover: 200;\r\n    --z-dropdown-menu: 1000;\r\n    --z-select: 1010;\r\n\r\n    /* 覆盖层级 */\r\n    --z-overlay: 300;\r\n    --z-loading: 310;\r\n    --z-notification: 320;\r\n\r\n    /* 模态框层级 */\r\n    --z-modal-backdrop: 400;\r\n    --z-modal: 410;\r\n    --z-modal-content: 420;\r\n\r\n    /* 特殊层级 */\r\n    --z-drag: 999;\r\n    --z-datepicker: 1000;\r\n    --z-preview: 1000;\r\n    --z-loading-overlay: 2000;\r\n    --z-debug: 9000;\r\n    --z-emergency: 9999;\r\n}\r\n\r\n/* 全局样式重置 */\r\n* {\r\n    box-sizing: border-box;\r\n}\r\n\r\n/* ========== 点击展开操作按钮样式 ========== */\r\n.hover-action-container {\r\n    display: block; /* 改为块级元素，占满单元格宽度 */\r\n    width: 100%;\r\n}\r\n\r\n/* 默认显示的触发按钮 */\r\n.action-trigger {\r\n    display: block;\r\n}\r\n\r\n.action-trigger .btn {\r\n    padding: 4px 8px !important;\r\n    font-size: 12px !important;\r\n    line-height: 1.3 !important;\r\n    min-height: 26px !important;\r\n    cursor: pointer;\r\n}\r\n\r\n/* 点击时显示的具体按钮 */\r\n.action-buttons {\r\n    display: none;\r\n    position: static; /* 改为静态定位，保持在单元格内 */\r\n    white-space: normal; /* 允许换行 */\r\n    background: rgba(248, 249, 250, 0.9);\r\n    padding: 3px;\r\n    border-radius: 3px;\r\n    border: 1px solid #ddd;\r\n    margin: 2px auto 0 auto; /* 上边距2px，左右自动居中 */\r\n    width: fit-content; /* 宽度自适应内容 */\r\n    min-width: auto; /* 最小宽度自动 */\r\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n    text-align: center; /* 内容居中对齐 */\r\n}\r\n\r\n.action-buttons .btn {\r\n    margin-right: 2px;\r\n    margin-bottom: 1px;\r\n    padding: 3px 6px !important;\r\n    font-size: 11px !important;\r\n    line-height: 1.2 !important;\r\n    min-height: 24px !important;\r\n}\r\n\r\n/* 点击展开的显示逻辑 */\r\n.hover-action-container.expanded .action-trigger {\r\n    display: none;\r\n}\r\n\r\n.hover-action-container.expanded .action-buttons {\r\n    display: block !important; /* 改为块级显示，允许按钮换行 */\r\n}\r\n\r\n/* 按钮在容器内的布局 */\r\n.action-buttons .btn {\r\n    margin: 1px; /* 按钮之间的间距 */\r\n    display: inline-block; /* 内联块级元素，可以换行 */\r\n    padding: 3px 6px !important;\r\n    font-size: 11px !important;\r\n    line-height: 1.2 !important;\r\n    min-height: 24px !important;\r\n}\r\n\r\n/* ========== 表格列宽自适应样式 ========== */\r\n.auto-width-table {\r\n    table-layout: auto !important; /* 自动调整列宽 */\r\n    width: 100%;\r\n}\r\n\r\n.auto-width-table th,\r\n.auto-width-table td {\r\n    width: auto !important; /* 根据内容自动调整 */\r\n    white-space: nowrap; /* 防止文字换行 */\r\n}\r\n\r\n/* 特定列的宽度控制 */\r\n.col-id {\r\n    width: 60px !important; /* ID列固定宽度 */\r\n}\r\n\r\n.col-status {\r\n    width: 80px !important; /* 状态列固定宽度 */\r\n}\r\n\r\n.col-date {\r\n    width: 120px !important; /* 日期列固定宽度 */\r\n}\r\n\r\n.col-actions {\r\n    width: 200px !important; /* 操作列固定宽度 */\r\n    text-align: center;\r\n}\r\n\r\n.col-name {\r\n    min-width: 150px; /* 名称列最小宽度 */\r\n    max-width: 300px; /* 名称列最大宽度 */\r\n    white-space: normal; /* 允许名称换行 */\r\n    word-break: break-word; /* 长单词换行 */\r\n}\r\n\r\n/* 加载状态 */\r\n.loading {\r\n    position: relative;\r\n}\r\n\r\n.loading::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    width: 20px;\r\n    height: 20px;\r\n    margin: -10px 0 0 -10px;\r\n    border: 2px solid #f3f3f3;\r\n    border-top: 2px solid #3498db;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n    0% {\r\n        transform: rotate(0deg);\r\n    }\r\n\r\n    100% {\r\n        transform: rotate(360deg);\r\n    }\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stat-card {\r\n    transition: all 0.3s ease;\r\n    cursor: pointer;\r\n}\r\n\r\n.stat-card:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 表格增强样式 */\r\n.data-table {\r\n    width: 100%;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.table-controls {\r\n    margin-bottom: 15px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n    gap: 10px;\r\n}\r\n\r\n.table-search {\r\n    max-width: 300px;\r\n    flex: 1;\r\n}\r\n\r\n.table-pagination {\r\n    margin-top: 15px;\r\n    text-align: center;\r\n}\r\n\r\n.table-pagination .btn {\r\n    margin: 0 2px;\r\n}\r\n\r\n.batch-actions {\r\n    display: none;\r\n    background: #f8f9fa;\r\n    padding: 10px;\r\n    border-radius: 4px;\r\n    margin-bottom: 15px;\r\n}\r\n\r\n.batch-actions .selected-count {\r\n    font-weight: bold;\r\n    color: #007bff;\r\n}\r\n\r\n/* 文件上传样式 */\r\n.file-preview {\r\n    margin-top: 10px;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 10px;\r\n}\r\n\r\n.file-preview-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    padding: 10px;\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n    background: #f9f9f9;\r\n}\r\n\r\n.file-preview-item img {\r\n    margin-bottom: 5px;\r\n}\r\n\r\n.file-preview-item span {\r\n    font-size: 12px;\r\n    text-align: center;\r\n    word-break: break-all;\r\n}\r\n\r\n/* 表单验证样式 */\r\n.form-group.has-error .form-control {\r\n    border-color: #dc3545;\r\n}\r\n\r\n.form-group.has-error .help-block {\r\n    color: #dc3545;\r\n}\r\n\r\n.is-invalid {\r\n    border-color: #dc3545 !important;\r\n}\r\n\r\n.is-valid {\r\n    border-color: #28a745 !important;\r\n}\r\n\r\n/* 模态框增强样式 */\r\n.modal-content {\r\n    border-radius: 6px;\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.modal-header {\r\n    border-bottom: 1px solid #e5e5e5;\r\n    padding: 15px 20px;\r\n}\r\n\r\n.modal-body {\r\n    padding: 20px;\r\n}\r\n\r\n.modal-footer {\r\n    border-top: 1px solid #e5e5e5;\r\n    padding: 15px 20px;\r\n    text-align: right;\r\n}\r\n\r\n.modal-footer .btn {\r\n    margin-left: 5px;\r\n}\r\n\r\n/* 在线用户样式 */\r\n.online-users-count {\r\n    cursor: pointer;\r\n    transition: color 0.3s ease;\r\n}\r\n\r\n.online-users-count:hover {\r\n    color: #007bff;\r\n    text-decoration: underline;\r\n}\r\n\r\n/* 侧边栏基础样式 */\r\n.sidebar {\r\n    position: fixed !important;\r\n    top: 0 !important;\r\n    left: 0 !important;\r\n    width: 220px !important;\r\n    height: 100vh !important;\r\n    background-color: #2e6da4 !important;\r\n    color: white !important;\r\n    z-index: var(--z-sidebar) !important;\r\n    overflow-y: auto !important;\r\n    /* 强制显示滚动条，避免菜单展开/折叠时的震动 */\r\n    min-height: 100vh;\r\n    /* 确保侧边栏始终有足够高度 */\r\n    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 侧边栏头部样式 */\r\n.sidebar-header {\r\n    padding: 15px;\r\n    text-align: center;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.sidebar-header img {\r\n    max-width: 100px;\r\n    max-height: 100px;\r\n    margin: 0 auto;\r\n    display: block;\r\n}\r\n\r\n.sidebar-title {\r\n    font-weight: bold;\r\n    margin: 10px 0;\r\n    color: white;\r\n    font-size: 18px;\r\n}\r\n\r\n/* 导航菜单样式 */\r\n.nav-sidebar {\r\n    margin-top: 20px;\r\n    padding: 0;\r\n    list-style: none;\r\n}\r\n\r\n.nav-sidebar li {\r\n    margin: 0;\r\n    padding: 0;\r\n}\r\n\r\n.nav-sidebar a {\r\n    display: block;\r\n    padding: 12px 20px;\r\n    color: white !important;\r\n    text-decoration: none;\r\n    transition: background-color 0.3s;\r\n    border: none;\r\n}\r\n\r\n.nav-sidebar a:hover,\r\n.nav-sidebar a.active {\r\n    background-color: #1e4d72 !important;\r\n    color: white !important;\r\n    text-decoration: none;\r\n    border-left: 4px solid #fff;\r\n}\r\n\r\n.nav-sidebar i {\r\n    margin-right: 10px;\r\n    width: 20px;\r\n    text-align: center;\r\n}\r\n\r\n/* 预设菜单最小高度，避免第一次展开时的滚动条闪烁 */\r\n.sidebar .collapse {\r\n    min-height: 0 !important;\r\n    /* 重置Bootstrap默认的min-height */\r\n}\r\n\r\n.sidebar .collapse.collapsing {\r\n    overflow: hidden !important;\r\n    /* 动画过程中隐藏溢出内容 */\r\n    min-height: 0 !important;\r\n}\r\n\r\n.sidebar .collapse.in {\r\n    overflow: visible !important;\r\n    /* 展开后显示所有内容 */\r\n}\r\n\r\n.main-content {\r\n    margin-left: 220px !important;\r\n    /* 相应调整主内容区域的左边距 */\r\n}\r\n\r\n/* 防止页面整体滚动条造成的震动 */\r\nhtml {\r\n    overflow-y: scroll !important;\r\n    /* 强制显示页面滚动条 */\r\n}\r\n\r\nbody {\r\n    overflow-x: hidden;\r\n    /* 隐藏水平滚动条 */\r\n}\r\n\r\n.sidebar-collapsed .sidebar {\r\n    width: 60px;\r\n}\r\n\r\n.sidebar-collapsed .sidebar .nav-label {\r\n    display: none;\r\n}\r\n\r\n.sidebar-collapsed .sidebar .nav-icon {\r\n    text-align: center;\r\n    width: 100%;\r\n}\r\n\r\n/* 响应式设计已移除 */\r\n\r\n/* 动画效果 */\r\n.fade-in {\r\n    animation: fadeIn 0.3s ease-in;\r\n}\r\n\r\n@keyframes fadeIn {\r\n    from {\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n/* ========== 搜索结果模态框样式 - 全局通用 ========== */\r\n/* 搜索模态框高度限制和滚动 - 确保不遮挡页面底部 */\r\nbody .search-modal.modal .modal-dialog {\r\n    max-height: 75vh !important;\r\n    margin: 20px auto !important;\r\n    margin-bottom: 120px !important; /* 确保底部有足够空间 */\r\n}\r\n\r\nbody .search-modal.modal .modal-content {\r\n    max-height: 70vh !important;\r\n    overflow: hidden !important;\r\n    display: flex !important;\r\n    flex-direction: column !important;\r\n}\r\n\r\nbody .search-modal.modal .modal-header {\r\n    flex-shrink: 0 !important;\r\n    padding: 10px 15px !important;\r\n}\r\n\r\nbody .search-modal.modal .modal-body {\r\n    max-height: 50vh !important;\r\n    overflow-y: auto !important;\r\n    flex: 1 !important;\r\n    padding: 10px !important;\r\n}\r\n\r\nbody .search-modal.modal .modal-footer {\r\n    flex-shrink: 0 !important;\r\n    padding: 10px 15px !important;\r\n}\r\n\r\n/* 搜索模态框表格容器样式 */\r\nbody .search-modal.modal .table-responsive {\r\n    max-height: 40vh !important;\r\n    overflow-y: auto !important;\r\n    border: 1px solid #ddd !important;\r\n    border-radius: 4px;\r\n}\r\n\r\n/* 确保搜索结果表格有滚动条 */\r\nbody .search-modal.modal .table-responsive table {\r\n    margin-bottom: 0 !important;\r\n}\r\n\r\n/* 搜索模态框表格样式优化 */\r\n.search-modal .table th {\r\n    background-color: #f5f5f5;\r\n    font-weight: bold;\r\n    border-bottom: 2px solid #ddd;\r\n    position: sticky;\r\n    top: 0;\r\n    /* z-index: 10; - 取消低优先级z-index */\r\n}\r\n\r\n.search-modal .table tbody tr:hover {\r\n    background-color: #f9f9f9;\r\n}\r\n\r\n/* 搜索模态框按钮样式 */\r\n.search-modal .btn {\r\n    margin: 2px;\r\n}\r\n\r\n/* 搜索模态框复选框样式 */\r\n.search-modal .table input[type=\"checkbox\"] {\r\n    transform: scale(1.2);\r\n}\r\n\r\n/* 搜索模态框响应式调整已移除 */\r\n\r\n/* 确保模态框不会超出视口 */\r\n.search-modal.modal {\r\n    overflow-y: auto !important;\r\n}\r\n\r\n.search-modal.modal .modal-dialog {\r\n    position: relative !important;\r\n    top: 0 !important;\r\n    transform: none !important;\r\n}\r\n\r\n.slide-in {\r\n    animation: slideIn 0.3s ease-out;\r\n}\r\n\r\n@keyframes slideIn {\r\n    from {\r\n        transform: translateY(-20px);\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        transform: translateY(0);\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n/* 工具提示样式 */\r\n.tooltip {\r\n    position: relative;\r\n    display: inline-block;\r\n}\r\n\r\n.tooltip .tooltiptext {\r\n    visibility: hidden;\r\n    width: 120px;\r\n    background-color: #555;\r\n    color: #fff;\r\n    text-align: center;\r\n    border-radius: 6px;\r\n    padding: 5px 0;\r\n    position: absolute;\r\n    /* z-index: 1; - 取消低优先级z-index */\r\n    bottom: 125%;\r\n    left: 50%;\r\n    margin-left: -60px;\r\n    opacity: 0;\r\n    transition: opacity 0.3s;\r\n}\r\n\r\n.tooltip:hover .tooltiptext {\r\n    visibility: visible;\r\n    opacity: 1;\r\n}\r\n\r\n/* ========== 实验室管理员主页样式 ========== */\r\n/* 实验室管理员主页数据卡片样式 */\r\n.lab-admin-stat-card {\r\n    background-color: #fff;\r\n    border-radius: 4px;\r\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);\r\n    margin-bottom: 20px;\r\n    position: relative;\r\n    overflow: hidden;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.lab-admin-stat-card:hover {\r\n    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);\r\n    transform: translateY(-2px);\r\n}\r\n\r\n.lab-admin-stat-card .icon {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 80px;\r\n    height: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 30px;\r\n}\r\n\r\n.lab-admin-stat-card .number {\r\n    font-size: 36px;\r\n    font-weight: bold;\r\n    margin-left: 90px;\r\n    padding-top: 15px;\r\n    padding-right: 15px;\r\n}\r\n\r\n.lab-admin-stat-card .title {\r\n    margin-left: 90px;\r\n    padding-bottom: 15px;\r\n    color: #777;\r\n}\r\n\r\n/* 实验室管理员主页面板样式 */\r\n.lab-admin-panel {\r\n    margin-bottom: 20px;\r\n    background-color: #fff;\r\n    border: 1px solid transparent;\r\n    border-radius: 4px;\r\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);\r\n}\r\n\r\n.lab-admin-panel-heading {\r\n    padding: 10px 15px;\r\n    border-bottom: 1px solid transparent;\r\n    border-top-left-radius: 3px;\r\n    border-top-right-radius: 3px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n}\r\n\r\n.lab-admin-panel-title {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n}\r\n\r\n.lab-admin-panel-body {\r\n    padding: 15px;\r\n}\r\n\r\n/* 实验室管理员主页待办事项样式 */\r\n.lab-admin-todo-item {\r\n    padding: 10px;\r\n    margin-bottom: 10px;\r\n    border-left: 4px solid #f0ad4e;\r\n    background-color: #f8f9fa;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n}\r\n\r\n.lab-admin-todo-item.overdue {\r\n    border-left-color: #d9534f;\r\n}\r\n\r\n.lab-admin-todo-item-content {\r\n    flex-grow: 1;\r\n}\r\n\r\n.lab-admin-todo-item-title {\r\n    font-weight: bold;\r\n    margin-bottom: 5px;\r\n}\r\n\r\n.lab-admin-todo-item-date {\r\n    font-size: 12px;\r\n    color: #777;\r\n}\r\n\r\n.lab-admin-todo-item-action {\r\n    margin-left: 10px;\r\n}\r\n\r\n/* 实验室管理员主页快捷入口样式 */\r\n.lab-admin-shortcut-container {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 10px;\r\n}\r\n\r\n.lab-admin-shortcut-item {\r\n    flex: 1;\r\n    min-width: 150px;\r\n    text-align: center;\r\n    padding: 15px;\r\n    background-color: #f8f9fa;\r\n    border-radius: 4px;\r\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.lab-admin-shortcut-item:hover {\r\n    background-color: #e9ecef;\r\n    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);\r\n    transform: translateY(-2px);\r\n}\r\n\r\n.lab-admin-shortcut-icon {\r\n    font-size: 24px;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.lab-admin-shortcut-title {\r\n    font-weight: bold;\r\n}\r\n\r\n/* 实验室管理员主页实验室卡片样式 */\r\n.lab-admin-lab-card {\r\n    margin-bottom: 15px;\r\n    border-radius: 4px;\r\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.lab-admin-lab-card:hover {\r\n    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);\r\n    transform: translateY(-2px);\r\n}\r\n\r\n.lab-admin-lab-card .panel-heading {\r\n    border-top-left-radius: 4px;\r\n    border-top-right-radius: 4px;\r\n}\r\n\r\n.lab-admin-lab-card .panel-body {\r\n    padding: 15px;\r\n}\r\n\r\n.lab-admin-lab-card .btn-group {\r\n    margin-top: 10px;\r\n}\r\n\r\n/* 实验室管理员主页公告样式 */\r\n.lab-admin-announcement {\r\n    padding: 10px;\r\n    margin-bottom: 10px;\r\n    border-left: 4px solid #5bc0de;\r\n    background-color: #f8f9fa;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.lab-admin-announcement:hover {\r\n    background-color: #e9ecef;\r\n    border-left-color: #31b0d5;\r\n}\r\n\r\n.lab-admin-announcement-title {\r\n    font-weight: bold;\r\n    margin-bottom: 5px;\r\n    color: #337ab7;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n}\r\n\r\n.lab-admin-announcement-date {\r\n    font-size: 12px;\r\n    color: #777;\r\n    margin-bottom: 5px;\r\n}\r\n\r\n.lab-admin-announcement-content {\r\n    color: #333;\r\n    display: none;\r\n    margin-top: 10px;\r\n    padding-top: 10px;\r\n    border-top: 1px solid #ddd;\r\n}\r\n\r\n.lab-admin-announcement.expanded .lab-admin-announcement-content {\r\n    display: block;\r\n}\r\n\r\n/* 实验室管理员主页旧样式合并 */\r\n.lab-admin-lab-card .panel-footer {\r\n    border-bottom-left-radius: 5px;\r\n    border-bottom-right-radius: 5px;\r\n    background-color: #f9f9f9;\r\n}\r\n\r\n.lab-admin-status-normal {\r\n    color: #5cb85c;\r\n}\r\n\r\n.lab-admin-status-warning {\r\n    color: #f0ad4e;\r\n}\r\n\r\n.lab-admin-status-danger {\r\n    color: #d9534f;\r\n}\r\n\r\n.lab-admin-task-item {\r\n    padding: 10px;\r\n    border-bottom: 1px solid #eee;\r\n}\r\n\r\n.lab-admin-task-item:last-child {\r\n    border-bottom: none;\r\n}\r\n\r\n.lab-admin-task-item .label {\r\n    margin-right: 5px;\r\n}\r\n\r\n.lab-admin-task-item .deadline {\r\n    color: #777;\r\n    font-size: 0.9em;\r\n}\r\n\r\n.lab-admin-task-item .overdue {\r\n    color: #d9534f;\r\n    font-weight: bold;\r\n}\r\n\r\n/* ========== 统计分析页面样式 ========== */\r\n/* 统计分析首页样式 */\r\n.stats-card {\r\n    margin-bottom: 20px;\r\n    transition: transform 0.3s;\r\n}\r\n\r\n.stats-card:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-icon {\r\n    font-size: 3rem;\r\n    margin-bottom: 15px;\r\n    color: #007bff;\r\n}\r\n\r\n.card-title {\r\n    font-size: 1.2rem;\r\n    font-weight: bold;\r\n}\r\n\r\n.card-description {\r\n    color: #6c757d;\r\n    margin-top: 10px;\r\n}\r\n\r\n/* 按指标项统计页面样式 */\r\n.search-box {\r\n    background-color: #f8f9fa;\r\n    padding: 15px;\r\n    border-radius: 5px;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n/* 基础表格响应式容器样式 */\r\n.table-responsive {\r\n    margin-top: 20px;\r\n}\r\n\r\n.nav-tabs {\r\n    margin-bottom: 15px;\r\n}\r\n\r\n.btn-export {\r\n    margin-left: 10px;\r\n}\r\n\r\n/* ========== 检查记录页面样式 ========== */\r\n/* 检查记录过滤区域样式 */\r\n.filter-section {\r\n    margin-bottom: 20px;\r\n    padding: 15px;\r\n    background-color: #f9f9f9;\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n}\r\n\r\n.result-tab {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.result-tab .nav-tabs {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.result-tab .tab-content {\r\n    padding: 15px;\r\n    border-left: 1px solid #ddd;\r\n    border-right: 1px solid #ddd;\r\n    border-bottom: 1px solid #ddd;\r\n    border-radius: 0 0 4px 4px;\r\n}\r\n\r\n.sort-icon {\r\n    margin-left: 5px;\r\n}\r\n\r\n.batch-actions {\r\n    margin-bottom: 10px;\r\n}\r\n\r\n/* ========== 首页时间选择器样式 ========== */\r\n/* 首页时间范围选择器样式 */\r\n.time-range-selector-container {\r\n    margin: 15px 0 20px 0;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.time-range-label {\r\n    margin-right: 10px;\r\n    font-weight: bold;\r\n}\r\n\r\n.time-range-selector .btn.active {\r\n    background-color: #337ab7;\r\n    color: white;\r\n    border-color: #2e6da4;\r\n}\r\n\r\n/* 按钮增强样式 */\r\n.btn {\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.btn:hover {\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.btn:active {\r\n    transform: translateY(0);\r\n}\r\n\r\n/* 卡片样式 */\r\n.card {\r\n    border: 1px solid #e3e6f0;\r\n    border-radius: 0.35rem;\r\n    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);\r\n}\r\n\r\n.card-header {\r\n    background-color: #f8f9fc;\r\n    border-bottom: 1px solid #e3e6f0;\r\n}\r\n\r\n/* 徽章样式 */\r\n.badge {\r\n    font-size: 0.75em;\r\n    font-weight: 700;\r\n    padding: 0.25rem 0.5rem;\r\n    border-radius: 0.35rem;\r\n}\r\n\r\n/* 进度条样式 */\r\n.progress {\r\n    height: 1rem;\r\n    border-radius: 0.35rem;\r\n    background-color: #eaecf4;\r\n}\r\n\r\n.progress-bar {\r\n    border-radius: 0.35rem;\r\n    transition: width 0.6s ease;\r\n}\r\n\r\n/* 列表组样式 */\r\n.list-group-item {\r\n    border: 1px solid #e3e6f0;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.list-group-item:hover {\r\n    background-color: #f8f9fc;\r\n}\r\n\r\n/* 导航样式 */\r\n.nav-tabs .nav-link {\r\n    border: 1px solid transparent;\r\n    border-top-left-radius: 0.35rem;\r\n    border-top-right-radius: 0.35rem;\r\n}\r\n\r\n.nav-tabs .nav-link.active {\r\n    background-color: #fff;\r\n    border-color: #e3e6f0 #e3e6f0 #fff;\r\n}\r\n\r\n/* ========== 首页专用样式 ========== */\r\n/* 页面标题区域 - 保持蓝色主题 */\r\n.page-header-section {\r\n    background: #2e6da4;\r\n    color: white;\r\n    padding: 20px 25px;\r\n    border-radius: 4px;\r\n    margin-bottom: 20px;\r\n    box-shadow: 0 2px 4px rgba(46, 109, 164, 0.2);\r\n}\r\n\r\n.page-header-title {\r\n    font-size: 22px;\r\n    font-weight: 500;\r\n    margin-bottom: 5px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n}\r\n\r\n.page-header-subtitle {\r\n    font-size: 14px;\r\n    opacity: 0.9;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.current-time {\r\n    font-size: 13px;\r\n    opacity: 0.9;\r\n    background: rgba(255, 255, 255, 0.15);\r\n    padding: 5px 10px;\r\n    border-radius: 3px;\r\n}\r\n\r\n/* 实时状态指示器 */\r\n.status-indicators {\r\n    display: flex;\r\n    gap: 15px;\r\n    margin-top: 10px;\r\n}\r\n\r\n.status-item {\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: 12px;\r\n    opacity: 0.9;\r\n}\r\n\r\n.status-dot {\r\n    width: 8px;\r\n    height: 8px;\r\n    border-radius: 50%;\r\n    margin-right: 5px;\r\n}\r\n\r\n.status-online {\r\n    background: #5cb85c;\r\n}\r\n\r\n.status-warning {\r\n    background: #f0ad4e;\r\n}\r\n\r\n.status-error {\r\n    background: #d9534f;\r\n}\r\n\r\n/* 改进数据卡片样式 - 保持原有色调 */\r\n.improved-stat-card {\r\n    background: white;\r\n    border-radius: 4px;\r\n    padding: 20px;\r\n    margin-bottom: 20px;\r\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);\r\n    transition: all 0.3s ease;\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.improved-stat-card:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);\r\n}\r\n\r\n.stat-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    margin-bottom: 15px;\r\n}\r\n\r\n.stat-icon-circle {\r\n    width: 50px;\r\n    height: 50px;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 20px;\r\n    color: white;\r\n}\r\n\r\n.stat-trend {\r\n    font-size: 11px;\r\n    padding: 3px 8px;\r\n    border-radius: 10px;\r\n    font-weight: 600;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 3px;\r\n}\r\n\r\n.trend-up {\r\n    background: #d4edda;\r\n    color: #155724;\r\n}\r\n\r\n.trend-down {\r\n    background: #f8d7da;\r\n    color: #721c24;\r\n}\r\n\r\n.trend-stable {\r\n    background: #e2e3e5;\r\n    color: #383d41;\r\n}\r\n\r\n.stat-number {\r\n    font-size: 32px;\r\n    font-weight: 700;\r\n    color: #333;\r\n    margin-bottom: 5px;\r\n    line-height: 1;\r\n}\r\n\r\n.stat-title {\r\n    font-size: 14px;\r\n    color: #666;\r\n    margin-bottom: 0;\r\n    font-weight: 500;\r\n}\r\n\r\n/* 最新动态样式 */\r\n.activity-item {\r\n    padding: 12px 0;\r\n    border-bottom: 1px solid #f1f1f1;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.activity-item:last-child {\r\n    border-bottom: none;\r\n}\r\n\r\n.activity-icon {\r\n    width: 35px;\r\n    height: 35px;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-right: 12px;\r\n    font-size: 14px;\r\n    color: white;\r\n}\r\n\r\n.activity-content {\r\n    flex: 1;\r\n}\r\n\r\n.activity-title {\r\n    font-size: 13px;\r\n    color: #333;\r\n    margin-bottom: 2px;\r\n    font-weight: 500;\r\n}\r\n\r\n.activity-time {\r\n    font-size: 11px;\r\n    color: #777;\r\n}\r\n\r\n/* 改进面板样式 - 保持原有风格 */\r\n.panel {\r\n    margin-bottom: 20px;\r\n    background-color: #fff;\r\n    border: 1px solid transparent;\r\n    border-radius: 4px;\r\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.panel:hover {\r\n    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);\r\n    transform: translateY(-1px);\r\n}\r\n\r\n.panel-heading {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid transparent;\r\n    border-top-left-radius: 3px;\r\n    border-top-right-radius: 3px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n}\r\n\r\n.panel-title {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.panel-title i {\r\n    margin-right: 8px;\r\n}\r\n\r\n.panel-body {\r\n    padding: 20px;\r\n}\r\n\r\n/* 待办事项样式 */\r\n.todo-item {\r\n    padding: 12px 15px;\r\n    margin-bottom: 10px;\r\n    border-left: 4px solid #f0ad4e;\r\n    background-color: #f8f9fa;\r\n    border-radius: 0 4px 4px 0;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.todo-item:hover {\r\n    background-color: #e9ecef;\r\n    transform: translateX(3px);\r\n}\r\n\r\n.todo-item.overdue {\r\n    border-left-color: #d9534f;\r\n}\r\n\r\n.todo-item.completed {\r\n    border-left-color: #5cb85c;\r\n    opacity: 0.7;\r\n}\r\n\r\n.todo-item-content {\r\n    flex-grow: 1;\r\n}\r\n\r\n.todo-item-title {\r\n    font-weight: 500;\r\n    margin-bottom: 3px;\r\n    font-size: 13px;\r\n    color: #333;\r\n}\r\n\r\n.todo-item-date {\r\n    font-size: 11px;\r\n    color: #777;\r\n}\r\n\r\n.todo-item-action {\r\n    margin-left: 10px;\r\n}\r\n\r\n/* 公告样式 */\r\n.announcement {\r\n    padding: 10px;\r\n    margin-bottom: 10px;\r\n    border-left: 4px solid #5bc0de;\r\n    background-color: #f8f9fa;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.announcement:hover {\r\n    background-color: #e9ecef;\r\n    border-left-color: #31b0d5;\r\n}\r\n\r\n.announcement-title {\r\n    font-weight: bold;\r\n    margin-bottom: 5px;\r\n    color: #337ab7;\r\n}\r\n\r\n.announcement-date {\r\n    font-size: 12px;\r\n    color: #777;\r\n    margin-bottom: 5px;\r\n}\r\n\r\n.announcement-content {\r\n    color: #333;\r\n    display: none;\r\n    margin-top: 10px;\r\n    padding-top: 10px;\r\n    border-top: 1px solid #ddd;\r\n}\r\n\r\n.announcement.expanded .announcement-content {\r\n    display: block;\r\n}\r\n\r\n/* 改进快捷入口样式 - 保持原有风格 */\r\n.shortcut-container {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 10px;\r\n}\r\n\r\n.shortcut-item {\r\n    flex: 1;\r\n    min-width: 120px;\r\n    text-align: center;\r\n    padding: 15px 10px;\r\n    background-color: #f8f9fa;\r\n    border-radius: 4px;\r\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);\r\n    transition: all 0.3s ease;\r\n    text-decoration: none;\r\n    color: #333;\r\n    border: 1px solid #e9ecef;\r\n}\r\n\r\n.shortcut-item:hover {\r\n    background-color: #e9ecef;\r\n    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);\r\n    transform: translateY(-2px);\r\n    text-decoration: none;\r\n    color: #333;\r\n    border-color: #dee2e6;\r\n}\r\n\r\n.shortcut-icon {\r\n    font-size: 24px;\r\n    margin-bottom: 8px;\r\n    display: block;\r\n}\r\n\r\n.shortcut-title {\r\n    font-weight: 500;\r\n    font-size: 13px;\r\n    line-height: 1.3;\r\n}\r\n\r\n.shortcut-badge {\r\n    display: inline-block;\r\n    min-width: 18px;\r\n    padding: 3px 6px;\r\n    font-size: 11px;\r\n    font-weight: 700;\r\n    line-height: 1;\r\n    color: #fff;\r\n    text-align: center;\r\n    white-space: nowrap;\r\n    vertical-align: middle;\r\n    background-color: #d9534f;\r\n    border-radius: 10px;\r\n    margin-left: 5px;\r\n}\r\n\r\n/* ========== base 页面样式 ========== */\r\n.footer {\r\n    margin-top: 30px;\r\n    padding: 15px 0;\r\n    background-color: #f5f5f5;\r\n    border-top: 1px solid #ddd;\r\n}\r\n\r\n@media print {\r\n    .no-print {\r\n        display: none !important;\r\n    }\r\n\r\n    .main-content {\r\n        margin-left: 0 !important;\r\n        padding: 0 !important;\r\n    }\r\n\r\n    body {\r\n        background-color: white !important;\r\n    }\r\n}\r\n\r\n/* 菜单展开样式 - Bootstrap 3兼容，强制优先级 */\r\n.sidebar .collapse.in {\r\n    display: block !important;\r\n}\r\n\r\n.sidebar a[data-toggle=\"collapse\"] .fa-angle-down {\r\n    transition: transform 0.4s ease-in-out;\r\n}\r\n\r\n/* Bootstrap collapse动画优化 - 统一动画时间 */\r\n.sidebar .collapsing {\r\n    transition: height 0.35s ease-in-out !important;\r\n}\r\n\r\n/* 快速折叠动画 - 用于菜单切换时的关闭动画 */\r\n.sidebar .collapse.fast-collapse.collapsing {\r\n    transition: height 0.25s ease-in-out !important;\r\n}\r\n\r\n/* 🚫 DISABLED: 确保菜单动画同步 - 与Bootstrap冲突，导致双重动画 */\r\n/*\r\n        .sidebar .collapse.in {\r\n            animation: slideDown 0.4s ease-in-out;\r\n        }\r\n\r\n        @keyframes slideDown {\r\n            from {\r\n                opacity: 0;\r\n                max-height: 0;\r\n            }\r\n\r\n            to {\r\n                opacity: 1;\r\n                max-height: 500px;\r\n            }\r\n        }\r\n        */\r\n\r\n        @keyframes slideDown {\r\n            from {\r\n                opacity: 0;\r\n                max-height: 0;\r\n            }\r\n\r\n            to {\r\n                opacity: 1;\r\n                max-height: 500px;\r\n            }\r\n        }\r\n\r\n.sidebar .collapse {\r\n    position: relative;\r\n    /* z-index: 1; - 取消低优先级z-index */\r\n    /* 🚫 DISABLED: transition: all 0.4s ease-in-out; - 与Bootstrap冲突 */\r\n    overflow: hidden;\r\n    height: auto;\r\n}\r\n\r\n/* 添加菜单展开/折叠动画 - 兼容Bootstrap 3和4+ */\r\n.sidebar .collapse:not(.show):not(.in) {\r\n    display: none;\r\n}\r\n\r\n/* ========== base-backup 页面样式 ========== */\r\n.footer {\r\n    margin-top: 30px;\r\n    padding: 15px 0;\r\n    background-color: #f5f5f5;\r\n    border-top: 1px solid #ddd;\r\n}\r\n\r\n@media print {\r\n    .no-print {\r\n        display: none !important;\r\n    }\r\n\r\n    .main-content {\r\n        margin-left: 0 !important;\r\n        padding: 0 !important;\r\n    }\r\n\r\n    body {\r\n        background-color: white !important;\r\n    }\r\n}\r\n\r\n/* 子菜单样式 */\r\n.sidebar .collapse .collapse {\r\n    padding-left: 15px;\r\n}\r\n\r\n.copyright {\r\n    margin: 0;\r\n    color: #777;\r\n    font-size: 14px;\r\n}\r\n\r\n@media print {\r\n    .no-print {\r\n        display: none !important;\r\n    }\r\n\r\n    .main-content {\r\n        margin-left: 0 !important;\r\n        padding: 0 !important;\r\n    }\r\n\r\n    body {\r\n        background-color: white !important;\r\n    }\r\n}\r\n\r\n/* 菜单展开样式 */\r\n.collapse.show {\r\n    display: block;\r\n}\r\n\r\n.sidebar .collapse {\r\n    padding-left: 15px;\r\n}\r\n\r\n.sidebar a[data-toggle=\"collapse\"] .fa-angle-down {\r\n    transition: transform 0.3s;\r\n}\r\n\r\n.sidebar a[data-toggle=\"collapse\"][aria-expanded=\"true\"] .fa-angle-down,\r\n.sidebar a[data-toggle=\"collapse\"].active .fa-angle-down {\r\n    transform: rotate(180deg);\r\n}\r\n\r\n/* 防止菜单重叠 - 改进版 */\r\n.sidebar li {\r\n    position: relative;\r\n}\r\n\r\n.sidebar .collapse {\r\n    position: relative;\r\n    /* z-index: 1; - 取消低优先级z-index */\r\n    transition: all 0.3s ease-out;\r\n    overflow: hidden;\r\n    height: auto;\r\n}\r\n\r\n/* 确保子菜单在父菜单内部 */\r\n.sidebar .collapse .collapse {\r\n    position: relative;\r\n    /* z-index: 2; - 取消低优先级z-index */\r\n}\r\n\r\n/* 添加菜单展开/折叠动画 */\r\n.sidebar .collapse:not(.show) {\r\n    display: none;\r\n}\r\n\r\n/* 防止菜单重叠的额外样式 */\r\n.sidebar .nav-sidebar>li {\r\n    clear: both;\r\n    margin-bottom: 2px;\r\n    width: 100%;\r\n}\r\n\r\n/* 确保菜单项不会被遮挡 */\r\n.sidebar a {\r\n    position: relative;\r\n    /* z-index: 3; - 取消低优先级z-index */\r\n}\r\n\r\n/* ========== admin-activity-report-preview 页面样式 ========== */\r\n.report-preview {\r\n    background-color: #fff;\r\n    padding: 30px;\r\n    border: 1px solid #ddd;\r\n    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\r\n    margin-top: 20px;\r\n}\r\n\r\n.report-preview h1 {\r\n    font-size: 24px;\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.report-preview h2 {\r\n    font-size: 18px;\r\n    margin-top: 25px;\r\n    margin-bottom: 15px;\r\n    border-bottom: 1px solid #eee;\r\n    padding-bottom: 5px;\r\n}\r\n\r\n/* ========== admin-all-announcements 页面样式 ========== */\r\n\r\n/* 公告卡片样式 */\r\n.announcement-card {\r\n    margin-bottom: 20px;\r\n    border-radius: 4px;\r\n    background-color: #fff;\r\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.announcement-card:hover {\r\n    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);\r\n    transform: translateY(-2px);\r\n}\r\n\r\n.card-header {\r\n    padding: 15px;\r\n    background-color: #f8f9fa;\r\n    border-bottom: 1px solid #ddd;\r\n    position: relative;\r\n    border-top-left-radius: 4px;\r\n    border-top-right-radius: 4px;\r\n}\r\n\r\n.card-title {\r\n    margin: 0;\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n    padding-right: 100px;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.card-body {\r\n    padding: 15px;\r\n}\r\n\r\n.card-footer {\r\n    padding: 10px 15px;\r\n    background-color: #f8f9fa;\r\n    border-top: 1px solid #ddd;\r\n    font-size: 12px;\r\n    color: #666;\r\n    border-bottom-left-radius: 4px;\r\n    border-bottom-right-radius: 4px;\r\n}\r\n\r\n.meta-info {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 15px;\r\n}\r\n\r\n.priority-badge {\r\n    display: inline-block;\r\n    padding: 3px 8px;\r\n    border-radius: 3px;\r\n    font-size: 12px;\r\n    margin-left: 10px;\r\n    color: white;\r\n}\r\n\r\n.priority-high {\r\n    background-color: #dc3545;\r\n}\r\n\r\n.priority-medium {\r\n    background-color: #fd7e14;\r\n}\r\n\r\n.priority-low {\r\n    background-color: #28a745;\r\n}\r\n\r\n/* 删除重复的announcement-date样式定义，避免绝对定位导致的叠加问题 */\r\n\r\n/* 筛选区域样式 */\r\n.filter-section {\r\n    background-color: #f8f9fa;\r\n    border-radius: 4px;\r\n    padding: 15px;\r\n    margin-bottom: 20px;\r\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.filter-section .form-group {\r\n    margin-bottom: 0;\r\n}\r\n\r\n/* 响应式调整 */\r\n\r\n/* ========== admin-announcements 页面样式 ========== */\r\n\r\n.announcement-card .card-header {\r\n    padding: 10px 15px;\r\n    border-bottom: 1px solid #ddd;\r\n    background-color: #f5f5f5;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n}\r\n\r\n.priority-high {\r\n    background-color: #d9534f;\r\n    color: white;\r\n}\r\n\r\n.priority-medium {\r\n    background-color: #f0ad4e;\r\n    color: white;\r\n}\r\n\r\n.priority-low {\r\n    background-color: #5bc0de;\r\n    color: white;\r\n}\r\n\r\n.status-badge {\r\n    display: inline-block;\r\n    padding: 2px 5px;\r\n    border-radius: 3px;\r\n    font-size: 12px;\r\n    margin-left: 5px;\r\n}\r\n\r\n.status-active {\r\n    background-color: #5cb85c;\r\n    color: white;\r\n}\r\n\r\n.status-inactive {\r\n    background-color: #777;\r\n    color: white;\r\n}\r\n\r\n.status-scheduled {\r\n    background-color: #5bc0de;\r\n    color: white;\r\n}\r\n\r\n.status-expired {\r\n    background-color: #d9534f;\r\n    color: white;\r\n}\r\n\r\n.announcement-card .card-title {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n}\r\n\r\n.announcement-card .card-body {\r\n    padding: 15px;\r\n}\r\n\r\n.announcement-card .card-footer {\r\n    padding: 10px 15px;\r\n    background-color: #f9f9f9;\r\n    border-top: 1px solid #ddd;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n}\r\n\r\n.announcement-card .meta-info {\r\n    color: #777;\r\n    font-size: 12px;\r\n}\r\n\r\n.announcement-card .actions {\r\n    display: flex;\r\n    gap: 5px;\r\n}\r\n\r\n/* ========== admin-assign-indicators 页面样式 ========== */\r\n.form-section {\r\n    margin-bottom: 30px;\r\n    padding: 20px;\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n    background-color: #f9f9f9;\r\n}\r\n\r\n.form-section-title {\r\n    margin-top: 0;\r\n    margin-bottom: 20px;\r\n    padding-bottom: 10px;\r\n    border-bottom: 1px solid #ddd;\r\n}\r\n\r\n.indicator-list,\r\n.personnel-list {\r\n    max-height: 400px;\r\n    overflow-y: auto;\r\n    border: 1px solid #ddd;\r\n    padding: 10px;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.indicator-item,\r\n.personnel-item {\r\n    margin-bottom: 5px;\r\n    padding: 5px;\r\n    border-bottom: 1px solid #eee;\r\n}\r\n\r\n.indicator-item:last-child,\r\n.personnel-item:last-child {\r\n    border-bottom: none;\r\n}\r\n\r\n.indicator-level-1 {\r\n    font-weight: bold;\r\n    color: #337ab7;\r\n}\r\n\r\n.indicator-level-2 {\r\n    font-weight: normal;\r\n    color: #5cb85c;\r\n    margin-left: 20px;\r\n}\r\n\r\n.indicator-level-3 {\r\n    font-weight: normal;\r\n    color: #5bc0de;\r\n    margin-left: 40px;\r\n}\r\n\r\n.selected-indicator {\r\n    background-color: #dff0d8;\r\n}\r\n\r\n.custom-assignment-section {\r\n    display: none;\r\n    margin-top: 20px;\r\n    padding: 15px;\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n    background-color: #f5f5f5;\r\n}\r\n\r\n.user-assignment-panel {\r\n    margin-bottom: 15px;\r\n    padding: 10px;\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n    background-color: #fff;\r\n}\r\n\r\n.user-assignment-title {\r\n    font-weight: bold;\r\n    margin-bottom: 10px;\r\n    padding-bottom: 5px;\r\n    border-bottom: 1px solid #eee;\r\n}\r\n\r\n.check-point-list {\r\n    max-height: 300px;\r\n    overflow-y: auto;\r\n    padding: 15px;\r\n    margin-top: 10px;\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n    background-color: #f9f9f9;\r\n}\r\n\r\n/* 检查要点容器内的复选框样式 */\r\n.check-point-list .checkbox {\r\n    margin-bottom: 8px;\r\n    margin-top: 0;\r\n    padding-left: 0;\r\n}\r\n\r\n.check-point-list .checkbox label {\r\n    display: block;\r\n    width: 100%;\r\n    margin-bottom: 0;\r\n    padding: 5px 0;\r\n    font-weight: normal;\r\n    cursor: pointer;\r\n    line-height: 1.4;\r\n}\r\n\r\n.check-point-list .checkbox input[type=\"checkbox\"] {\r\n    margin-right: 8px;\r\n    margin-top: 2px;\r\n    vertical-align: top;\r\n}\r\n\r\n.check-point-list .text-muted {\r\n    font-size: 12px;\r\n    color: #999;\r\n    margin-left: 5px;\r\n}\r\n\r\n/* 日常巡查页面的检查要点列表样式 */\r\n.hazard-check-point-list {\r\n    max-height: 300px !important;\r\n    overflow-y: auto !important;\r\n    padding: 15px !important;\r\n    margin-top: 10px !important;\r\n    border: 1px solid #ddd !important;\r\n    border-radius: 4px !important;\r\n    background-color: #f9f9f9 !important;\r\n}\r\n\r\n.hazard-check-point-list .check-points-container {\r\n    margin: 0 !important;\r\n}\r\n\r\n.hazard-check-point-list .checkbox {\r\n    margin-bottom: 8px !important;\r\n    margin-top: 0 !important;\r\n    padding-left: 0 !important;\r\n}\r\n\r\n.hazard-check-point-list .checkbox label {\r\n    display: block !important;\r\n    width: 100% !important;\r\n    margin-bottom: 0 !important;\r\n    padding: 5px 0 !important;\r\n    font-weight: normal !important;\r\n    cursor: pointer !important;\r\n    line-height: 1.4 !important;\r\n}\r\n\r\n.hazard-check-point-list .checkbox input[type=\"checkbox\"] {\r\n    margin-right: 8px !important;\r\n    margin-top: 2px !important;\r\n    vertical-align: top !important;\r\n}\r\n\r\n.hazard-check-point-list .text-muted {\r\n    font-size: 12px !important;\r\n    color: #999 !important;\r\n    margin-left: 5px !important;\r\n}\r\n\r\n.check-point-item {\r\n    margin-bottom: 3px;\r\n    padding: 3px;\r\n}\r\n\r\n.tab-content {\r\n    padding: 15px;\r\n    border: 1px solid #ddd;\r\n    border-top: none;\r\n    border-radius: 0 0 4px 4px;\r\n}\r\n\r\n/* 第二个 check-point-list 样式已合并到上面 */\r\n\r\n/* ========== admin-browse-images 页面样式 ========== */\r\n.image-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n    gap: 15px;\r\n    margin-top: 20px;\r\n}\r\n\r\n.image-item {\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n    padding: 10px;\r\n    text-align: center;\r\n    transition: all 0.3s;\r\n}\r\n\r\n.image-item:hover {\r\n    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\r\n    transform: translateY(-5px);\r\n}\r\n\r\n.image-container {\r\n    height: 150px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-bottom: 10px;\r\n    overflow: hidden;\r\n}\r\n\r\n.image-container img {\r\n    max-width: 100%;\r\n    max-height: 150px;\r\n    object-fit: contain;\r\n}\r\n\r\n.image-filename {\r\n    font-size: 12px;\r\n    color: #666;\r\n    word-break: break-all;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n/* ========== admin-category-import-excel 页面样式 ========== */\r\n.mb-3 {\r\n    margin-bottom: 15px;\r\n}\r\n\r\n/* ========== admin-category-permission-matrix 页面样式 ========== */\r\n.matrix-container {\r\n    padding: 20px;\r\n    overflow-x: auto;\r\n}\r\n\r\n.permission-matrix {\r\n    width: 100%;\r\n    min-width: 1200px;\r\n    border-collapse: collapse;\r\n    background: white;\r\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.permission-matrix th,\r\n.permission-matrix td {\r\n    border: 1px solid #dee2e6;\r\n    padding: 6px;\r\n    text-align: center;\r\n    vertical-align: middle;\r\n    font-size: 12px;\r\n}\r\n\r\n.permission-matrix th {\r\n    background: #2e6da4;\r\n    color: white;\r\n    font-weight: bold;\r\n    position: sticky;\r\n    top: 0;\r\n    /* z-index: 10; - 取消低优先级z-index */\r\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.role-header {\r\n    min-width: 120px;\r\n    max-width: 140px;\r\n    padding: 8px 4px;\r\n}\r\n\r\n.role-header small {\r\n    font-size: 0.7em;\r\n    opacity: 0.9;\r\n    font-weight: normal;\r\n}\r\n\r\n.permission-cell {\r\n    min-width: 120px;\r\n    padding: 4px;\r\n    background: #f8f9fa;\r\n}\r\n\r\n.permission-controls {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 2px;\r\n}\r\n\r\n.permission-controls>div {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 3px;\r\n}\r\n\r\n.permission-checkbox {\r\n    margin: 0;\r\n    transform: scale(0.8);\r\n}\r\n\r\n.permission-controls small {\r\n    font-size: 0.7em;\r\n    white-space: nowrap;\r\n}\r\n\r\n/* 权限状态颜色 */\r\n.access-granted {\r\n    background-color: #d4edda !important;\r\n    border-left: 3px solid #28a745;\r\n}\r\n\r\n.access-denied {\r\n    background-color: #f8d7da !important;\r\n    border-left: 3px solid #dc3545;\r\n}\r\n\r\n.menu-visible {\r\n    border-right: 3px solid #007bff;\r\n}\r\n\r\n.menu-hidden {\r\n    border-right: 3px solid #6c757d;\r\n}\r\n\r\n.protected-cell {\r\n    background: #fff3cd !important;\r\n    border: 2px solid #ffc107 !important;\r\n    position: relative;\r\n}\r\n\r\n.protected-cell::before {\r\n    content: \"🛡️\";\r\n    position: absolute;\r\n    top: 2px;\r\n    right: 2px;\r\n    font-size: 12px;\r\n}\r\n\r\n/* 图例 */\r\n.legend {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 15px;\r\n    margin-bottom: 20px;\r\n    padding: 15px;\r\n    background: #f8f9fa;\r\n    border-radius: 5px;\r\n}\r\n\r\n.legend-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    font-size: 0.9em;\r\n}\r\n\r\n.legend-color {\r\n    width: 20px;\r\n    height: 15px;\r\n    border: 1px solid #dee2e6;\r\n    border-radius: 3px;\r\n}\r\n\r\n/* 过滤控制 */\r\n.filter-controls {\r\n    margin-bottom: 20px;\r\n    padding: 15px;\r\n    background: #f8f9fa;\r\n    border-radius: 5px;\r\n}\r\n\r\n/* 权限矩阵页面表格容器样式 - 使用最高优先级 */\r\n.table-responsive.permission-matrix-container.permission-matrix-container {\r\n    max-height: 80vh !important;\r\n    overflow: auto !important;\r\n    overflow-x: auto !important;\r\n    overflow-y: auto !important;\r\n    border: 1px solid #dee2e6 !important;\r\n    border-radius: 5px !important;\r\n    position: relative !important;\r\n}\r\n\r\n/* 确保权限矩阵表格的sticky定位正常工作 */\r\n.permission-matrix-container .permission-matrix .menu-name {\r\n    position: sticky !important;\r\n    left: 0 !important;\r\n    /* z-index: 5 !important; - 取消低优先级z-index */\r\n    background: #ffffff !important;\r\n}\r\n\r\n.permission-matrix-container .permission-matrix th {\r\n    position: sticky !important;\r\n    top: 0 !important;\r\n    /* z-index: 10 !important; - 取消低优先级z-index */\r\n}\r\n\r\n/* 批量操作控制 */\r\n.batch-controls {\r\n    margin-bottom: 20px;\r\n    padding: 15px;\r\n    background: #e9ecef;\r\n    border-radius: 5px;\r\n}\r\n\r\n/* 样式控制面板 */\r\n.style-controls {\r\n    margin-bottom: 20px;\r\n    padding: 15px;\r\n    background: #f0f8ff;\r\n    border: 1px solid #2e6da4;\r\n    border-radius: 5px;\r\n}\r\n\r\n.style-controls .form-group {\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.style-controls label {\r\n    font-weight: bold;\r\n    margin-right: 10px;\r\n    min-width: 100px;\r\n    display: inline-block;\r\n}\r\n\r\n.style-controls input[type=\"range\"] {\r\n    width: 150px;\r\n    margin: 0 10px;\r\n}\r\n\r\n.style-controls .value-display {\r\n    font-weight: bold;\r\n    color: #2e6da4;\r\n    min-width: 50px;\r\n    display: inline-block;\r\n}\r\n\r\n.section-title {\r\n    font-size: 1.2em;\r\n    font-weight: bold;\r\n    color: #2e6da4;\r\n    margin: 20px 0 10px 0;\r\n    padding: 10px;\r\n    background: #f8f9fa;\r\n    border-left: 4px solid #2e6da4;\r\n}\r\n\r\n.loading-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: rgba(0, 0, 0, 0.5);\r\n    display: none;\r\n    justify-content: center;\r\n    align-items: center;\r\n    z-index: var(--z-emergency);\r\n}\r\n\r\n.loading-spinner {\r\n    background: white;\r\n    padding: 20px;\r\n    border-radius: 8px;\r\n    text-align: center;\r\n}\r\n\r\n.role-stats {\r\n    background: #e9ecef;\r\n    padding: 10px;\r\n    border-radius: 5px;\r\n    margin: 10px 0;\r\n    font-size: 13px;\r\n}\r\n\r\n/* ========== admin-check-point-batch-form 页面样式 ========== */\r\n.check-point-content {\r\n    white-space: pre-wrap;\r\n    word-wrap: break-word;\r\n    overflow: visible;\r\n    height: auto !important;\r\n    min-height: 30px;\r\n    max-height: none;\r\n    line-height: 1.5;\r\n    transition: height 0.2s ease;\r\n}\r\n\r\n.template-buttons .btn {\r\n    margin-right: 5px;\r\n    margin-bottom: 5px;\r\n}\r\n\r\n.mb-2 {\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.mt-4 {\r\n    margin-top: 20px;\r\n}\r\n\r\n.template-buttons {\r\n    margin-bottom: 10px;\r\n}\r\n\r\n/* ========== admin-check-point-form 页面样式 ========== */\r\n.check-point-content {\r\n    white-space: pre-wrap;\r\n    word-wrap: break-word;\r\n    overflow: visible;\r\n    height: auto !important;\r\n    min-height: 30px;\r\n    max-height: none;\r\n    line-height: 1.5;\r\n    transition: height 0.2s ease;\r\n}\r\n\r\n/* ========== admin-check-point-import-excel 页面样式 ========== */\r\n.mb-3 {\r\n    margin-bottom: 15px;\r\n}\r\n\r\n/* ========== admin-edit-menu-item 页面样式 ========== */\r\n.form-container {\r\n    max-width: 800px;\r\n    margin: 0 auto;\r\n    padding: 20px;\r\n}\r\n\r\n.icon-preview {\r\n    display: inline-block;\r\n    width: 30px;\r\n    height: 30px;\r\n    line-height: 30px;\r\n    text-align: center;\r\n    background: #f8f9fa;\r\n    border: 1px solid #dee2e6;\r\n    border-radius: 3px;\r\n    margin-left: 10px;\r\n}\r\n\r\n.icon-selector {\r\n    display: none;\r\n    margin-top: 10px;\r\n    padding: 15px;\r\n    background: #f8f9fa;\r\n    border: 1px solid #dee2e6;\r\n    border-radius: 5px;\r\n    max-height: 300px;\r\n    overflow-y: auto;\r\n}\r\n\r\n.icon-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));\r\n    gap: 5px;\r\n}\r\n\r\n.icon-option {\r\n    width: 40px;\r\n    height: 40px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    border: 1px solid #dee2e6;\r\n    border-radius: 3px;\r\n    cursor: pointer;\r\n    transition: all 0.2s;\r\n}\r\n\r\n.icon-option:hover {\r\n    background: #007bff;\r\n    color: white;\r\n}\r\n\r\n.icon-option.selected {\r\n    background: #007bff;\r\n    color: white;\r\n    border-color: #0056b3;\r\n}\r\n\r\n.form-section {\r\n    margin-bottom: 30px;\r\n    padding: 20px;\r\n    background: #f8f9fa;\r\n    border-radius: 5px;\r\n}\r\n\r\n.form-section h4 {\r\n    margin-bottom: 15px;\r\n    color: #495057;\r\n    border-bottom: 2px solid #007bff;\r\n    padding-bottom: 5px;\r\n}\r\n\r\nbody {\r\n    font-family: Arial, sans-serif;\r\n    padding: 20px;\r\n}\r\n\r\n.preview-item {\r\n    padding: 10px;\r\n    border: 1px solid #ddd;\r\n    border-radius: 5px;\r\n    margin: 10px 0;\r\n    background: #f8f9fa;\r\n}\r\n\r\n.icon {\r\n    margin-right: 8px;\r\n}\r\n\r\n.status {\r\n    display: inline-block;\r\n    padding: 2px 8px;\r\n    border-radius: 10px;\r\n    font-size: 0.8em;\r\n    margin-left: 10px;\r\n}\r\n\r\n/* ========== admin-hazard-detail 页面样式 ========== */\r\n/* 图片查看相关样式 */\r\n.hazard-image,\r\n.rectification-image {\r\n    transition: transform 0.2s ease;\r\n    cursor: pointer;\r\n}\r\n\r\n.check-point-item {\r\n    padding: 5px 10px;\r\n    margin-bottom: 5px;\r\n    background-color: #f9f9f9;\r\n    border-left: 3px solid #337ab7;\r\n    border-radius: 3px;\r\n}\r\n\r\n.check-point-item:last-child {\r\n    margin-bottom: 0;\r\n}\r\n\r\n/* 流程步骤时间线样式 */\r\n.timeline {\r\n    position: relative;\r\n    padding: 20px 0;\r\n    list-style: none;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.timeline:after {\r\n    content: \"\";\r\n    display: table;\r\n    clear: both;\r\n}\r\n\r\n.timeline-line {\r\n    position: absolute;\r\n    top: 0;\r\n    bottom: 0;\r\n    left: 50%;\r\n    width: 4px;\r\n    margin-left: -2px;\r\n    background: #ddd;\r\n}\r\n\r\n.timeline-item {\r\n    position: relative;\r\n    margin-bottom: 30px;\r\n    clear: both;\r\n}\r\n\r\n.timeline-item:after {\r\n    content: \"\";\r\n    display: table;\r\n    clear: both;\r\n}\r\n\r\n.timeline-content {\r\n    position: relative;\r\n    width: 45%;\r\n    padding: 15px;\r\n    border-radius: 5px;\r\n    background: #f9f9f9;\r\n    border: 1px solid #ddd;\r\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.timeline-content h4 {\r\n    margin-top: 0;\r\n    color: #333;\r\n}\r\n\r\n.timeline-marker {\r\n    position: absolute;\r\n    top: 15px;\r\n    left: 50%;\r\n    margin-left: -10px;\r\n    width: 20px;\r\n    height: 20px;\r\n    border-radius: 50%;\r\n    background: #fff;\r\n    border: 4px solid #ddd;\r\n    /* z-index: 10; - 取消低优先级z-index */\r\n}\r\n\r\n/* 当前步骤样式 */\r\n.timeline-item.current .timeline-content {\r\n    background: #d9edf7;\r\n    border-color: #bce8f1;\r\n}\r\n\r\n.timeline-item.current .timeline-marker {\r\n    border-color: #31708f;\r\n}\r\n\r\n/* 已完成步骤样式 */\r\n.timeline-item.completed .timeline-content {\r\n    background: #dff0d8;\r\n    border-color: #d6e9c6;\r\n}\r\n\r\n.timeline-item.completed .timeline-marker {\r\n    border-color: #3c763d;\r\n}\r\n\r\n/* ========== admin-hazard-drafts 页面样式 ========== */\r\n.draft-item {\r\n    margin-bottom: 10px;\r\n    padding: 10px;\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n}\r\n\r\n.draft-item:hover {\r\n    background-color: #f9f9f9;\r\n}\r\n\r\n.draft-actions {\r\n    text-align: right;\r\n}\r\n\r\n.draft-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 15px;\r\n}\r\n\r\n.draft-count {\r\n    background-color: #337ab7;\r\n    color: white;\r\n    padding: 2px 8px;\r\n    border-radius: 10px;\r\n    font-size: 12px;\r\n    margin-left: 5px;\r\n}\r\n\r\n/* ========== admin-hazard-issue-review 页面样式 ========== */\r\n.hazard-info {\r\n    background-color: #f9f9f9;\r\n    padding: 20px;\r\n    border-radius: 5px;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.previous-review {\r\n    background-color: #f8d7da;\r\n    border: 1px solid #f5c6cb;\r\n    border-radius: 5px;\r\n    padding: 15px;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n/* ========== admin-hazard-rectification 页面样式 ========== */\r\n\r\n/* ========== admin-hazard-rectification-detail 页面样式 ========== */\r\n/* 图片查看相关样式 */\r\n.hazard-image,\r\n.rectification-image {\r\n    transition: transform 0.2s ease;\r\n    cursor: pointer;\r\n}\r\n\r\n/* 图片查看相关样式 */\r\n.hazard-image,\r\n.rectification-image {\r\n    transition: transform 0.2s ease;\r\n    cursor: pointer;\r\n}\r\n\r\n.thumbnail {\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n/* 按钮样式优化 */\r\n.btn.btn-sm.btn-danger.remove-image {\r\n    position: absolute;\r\n    top: 5px;\r\n    right: 5px;\r\n    /* z-index: 10; - 取消低优先级z-index */\r\n}\r\n\r\n/* ========== admin-hazard-rectification-detail-new 页面样式 ========== */\r\n/* 图片查看相关样式 */\r\n.hazard-image,\r\n.rectification-image {\r\n    transition: transform 0.2s ease;\r\n    cursor: pointer;\r\n    max-height: 200px;\r\n    width: auto;\r\n    display: block;\r\n    margin: 0 auto;\r\n}\r\n\r\n/* ========== admin-hazard-report 页面样式 ========== */\r\n.hazard-form {\r\n    background-color: #f9f9f9;\r\n    padding: 20px;\r\n    border-radius: 5px;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n/* 检查依据选择模态框样式 - 使用统一z-index变量 */\r\n#checkCategoryModal {\r\n    z-index: var(--z-modal) !important;\r\n}\r\n\r\n#checkCategoryModal .modal-backdrop {\r\n    z-index: var(--z-modal-backdrop) !important;\r\n}\r\n\r\n#checkCategoryModal .modal-dialog {\r\n    width: 90%;\r\n    max-width: 1200px;\r\n    z-index: var(--z-modal) !important;\r\n}\r\n\r\n#checkCategoryModal .modal-content {\r\n    border-radius: 6px;\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, .2);\r\n    z-index: var(--z-modal-content) !important;\r\n}\r\n\r\n/* 全局模态框层级修复 - 使用统一z-index变量 */\r\n.modal {\r\n    z-index: var(--z-modal) !important;\r\n}\r\n\r\n.modal-backdrop {\r\n    z-index: var(--z-modal-backdrop) !important;\r\n}\r\n\r\n.modal-dialog {\r\n    z-index: var(--z-modal) !important;\r\n}\r\n\r\n.modal-content {\r\n    z-index: var(--z-modal-content) !important;\r\n}\r\n\r\n#checkCategoryModal .modal-header {\r\n    background-color: #f8f8f8;\r\n    border-bottom: 2px solid #e7e7e7;\r\n    padding: 15px 20px;\r\n    border-top-left-radius: 6px;\r\n    border-top-right-radius: 6px;\r\n}\r\n\r\n.hazard-item {\r\n    border: 1px solid #ddd;\r\n    padding: 15px;\r\n    margin-bottom: 15px;\r\n    border-radius: 5px;\r\n    position: relative;\r\n}\r\n\r\n.hazard-item .close {\r\n    position: absolute;\r\n    top: 10px;\r\n    right: 10px;\r\n}\r\n\r\n.required-label:after {\r\n    content: \" *\";\r\n    color: red;\r\n}\r\n\r\n.image-preview {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin-top: 10px;\r\n}\r\n\r\n.image-preview-item {\r\n    display: inline-block;\r\n    margin-bottom: 15px;\r\n    vertical-align: top;\r\n}\r\n\r\n/* 图片查看相关样式 */\r\n.hazard-image,\r\n.rectification-image {\r\n    transition: transform 0.2s ease;\r\n    cursor: pointer;\r\n    max-height: 200px;\r\n    width: auto;\r\n    display: block;\r\n    margin: 0 auto;\r\n}\r\n\r\n.hazard-image:hover,\r\n.rectification-image:hover {\r\n    transform: scale(1.05);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.thumbnail {\r\n    position: relative;\r\n    overflow: hidden;\r\n    margin-bottom: 0;\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n    padding: 4px;\r\n    background-color: #fff;\r\n    height: 220px;\r\n    /* 固定高度，包含图片和边框 */\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.remove-image {\r\n    position: absolute;\r\n    top: 5px;\r\n    right: 5px;\r\n    background: #ff4d4f;\r\n    color: white;\r\n    border-radius: 4px;\r\n    cursor: pointer;\r\n    font-weight: bold;\r\n    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);\r\n    transition: all 0.2s ease;\r\n    /* z-index: 10; - 取消低优先级z-index */\r\n    padding: 2px 5px;\r\n    font-size: 12px;\r\n}\r\n\r\n.remove-image:hover {\r\n    background: #ff7875;\r\n    transform: scale(1.1);\r\n}\r\n\r\n.upload-image-btn {\r\n    background-color: #5cb85c;\r\n    color: white;\r\n    font-weight: bold;\r\n    padding: 6px 12px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.upload-image-btn:hover {\r\n    background-color: #4cae4c;\r\n    transform: scale(1.05);\r\n}\r\n\r\n.image-upload-container {\r\n    border: 2px dashed #5cb85c;\r\n    padding: 15px;\r\n    border-radius: 5px;\r\n    margin-top: 10px;\r\n    background-color: #f8fff8;\r\n    position: relative;\r\n}\r\n\r\n.image-upload-icon {\r\n    font-size: 24px;\r\n    color: #5cb85c;\r\n    margin-right: 10px;\r\n    vertical-align: middle;\r\n}\r\n\r\n.upload-status {\r\n    margin-top: 10px;\r\n}\r\n\r\n.draft-list {\r\n    margin-top: 20px;\r\n}\r\n\r\n/* 已选择的检查要点样式已移除，因为不再有重复显示区域 */\r\n\r\n/* 检查要点下拉框样式 */\r\n.check-point-dropdown {\r\n    position: static !important;\r\n    border: 1px solid #ccc !important;\r\n    border-radius: 4px !important;\r\n    margin-top: 5px !important;\r\n    margin-bottom: 10px !important;\r\n    background-color: #fff !important;\r\n    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;\r\n}\r\n\r\n.check-point-item {\r\n    padding: 8px 12px !important;\r\n    cursor: pointer !important;\r\n    border-bottom: 1px solid #eee !important;\r\n}\r\n\r\n.check-point-item:hover {\r\n    background-color: #f5f5f5 !important;\r\n}\r\n\r\n#checkCategoryModal .modal-title {\r\n    font-weight: bold;\r\n    color: #333;\r\n    font-size: 18px;\r\n}\r\n\r\n#checkCategoryModal .modal-body {\r\n    max-height: 70vh;\r\n    overflow-y: auto;\r\n    padding: 20px;\r\n    background-color: #fff;\r\n}\r\n\r\n#checkCategoryModal .modal-footer {\r\n    background-color: #f8f8f8;\r\n    border-top: 1px solid #e7e7e7;\r\n    padding: 15px 20px;\r\n    border-bottom-left-radius: 6px;\r\n    border-bottom-right-radius: 6px;\r\n}\r\n\r\n/* 使用更强的选择器优先级 */\r\n.modal-dialog #categoryTabs>li>a,\r\n.modal-content #categoryTabs>li>a,\r\n#checkCategoryModal #categoryTabs>li>a {\r\n    font-weight: bold !important;\r\n    color: white !important;\r\n    background-color: #337ab7 !important;\r\n    border: 1px solid #337ab7 !important;\r\n    border-bottom-color: transparent !important;\r\n    padding: 10px 20px !important;\r\n    margin-right: 5px !important;\r\n    border-top-left-radius: 6px !important;\r\n    border-top-right-radius: 6px !important;\r\n    transition: all 0.3s ease !important;\r\n    text-decoration: none !important;\r\n}\r\n\r\n.modal-dialog #categoryTabs>li>a:hover,\r\n.modal-content #categoryTabs>li>a:hover,\r\n#checkCategoryModal #categoryTabs>li>a:hover {\r\n    background-color: #286090 !important;\r\n    color: white !important;\r\n    border-color: #286090 !important;\r\n    text-decoration: none !important;\r\n}\r\n\r\n.modal-dialog #categoryTabs>li.active>a,\r\n.modal-content #categoryTabs>li.active>a,\r\n#checkCategoryModal #categoryTabs>li.active>a {\r\n    background-color: #337ab7 !important;\r\n    color: white !important;\r\n    border: 1px solid #337ab7 !important;\r\n    border-bottom-color: transparent !important;\r\n    text-decoration: none !important;\r\n}\r\n\r\n/* 选项卡样式 - 与添加专项检查页面保持一致 */\r\n#categoryTabs {\r\n    margin-bottom: 20px !important;\r\n    border-bottom: 2px solid #337ab7 !important;\r\n}\r\n\r\n#categoryTabs>li {\r\n    margin-bottom: -2px !important;\r\n}\r\n\r\n#categoryTabs>li>a {\r\n    font-weight: bold !important;\r\n    color: #555 !important;\r\n    background-color: #f5f5f5 !important;\r\n    border: 1px solid #ddd !important;\r\n    border-bottom-color: transparent !important;\r\n    padding: 10px 20px !important;\r\n    margin-right: 5px !important;\r\n    border-top-left-radius: 6px !important;\r\n    border-top-right-radius: 6px !important;\r\n    transition: all 0.3s ease !important;\r\n    text-decoration: none !important;\r\n}\r\n\r\n#categoryTabs>li>a:hover {\r\n    background-color: #e8e8e8 !important;\r\n    color: #337ab7 !important;\r\n    text-decoration: none !important;\r\n}\r\n\r\n#categoryTabs>li.active>a,\r\n#categoryTabs>li.active>a:hover,\r\n#categoryTabs>li.active>a:focus,\r\n#categoryTabs>li.active>a:link,\r\n#categoryTabs>li.active>a:visited,\r\n#categoryTabs>li.active>a:active {\r\n    background-color: #337ab7 !important;\r\n    color: white !important;\r\n    border: 1px solid #337ab7 !important;\r\n    border-bottom-color: transparent !important;\r\n    text-decoration: none !important;\r\n}\r\n\r\n/* 模态框中的选项卡样式 - 与添加专项检查页面保持一致 */\r\n#checkCategoryModal .nav-tabs {\r\n    margin-bottom: 20px !important;\r\n    border-bottom: 2px solid #337ab7 !important;\r\n}\r\n\r\n#checkCategoryModal .nav-tabs>li {\r\n    margin-bottom: -2px !important;\r\n}\r\n\r\n#checkCategoryModal .nav-tabs>li>a {\r\n    font-weight: bold !important;\r\n    color: #555 !important;\r\n    background-color: #f5f5f5 !important;\r\n    border: 1px solid #ddd !important;\r\n    border-bottom-color: transparent !important;\r\n    padding: 10px 20px !important;\r\n    margin-right: 5px !important;\r\n    border-top-left-radius: 6px !important;\r\n    border-top-right-radius: 6px !important;\r\n    transition: all 0.3s ease !important;\r\n    text-decoration: none !important;\r\n}\r\n\r\n#checkCategoryModal .nav-tabs>li>a:hover {\r\n    background-color: #e8e8e8 !important;\r\n    color: #337ab7 !important;\r\n    text-decoration: none !important;\r\n}\r\n\r\n#checkCategoryModal .nav-tabs>li.active>a,\r\n#checkCategoryModal .nav-tabs>li.active>a:hover,\r\n#checkCategoryModal .nav-tabs>li.active>a:focus,\r\n#checkCategoryModal .nav-tabs>li.active>a:link,\r\n#checkCategoryModal .nav-tabs>li.active>a:visited,\r\n#checkCategoryModal .nav-tabs>li.active>a:active {\r\n    background-color: #337ab7 !important;\r\n    color: white !important;\r\n    border: 1px solid #337ab7 !important;\r\n    border-bottom-color: transparent !important;\r\n    text-decoration: none !important;\r\n}\r\n\r\n/* 二级指标面板样式 */\r\n.category-panel {\r\n    margin-bottom: 20px;\r\n    border: 1px solid #ddd;\r\n    border-radius: 6px;\r\n    box-shadow: 0 1px 3px rgba(0, 0, 0, .1);\r\n    overflow: hidden;\r\n}\r\n\r\n.category-panel-heading {\r\n    padding: 12px 15px;\r\n    background-color: #337ab7;\r\n    border-bottom: 1px solid #ddd;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    position: relative;\r\n    color: white;\r\n}\r\n\r\n.category-panel-heading:after {\r\n    content: '\\f107';\r\n    font-family: 'FontAwesome';\r\n    position: absolute;\r\n    right: 15px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    color: white;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.category-panel-heading.active:after {\r\n    transform: translateY(-50%) rotate(180deg);\r\n}\r\n\r\n.category-panel-heading:hover {\r\n    background-color: #286090;\r\n}\r\n\r\n.category-panel-heading h4 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    color: white;\r\n    font-weight: bold;\r\n}\r\n\r\n.category-panel-body {\r\n    padding: 0;\r\n    background-color: #fff;\r\n    border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 表格样式 */\r\n.category-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    margin-bottom: 0;\r\n    border: 1px solid #ddd;\r\n}\r\n\r\n.category-table tr {\r\n    border-bottom: 1px solid #ddd;\r\n}\r\n\r\n.category-table tr:last-child {\r\n    border-bottom: none;\r\n}\r\n\r\n.category-table td {\r\n    padding: 0;\r\n    vertical-align: middle;\r\n    border-right: 1px solid #ddd;\r\n    border-bottom: 1px solid #ddd;\r\n}\r\n\r\n.category-table td:last-child {\r\n    border-right: none;\r\n}\r\n\r\n/* 表头样式 */\r\n.category-table-header {\r\n    background-color: #337ab7;\r\n    color: white;\r\n    font-weight: bold;\r\n    text-align: center;\r\n    padding: 10px !important;\r\n}\r\n\r\n/* 二级指标表头样式 */\r\n.category-level-2-header {\r\n    background-color: #337ab7;\r\n    color: white;\r\n    font-weight: bold;\r\n    text-align: left;\r\n    padding: 8px 15px !important;\r\n}\r\n\r\n/* 三级指标样式 */\r\n.category-item {\r\n    padding: 0;\r\n    box-sizing: border-box;\r\n    border: 1px solid #ddd;\r\n    width: 100%;\r\n    display: block;\r\n    margin-bottom: -1px;\r\n}\r\n\r\n/* 颜色区分 */\r\n.category-level-1 {\r\n    background-color: #337ab7;\r\n    color: white;\r\n}\r\n\r\n.category-level-2 {\r\n    background-color: #337ab7;\r\n    color: white;\r\n}\r\n\r\n.category-level-3 {\r\n    background-color: #ffffff;\r\n    color: #333;\r\n    border: 1px solid #ddd;\r\n}\r\n\r\n/* 表格行颜色 */\r\n.category-table tr:nth-child(odd) {\r\n    background-color: #f9f9f9;\r\n}\r\n\r\n.category-table tr:hover {\r\n    background-color: #f5f5f5;\r\n}\r\n\r\n/* 选中样式 */\r\n.category-item label.selected {\r\n    background-color: #d9edf7;\r\n    color: #31708f;\r\n    font-weight: bold;\r\n    border: 1px solid #bce8f1;\r\n}\r\n\r\n.category-radio {\r\n    margin-right: 5px !important;\r\n}\r\n\r\n/* 确保所有表格宽度一致 */\r\n.level-2-container {\r\n    width: 100%;\r\n}\r\n\r\n/* 确保检查依据选择框在最上层 - 使用统一z-index变量 */\r\n.form-group[style*=\"z-index: 2000\"] {\r\n    position: relative;\r\n    z-index: var(--z-loading-overlay) !important;\r\n}\r\n\r\n/* 搜索结果样式 */\r\n.category-search-result {\r\n    margin-top: 15px;\r\n    padding: 15px;\r\n    background-color: #f9f9f9;\r\n    border: 1px solid #ddd;\r\n    border-radius: 6px;\r\n    box-shadow: 0 1px 3px rgba(0, 0, 0, .1);\r\n}\r\n\r\n.category-search-result h4 {\r\n    margin-top: 0;\r\n    margin-bottom: 15px;\r\n    color: #337ab7;\r\n    font-weight: bold;\r\n    padding-bottom: 10px;\r\n    border-bottom: 1px solid #eee;\r\n}\r\n\r\n.category-search-item {\r\n    padding: 8px 12px;\r\n    border-bottom: 1px solid #eee;\r\n    transition: all 0.2s ease;\r\n}\r\n\r\n.category-search-item:last-child {\r\n    border-bottom: none;\r\n}\r\n\r\n.category-search-item:hover {\r\n    background-color: #f0f0f0;\r\n}\r\n\r\n/* 搜索框样式 */\r\n#categorySearchInput {\r\n    border-radius: 4px 0 0 4px;\r\n    height: 38px;\r\n    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);\r\n}\r\n\r\n#categorySearchBtn {\r\n    height: 38px;\r\n    background-color: #337ab7;\r\n    color: white;\r\n    border-color: #2e6da4;\r\n}\r\n\r\n#categorySearchBtn:hover {\r\n    background-color: #286090;\r\n    border-color: #204d74;\r\n}\r\n\r\n/* 确认按钮样式 */\r\n#confirmCategorySelection {\r\n    background-color: #5cb85c;\r\n    border-color: #4cae4c;\r\n    padding: 8px 20px;\r\n    font-weight: bold;\r\n}\r\n\r\n#confirmCategorySelection:hover {\r\n    background-color: #449d44;\r\n    border-color: #398439;\r\n}\r\n\r\n/* ========== admin-hazard-review 页面样式 ========== */\r\n.hazard-info {\r\n    background-color: #f9f9f9;\r\n    padding: 20px;\r\n    border-radius: 5px;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n    border-bottom: 1px solid #ddd;\r\n    padding-bottom: 10px;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.image-gallery {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin-top: 10px;\r\n}\r\n\r\n.image-item img {\r\n    width: 150px;\r\n    height: 150px;\r\n    object-fit: cover;\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n    cursor: pointer;\r\n}\r\n\r\n.review-form {\r\n    background-color: #f9f9f9;\r\n    padding: 20px;\r\n    border-radius: 5px;\r\n}\r\n\r\n.modal-image {\r\n    max-width: 100%;\r\n    max-height: 80vh;\r\n}\r\n\r\n/* ========== admin-image-browser 页面样式 ========== */\r\n.image-browser-container {\r\n    padding: 15px;\r\n}\r\n\r\n.image-item {\r\n    margin-bottom: 20px;\r\n    border: 1px solid #ddd;\r\n    padding: 10px;\r\n    border-radius: 4px;\r\n    transition: all 0.3s;\r\n}\r\n\r\n.image-preview {\r\n    height: 150px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    overflow: hidden;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.image-preview img {\r\n    max-width: 100%;\r\n    max-height: 140px;\r\n    object-fit: contain;\r\n}\r\n\r\n.image-info {\r\n    font-size: 12px;\r\n    color: #666;\r\n}\r\n\r\n.image-actions {\r\n    margin-top: 10px;\r\n}\r\n\r\n.selected {\r\n    border: 2px solid #5cb85c;\r\n    background-color: #f9fff9;\r\n}\r\n\r\n#selected-images {\r\n    margin-top: 20px;\r\n    padding: 10px;\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n    background-color: #f9f9f9;\r\n}\r\n\r\n.selected-image-item {\r\n    display: inline-block;\r\n    margin: 5px;\r\n    position: relative;\r\n}\r\n\r\n.selected-image-item img {\r\n    max-width: 100px;\r\n    max-height: 100px;\r\n    border: 1px solid #ddd;\r\n    padding: 2px;\r\n}\r\n\r\n.remove-selected {\r\n    position: absolute;\r\n    top: -10px;\r\n    right: -10px;\r\n    background-color: #d9534f;\r\n    color: white;\r\n    border-radius: 50%;\r\n    width: 20px;\r\n    height: 20px;\r\n    text-align: center;\r\n    line-height: 20px;\r\n    cursor: pointer;\r\n}\r\n\r\n/* ========== admin-image-selector 页面样式 ========== */\r\n.image-grid {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 15px;\r\n    margin-top: 15px;\r\n}\r\n\r\n.image-item {\r\n    width: 150px;\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n    padding: 10px;\r\n    cursor: pointer;\r\n    transition: all 0.3s;\r\n}\r\n\r\n.image-item:hover {\r\n    border-color: #337ab7;\r\n    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.image-item.selected {\r\n    border-color: #5cb85c;\r\n    background-color: #f9fff9;\r\n}\r\n\r\n.image-preview {\r\n    height: 100px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    overflow: hidden;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.image-preview img {\r\n    max-width: 100%;\r\n    max-height: 100px;\r\n    object-fit: contain;\r\n}\r\n\r\n.image-info {\r\n    font-size: 12px;\r\n    color: #666;\r\n    text-align: center;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n}\r\n\r\n.filter-form {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.pagination {\r\n    margin-top: 20px;\r\n}\r\n\r\n.image-search {\r\n    margin-bottom: 15px;\r\n}\r\n\r\n.image-upload-section {\r\n    margin-bottom: 20px;\r\n    padding: 15px;\r\n    border: 1px dashed #ddd;\r\n    border-radius: 4px;\r\n    background-color: #f9f9f9;\r\n}\r\n\r\n.image-upload-section h4 {\r\n    margin-top: 0;\r\n}\r\n\r\n.image-size-options {\r\n    margin-top: 15px;\r\n}\r\n\r\n.image-size-options label {\r\n    margin-right: 15px;\r\n}\r\n\r\n/* ========== admin-inspection-activities - 副本 (2) 页面样式 ========== */\r\n/* 统计卡片样式 */\r\n.stat-card {\r\n    background-color: #fff;\r\n    border-radius: 4px;\r\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);\r\n    margin-bottom: 20px;\r\n    position: relative;\r\n    overflow: hidden;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n/* 表格样式 */\r\n.table {\r\n    table-layout: fixed;\r\n    width: 100%;\r\n}\r\n\r\n/* 操作列按钮样式 */\r\n.btn-sm {\r\n    padding: 4px 8px;\r\n    font-size: 12px;\r\n    line-height: 1.3;\r\n    margin: 2px 1px;\r\n    white-space: nowrap;\r\n    border-radius: 3px;\r\n}\r\n\r\n/* 统一操作列中所有按钮的大小 */\r\n.table .col-actions .btn,\r\n.inspection-activities-table td:nth-child(9) .btn,\r\n.action-buttons .btn {\r\n    padding: 4px 8px !important;\r\n    font-size: 12px !important;\r\n    line-height: 1.3 !important;\r\n    margin: 2px 1px !important;\r\n    white-space: nowrap !important;\r\n    border-radius: 3px !important;\r\n    min-height: 26px !important; /* 确保所有按钮高度一致 */\r\n    display: inline-block !important;\r\n    vertical-align: middle !important;\r\n}\r\n\r\n/* 确保下拉菜单按钮也统一大小 */\r\n.table .col-actions .btn-group .btn,\r\n.table .col-actions .dropdown-toggle {\r\n    padding: 4px 8px !important;\r\n    font-size: 12px !important;\r\n    line-height: 1.3 !important;\r\n    min-height: 26px !important;\r\n}\r\n\r\n/* 操作列样式 */\r\ntd:last-child {\r\n    white-space: normal !important;\r\n    line-height: 1.4;\r\n}\r\n\r\n/* 草稿徽章样式 */\r\n.draft-badge {\r\n    background-color: #ff6b6b;\r\n    color: white;\r\n    border-radius: 50%;\r\n    padding: 1px 3px;\r\n    font-size: 9px;\r\n    margin-left: 3px;\r\n    min-width: 14px;\r\n    text-align: center;\r\n    display: inline-block;\r\n    line-height: 1.2;\r\n}\r\n\r\n/* 下拉菜单样式 */\r\n.btn-group {\r\n    display: inline-block;\r\n    vertical-align: top;\r\n    margin: 2px 1px;\r\n    position: relative;\r\n}\r\n\r\n/* 专门针对表格中的下拉菜单 - 使用统一z-index变量 */\r\n.table .btn-group .dropdown-menu,\r\n.table .dropdown-menu,\r\n.activity-table-row .dropdown-menu {\r\n    min-width: 140px !important;\r\n    z-index: var(--z-dropdown-menu) !important;\r\n    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175) !important;\r\n    border: 1px solid rgba(0, 0, 0, 0.15) !important;\r\n    background-color: #fff !important;\r\n    border-radius: 4px !important;\r\n    position: fixed !important;\r\n    display: block !important;\r\n}\r\n\r\n/* 确保下拉菜单在打开时可见 */\r\n.table .btn-group.open .dropdown-menu,\r\n.table .open .dropdown-menu {\r\n    display: block !important;\r\n    visibility: visible !important;\r\n    opacity: 1 !important;\r\n}\r\n\r\n/* 统一的表格容器下拉菜单支持样式 */\r\n.table-responsive:not(.permission-matrix-container):not(.search-results-container) {\r\n    overflow: visible !important;\r\n    position: static !important;\r\n}\r\n\r\n/* 确保表格行不会裁剪下拉菜单 */\r\n.table tbody tr {\r\n    position: relative;\r\n}\r\n\r\n/* 确保操作列单元格不会裁剪下拉菜单 */\r\n.table th:last-child,\r\n.table td:last-child {\r\n    min-width: 280px;\r\n    position: relative;\r\n    overflow: visible !important;\r\n}\r\n\r\n/* 确保整个页面容器不会裁剪下拉菜单 */\r\n.container-fluid {\r\n    overflow: visible !important;\r\n}\r\n\r\n/* 强制所有可能影响下拉菜单的容器都不裁剪内容 */\r\nbody,\r\nhtml {\r\n    overflow-x: visible !important;\r\n}\r\n\r\n/* 特别针对表格下拉菜单的强制样式 - 使用统一z-index变量 */\r\n.table .btn-group.open .dropdown-menu {\r\n    position: fixed !important;\r\n    z-index: var(--z-dropdown-menu) !important;\r\n    display: block !important;\r\n    visibility: visible !important;\r\n    opacity: 1 !important;\r\n    transform: none !important;\r\n    clip: none !important;\r\n    clip-path: none !important;\r\n}\r\n\r\n/* 表格标题行样式 */\r\n.table-title-row th {\r\n    background-color: #f5f5f5 !important;\r\n    border-bottom: 2px solid #ddd !important;\r\n}\r\n\r\n/* 强制下拉菜单显示在最上层 - 使用统一z-index变量 */\r\n.dropdown-menu.show,\r\n.dropdown-menu.open {\r\n    z-index: var(--z-dropdown-menu) !important;\r\n}\r\n\r\n/* ========== 通用下拉菜单样式 ========== */\r\n/* 基础下拉菜单样式 - 适用于所有页面 */\r\n.dropdown-menu {\r\n    position: absolute !important;\r\n    top: 100% !important;\r\n    left: 0 !important;\r\n    z-index: var(--z-dropdown-menu) !important;\r\n    display: none; /* 允许JavaScript控制显示/隐藏 */\r\n    float: left !important;\r\n    min-width: 160px !important;\r\n    padding: 5px 0 !important;\r\n    margin: 2px 0 0 !important;\r\n    font-size: 14px !important;\r\n    text-align: left !important;\r\n    list-style: none !important;\r\n    background-color: #fff !important;\r\n    border: 1px solid #ccc !important;\r\n    border: 1px solid rgba(0, 0, 0, .15) !important;\r\n    border-radius: 4px !important;\r\n    box-shadow: 0 6px 12px rgba(0, 0, 0, .175) !important;\r\n    background-clip: padding-box !important;\r\n}\r\n\r\n/* 下拉菜单自动位置修正 - 当菜单超出视口时自动调整 */\r\n.dropdown-menu.auto-position {\r\n    position: fixed !important;\r\n}\r\n\r\n/* 下拉菜单强制关闭样式 */\r\n.dropdown-menu.force-hidden {\r\n    display: none !important;\r\n}\r\n\r\n/* 下拉菜单显示状态 */\r\n.dropdown-menu.show,\r\n.dropdown-menu.open,\r\n.btn-group.open .dropdown-menu,\r\n.dropdown.open .dropdown-menu {\r\n    display: block !important;\r\n    visibility: visible !important;\r\n    opacity: 1 !important;\r\n}\r\n\r\n/* 右对齐的下拉菜单 */\r\n.dropdown-menu-right {\r\n    right: 0 !important;\r\n    left: auto !important;\r\n}\r\n\r\n/* 表格整体样式优化 */\r\n.table {\r\n    margin-bottom: 0 !important;\r\n    border-collapse: separate !important;\r\n    border-spacing: 0 !important;\r\n}\r\n\r\n/* 确保所有容器都不会裁剪下拉菜单 */\r\n\r\n/* ========== admin-inspection-activities - 副本 (3) 页面样式 ========== */\r\n\r\n/* ========== admin-inspection-activities - 副本 页面样式 ========== */\r\n\r\n/* 确保表格不被任何容器限制 - 但排除搜索结果容器 */\r\n.table-responsive:not(.search-results-container) {\r\n    overflow: visible !important;\r\n    position: static !important;\r\n}\r\n\r\n/* ========== admin-inspection-activities 页面样式 ========== */\r\n\r\n/* 统计卡片图标样式 */\r\n.stat-card .icon.total-icon {\r\n    background-color: #2e6da4;\r\n}\r\n\r\n.stat-card .icon.active-icon {\r\n    background-color: #5cb85c;\r\n}\r\n\r\n.stat-card .icon.completed-icon {\r\n    background-color: #5bc0de;\r\n}\r\n\r\n.stat-card .icon.cancelled-icon {\r\n    background-color: #d9534f;\r\n}\r\n\r\n.stat-card .icon i {\r\n    color: white;\r\n}\r\n\r\n/* 表格标题行样式 */\r\n.inspection-activities-table .table-title-row th {\r\n    background-color: #f5f5f5;\r\n    text-align: center;\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    padding: 15px;\r\n    position: relative;\r\n}\r\n\r\n/* 回收站入口样式 */\r\n.recycle-bin-entry {\r\n    position: absolute;\r\n    right: 15px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n}\r\n\r\n/* 回收站计数徽章样式 */\r\n.recycle-bin-entry .badge {\r\n    background-color: #d9534f;\r\n}\r\n\r\n/* 表格列宽样式 */\r\n.inspection-activities-table th.col-index { width: 5%; }\r\n.inspection-activities-table th.col-name { width: 15%; }\r\n.inspection-activities-table th.col-type { width: 10%; }\r\n.inspection-activities-table th.col-target { width: 10%; }\r\n.inspection-activities-table th.col-period { width: 15%; }\r\n.inspection-activities-table th.col-status { width: 8%; }\r\n.inspection-activities-table th.col-report { width: 10%; }\r\n.inspection-activities-table th.col-created { width: 10%; }\r\n.inspection-activities-table th.col-actions { width: 17%; }\r\n\r\n/* 检查活动表格特定对齐 */\r\n.inspection-activities-table th,\r\n.inspection-activities-table td {\r\n    vertical-align: middle !important;\r\n    padding: 12px 8px !important;\r\n}\r\n\r\n/* 序号列居中 */\r\n.inspection-activities-table td:nth-child(1) {\r\n    text-align: center;\r\n    font-weight: 500;\r\n}\r\n\r\n/* 标签列居中 */\r\n.inspection-activities-table td:nth-child(3),\r\n.inspection-activities-table td:nth-child(4),\r\n.inspection-activities-table td:nth-child(6),\r\n.inspection-activities-table td:nth-child(7) {\r\n    text-align: center;\r\n}\r\n\r\n/* 日期时间列居中 */\r\n.inspection-activities-table td:nth-child(5),\r\n.inspection-activities-table td:nth-child(8) {\r\n    text-align: center;\r\n    font-family: 'Courier New', monospace; /* 等宽字体让日期更整齐 */\r\n}\r\n\r\n/* 活动名称列左对齐 */\r\n.inspection-activities-table td:nth-child(2) {\r\n    text-align: left;\r\n    font-weight: 500;\r\n}\r\n\r\n/* 操作列居中 */\r\n.inspection-activities-table td:nth-child(9) {\r\n    text-align: center;\r\n}\r\n\r\n/* 确保所有容器都不会裁剪下拉菜单 */\r\n\r\n.panel {\r\n    overflow: visible !important;\r\n}\r\n\r\n/* 确保表格不被任何容器限制 */\r\n\r\n/* 搜索结果容器的特殊样式 */\r\n.table-responsive.search-results-container {\r\n    max-height: 400px !important;\r\n    overflow-y: auto !important;\r\n    overflow-x: hidden !important;\r\n    border: 1px solid #ddd !important;\r\n    border-radius: 4px !important;\r\n    background-color: #fff !important;\r\n    box-sizing: border-box !important;\r\n}\r\n\r\n/* 面板标题栏样式 - 已删除重复定义，使用第1052行的完整定义 */\r\n\r\n.stat-card:hover {\r\n    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);\r\n    transform: translateY(-2px);\r\n}\r\n\r\n.stat-card .icon {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 80px;\r\n    height: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 30px;\r\n}\r\n\r\n.stat-card .number {\r\n    font-size: 36px;\r\n    font-weight: bold;\r\n    margin-left: 90px;\r\n    padding-top: 15px;\r\n    padding-right: 15px;\r\n}\r\n\r\n.stat-card .title {\r\n    margin-left: 90px;\r\n    padding-bottom: 15px;\r\n    color: #777;\r\n}\r\n\r\n.search-box {\r\n    margin-bottom: 15px;\r\n}\r\n\r\n.filter-btn {\r\n    margin-left: 5px;\r\n}\r\n\r\n.filter-active {\r\n    background-color: #3498db;\r\n    color: white;\r\n}\r\n\r\n.table th,\r\n.table td {\r\n    vertical-align: middle; /* 垂直居中 */\r\n    padding: 12px 8px; /* 增加上下内边距 */\r\n    text-align: left; /* 默认左对齐 */\r\n}\r\n\r\n/* 特定列的对齐方式 - 使用更高优先级 */\r\n.table th.text-center,\r\n.table td.text-center,\r\n.table .col-id,\r\n.table .col-status,\r\n.table .col-actions,\r\n.table .col-date,\r\n.table .col-count,\r\n.table .col-number,\r\n.table .col-level,\r\n.table .col-type,\r\n.table .col-boolean,\r\n.table .col-name {\r\n    text-align: center !important; /* 居中对齐 */\r\n}\r\n\r\n/* 确保所有容器都不会裁剪下拉菜单 */\r\n\r\n.panel {\r\n    overflow: visible !important;\r\n}\r\n\r\n.panel-body {\r\n    overflow: visible !important;\r\n    position: static !important;\r\n}\r\n\r\n.table {\r\n    overflow: visible !important;\r\n}\r\n\r\n.row {\r\n    overflow: visible !important;\r\n}\r\n\r\n.col-md-12 {\r\n    overflow: visible !important;\r\n}\r\n\r\n.content-wrapper,\r\n.main-content,\r\n.page-content {\r\n    overflow: visible !important;\r\n}\r\n\r\n/* 确保表格不被任何容器限制 */\r\n\r\n.table thead th {\r\n    border-top: none !important;\r\n}\r\n\r\n.dropdown-menu li a {\r\n    padding: 6px 12px;\r\n    font-size: 12px;\r\n}\r\n\r\n.dropdown-menu li a i {\r\n    margin-right: 6px;\r\n    width: 14px;\r\n}\r\n\r\n.dropdown-menu .text-danger {\r\n    color: #d9534f !important;\r\n}\r\n\r\n.dropdown-menu .text-danger:hover {\r\n    background-color: #f5f5f5;\r\n    color: #c9302c !important;\r\n}\r\n\r\n.panel-title {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n}\r\n\r\n/* ========== admin-inspection-activity-form 页面样式 ========== */\r\n/* 日期选择器样式 - 使用统一z-index变量 */\r\n.datepicker {\r\n    z-index: var(--z-datepicker) !important;\r\n}\r\n\r\n/* 模态框背景遮罩层 - 使用统一z-index变量 */\r\n.modal-backdrop {\r\n    z-index: var(--z-modal-backdrop) !important;\r\n}\r\n\r\n.required-field label:after {\r\n    content: \" *\";\r\n    color: red;\r\n}\r\n\r\n.tab-content {\r\n    padding: 20px;\r\n    border-left: 1px solid #ddd;\r\n    border-right: 1px solid #ddd;\r\n    border-bottom: 1px solid #ddd;\r\n    border-radius: 0 0 4px 4px;\r\n}\r\n\r\n.nav-tabs {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.personnel-list,\r\n.laboratory-list {\r\n    max-height: 300px;\r\n    overflow-y: auto;\r\n    border: 1px solid #ddd;\r\n    padding: 10px;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n/* 二级指标表格 */\r\n.level-2-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    margin-bottom: 15px;\r\n    border: 1px solid #ddd;\r\n    table-layout: fixed;\r\n}\r\n\r\n/* 二级指标标题 */\r\n.level-2-header {\r\n    background-color: #337ab7 !important;\r\n    color: white !important;\r\n    font-weight: bold;\r\n    padding: 8px 15px;\r\n    text-align: left;\r\n    border-bottom: 1px solid #ddd;\r\n}\r\n\r\n/* 表格单元格通用样式 */\r\n.level-2-table th,\r\n.level-2-table td {\r\n    border: 1px solid #ddd;\r\n}\r\n\r\n/* 三级指标容器 */\r\n.level-3-container {\r\n    padding: 0;\r\n}\r\n\r\n/* 三级指标项 */\r\n.level-3-item {\r\n    padding: 8px 15px;\r\n    background-color: #fff;\r\n    vertical-align: middle;\r\n}\r\n\r\n/* 脉冲动画效果 */\r\n@keyframes pulse {\r\n    0% {\r\n        transform: scale(1);\r\n        box-shadow: 0 0 0 0 rgba(51, 122, 183, 0.4);\r\n    }\r\n\r\n    70% {\r\n        transform: scale(1.05);\r\n        box-shadow: 0 0 0 10px rgba(51, 122, 183, 0);\r\n    }\r\n\r\n    100% {\r\n        transform: scale(1);\r\n        box-shadow: 0 0 0 0 rgba(51, 122, 183, 0);\r\n    }\r\n}\r\n\r\n.pulse-animation {\r\n    animation: pulse 0.5s ease-in-out;\r\n}\r\n\r\n.personnel-item,\r\n.laboratory-item {\r\n    margin-bottom: 5px;\r\n    padding: 5px;\r\n    border-bottom: 1px solid #eee;\r\n}\r\n\r\n.personnel-item:last-child,\r\n.laboratory-item:last-child {\r\n    border-bottom: none;\r\n}\r\n\r\n.filter-section {\r\n    margin-bottom: 15px;\r\n    padding: 10px;\r\n    background-color: #f5f5f5;\r\n    border-radius: 4px;\r\n}\r\n\r\n/* 检查依据和检查要点样式 */\r\n#selectedCategoriesContainer,\r\n#selectedCheckPointsContainer {\r\n    min-height: 80px;\r\n    max-height: 200px;\r\n    overflow-y: auto;\r\n}\r\n\r\n.label {\r\n    display: inline-block;\r\n    margin: 5px;\r\n    padding: 5px 10px;\r\n    font-size: 14px;\r\n}\r\n\r\n.label a {\r\n    color: white;\r\n    margin-left: 5px;\r\n    text-decoration: none;\r\n}\r\n\r\n.label a:hover {\r\n    color: #f0f0f0;\r\n}\r\n\r\n/* ========== admin-inspection-check 页面样式 ========== */\r\n/* 检查依据和检查要点相关样式 */\r\n.selected-check-points-container {\r\n    margin-top: 10px !important;\r\n    border: 1px solid #eee !important;\r\n    border-radius: 4px !important;\r\n    padding: 10px !important;\r\n    background-color: #f9f9f9 !important;\r\n    min-height: 100px !important;\r\n    max-height: 200px !important;\r\n    overflow-y: auto !important;\r\n    display: block !important;\r\n}\r\n\r\n/* 检查依据和检查要点相关样式 */\r\n.selected-check-points-container {\r\n    margin-top: 10px !important;\r\n    border: 1px solid #eee !important;\r\n    border-radius: 4px !important;\r\n    padding: 10px !important;\r\n    background-color: #f9f9f9 !important;\r\n    min-height: 100px !important;\r\n    max-height: 200px !important;\r\n    overflow-y: auto !important;\r\n    display: block !important;\r\n}\r\n\r\n.selected-check-point {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    margin-right: 10px;\r\n    margin-bottom: 10px;\r\n    padding: 5px 10px;\r\n    background-color: #fff;\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n}\r\n\r\n.selected-check-point .badge {\r\n    display: inline-block;\r\n    padding: 3px 7px;\r\n    font-size: 12px;\r\n    font-weight: 700;\r\n    line-height: 1;\r\n    color: #fff;\r\n    text-align: center;\r\n    white-space: nowrap;\r\n    vertical-align: middle;\r\n    background-color: #337ab7;\r\n    border-radius: 10px;\r\n}\r\n\r\n.selected-check-point .remove-selected-point {\r\n    margin-left: 5px;\r\n    color: #d9534f;\r\n    cursor: pointer;\r\n}\r\n\r\n.selected-check-point .remove-selected-point:hover {\r\n    color: #c9302c;\r\n}\r\n\r\n/* 检查依据选择相关样式 */\r\n.selected-category-item {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    margin-right: 10px;\r\n    margin-bottom: 10px;\r\n    padding: 5px 10px;\r\n    background-color: #fff;\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n}\r\n\r\n.selected-category-item .badge {\r\n    display: inline-block;\r\n    padding: 3px 7px;\r\n    font-size: 12px;\r\n    font-weight: 700;\r\n    line-height: 1;\r\n    color: #fff;\r\n    text-align: center;\r\n    white-space: nowrap;\r\n    vertical-align: middle;\r\n    background-color: #5bc0de;\r\n    border-radius: 10px;\r\n}\r\n\r\n.selected-category-item .remove-selected-category {\r\n    margin-left: 5px;\r\n    color: #d9534f;\r\n    cursor: pointer;\r\n}\r\n\r\n.selected-category-item .remove-selected-category:hover {\r\n    color: #c9302c;\r\n}\r\n\r\n/* 选项卡样式 */\r\n#categoryTabs {\r\n    margin-bottom: 20px;\r\n    border-bottom: 2px solid #337ab7;\r\n}\r\n\r\n#categoryTabs>li {\r\n    margin-bottom: -2px;\r\n}\r\n\r\n#categoryTabs>li>a {\r\n    font-weight: bold;\r\n    color: #555;\r\n    background-color: #f5f5f5;\r\n    border: 1px solid #ddd;\r\n    border-bottom-color: transparent;\r\n    padding: 10px 20px;\r\n    margin-right: 5px;\r\n    border-top-left-radius: 6px;\r\n    border-top-right-radius: 6px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n#categoryTabs>li>a:hover {\r\n    background-color: #e8e8e8;\r\n    color: #337ab7;\r\n}\r\n\r\n#categoryTabs>li.active>a {\r\n    background-color: #337ab7;\r\n    color: white;\r\n    border: 1px solid #337ab7;\r\n    border-bottom-color: transparent;\r\n}\r\n\r\n/* 检查依据模态框内容区域最大高度 */\r\n#categoryTabContent {\r\n    max-height: 400px;\r\n    overflow-y: auto;\r\n}\r\n\r\n/* 二级指标容器 */\r\n.level-2-container {\r\n    margin-bottom: 15px;\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n}\r\n\r\n.level-2-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    margin: 0;\r\n}\r\n\r\n.level-2-header {\r\n    background-color: #337ab7 !important;\r\n    color: white !important;\r\n    font-weight: bold;\r\n    padding: 10px 15px;\r\n    text-align: left;\r\n}\r\n\r\n.level-3-item {\r\n    padding: 8px 15px;\r\n    border-bottom: 1px solid #eee;\r\n    transition: background-color 0.2s ease;\r\n}\r\n\r\n.level-3-item:hover {\r\n    background-color: #f5f5f5;\r\n}\r\n\r\n.level-3-item label {\r\n    display: block;\r\n    margin: 0;\r\n    cursor: pointer;\r\n    font-weight: normal;\r\n    width: 100%;\r\n}\r\n\r\n.level-3-item label.selected {\r\n    font-weight: bold;\r\n    color: #337ab7;\r\n}\r\n\r\n/* ========== admin-inspection-clauses 页面样式 ========== */\r\n.check-points-container {\r\n    padding: 0;\r\n    margin: 0;\r\n}\r\n\r\n.check-point-item {\r\n    margin-bottom: 5px;\r\n}\r\n\r\n.check-point-item .input-group {\r\n    width: 100%;\r\n}\r\n\r\n.check-point-item .input-group-addon {\r\n    background-color: #f8f8f8;\r\n    color: #5cb85c;\r\n}\r\n\r\n.check-point-item .form-control {\r\n    height: auto;\r\n    min-height: 30px;\r\n    padding: 5px 10px;\r\n    background-color: #f9f9f9;\r\n    border-color: #ddd;\r\n}\r\n\r\n.mt-1 {\r\n    margin-top: 5px;\r\n}\r\n\r\n.mt-2 {\r\n    margin-top: 10px;\r\n}\r\n\r\n/* ========== admin-issue-review 页面样式 ========== */\r\n\r\n/* 筛选样式 */\r\n.bootstrap-select .dropdown-menu {\r\n    max-height: 400px !important;\r\n}\r\n\r\n.bootstrap-select .dropdown-menu li a {\r\n    white-space: normal;\r\n    word-wrap: break-word;\r\n}\r\n\r\n#advancedSearch .form-group {\r\n    margin-bottom: 10px;\r\n}\r\n\r\n/* 修复下拉框重叠问题 - 使用统一z-index变量 */\r\n.bootstrap-select .dropdown-menu {\r\n    z-index: var(--z-select) !important;\r\n    /* 确保下拉菜单在最上层 */\r\n    position: fixed !important;\r\n    /* 使用固定定位，不受父元素影响 */\r\n    max-height: 300px !important;\r\n    /* 限制最大高度 */\r\n    overflow-y: auto !important;\r\n    /* 允许垂直滚动 */\r\n    width: auto !important;\r\n    /* 自适应宽度 */\r\n    min-width: 200px !important;\r\n    /* 最小宽度 */\r\n}\r\n\r\n/* 确保下拉框内容不被截断 */\r\n.bootstrap-select .dropdown-menu li a {\r\n    white-space: normal !important;\r\n    word-wrap: break-word !important;\r\n    padding: 8px 10px !important;\r\n    line-height: 1.4 !important;\r\n}\r\n\r\n/* 确保下拉框有足够的空间展开 */\r\n#filterForm .form-group {\r\n    margin-bottom: 30px !important;\r\n    position: relative !important;\r\n}\r\n\r\n/* 确保下拉框按钮样式正确 */\r\n.bootstrap-select .btn {\r\n    white-space: normal !important;\r\n    overflow: hidden !important;\r\n    text-overflow: ellipsis !important;\r\n}\r\n\r\n/* 修复下拉框位置问题 */\r\n.bootstrap-select.btn-group .dropdown-menu {\r\n    margin-top: 5px !important;\r\n}\r\n\r\n.nav-tabs {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n#filterForm {\r\n    margin-bottom: 15px;\r\n}\r\n\r\n/* 排序图标 */\r\nth[data-sort] {\r\n    cursor: pointer;\r\n}\r\n\r\n/* ========== admin-laboratories 页面样式 ========== */\r\n/* 实验室属性下拉多选框样式 */\r\n.property-dropdown {\r\n    width: 100%;\r\n    position: relative;\r\n}\r\n\r\n.property-dropdown .dropdown-toggle {\r\n    text-align: left;\r\n    width: 100%;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n}\r\n\r\n.property-dropdown-menu {\r\n    padding: 10px;\r\n    width: 100%;\r\n    max-height: none;\r\n    overflow: visible;\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    box-shadow: 0 6px 12px rgba(0, 0, 0, .175);\r\n    background-color: #fff;\r\n    border: 1px solid rgba(0, 0, 0, .15);\r\n    border-radius: 4px;\r\n    z-index: var(--z-dropdown-menu);\r\n}\r\n\r\n/* 确保下拉框在打开状态下正确显示 */\r\n.property-dropdown.open .property-dropdown-menu {\r\n    display: block;\r\n}\r\n\r\n/* 确保下拉框在关闭状态下不显示 */\r\n.property-dropdown:not(.open) .property-dropdown-menu {\r\n    display: none;\r\n}\r\n\r\n.property-items-wrapper {\r\n    max-height: 250px;\r\n    overflow-y: auto;\r\n    padding: 5px;\r\n    width: 100%;\r\n}\r\n\r\n/* 修复复选框内容左对齐 */\r\n.property-items-wrapper .checkbox {\r\n    padding-left: 0;\r\n    margin-left: 0;\r\n}\r\n\r\n.property-items-wrapper .checkbox label {\r\n    padding-left: 20px;\r\n    margin-left: 0;\r\n    width: 100%;\r\n}\r\n\r\n.dropdown-header {\r\n    padding: 5px;\r\n    font-size: 14px;\r\n    color: #333;\r\n}\r\n\r\n.dropdown-header .checkbox {\r\n    margin: 0;\r\n}\r\n\r\n.property-dropdown-menu .checkbox {\r\n    margin-top: 5px;\r\n    margin-bottom: 5px;\r\n    display: block;\r\n}\r\n\r\n.property-dropdown-menu label {\r\n    font-weight: normal;\r\n    cursor: pointer;\r\n    display: block;\r\n    padding: 3px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    position: relative;\r\n}\r\n\r\n.property-dropdown-menu label:hover {\r\n    background-color: #eaf2fa;\r\n    border-radius: 3px;\r\n}\r\n\r\n/* 添加一些间距和边框效果 */\r\n.form-group {\r\n    margin-bottom: 15px;\r\n}\r\n\r\n/* ========== 统一复选框样式 - 全项目通用 ========== */\r\n/* 基础复选框容器样式 */\r\n.checkbox {\r\n    position: relative;\r\n    display: block;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    padding-left: 20px;\r\n}\r\n\r\n/* 复选框输入框样式 */\r\n.checkbox input[type=\"checkbox\"] {\r\n    position: absolute;\r\n    margin-top: 4px;\r\n    margin-left: -20px;\r\n}\r\n\r\n/* 复选框标签样式 */\r\n.checkbox label {\r\n    min-height: 20px;\r\n    padding-left: 0;\r\n    margin-bottom: 0;\r\n    font-weight: normal;\r\n    cursor: pointer;\r\n    display: inline;\r\n}\r\n\r\n/* 选中状态的高亮效果 */\r\n.property-dropdown .btn-has-selection {\r\n    border-color: #66afe9;\r\n    background-color: #f0f7fd;\r\n}\r\n\r\n/* 改进表单控件样式 */\r\n.form-control:focus {\r\n    border-color: #66afe9;\r\n    outline: 0;\r\n    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);\r\n}\r\n\r\n/* 防止点击复选框关闭下拉菜单 */\r\n.dropdown-menu .checkbox {\r\n    position: relative;\r\n    /* z-index: 10; - 取消低优先级z-index */\r\n}\r\n\r\n/* ========== admin-laboratory-info-card 页面样式 ========== */\r\n.nav-tabs-custom {\r\n    margin-bottom: 20px;\r\n    background: #fff;\r\n    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\r\n    border-radius: 3px;\r\n}\r\n\r\n.nav-tabs-custom>.nav-tabs {\r\n    margin: 0;\r\n    border-bottom-color: #f4f4f4;\r\n}\r\n\r\n.nav-tabs-custom>.nav-tabs>li {\r\n    margin-right: 5px;\r\n}\r\n\r\n.nav-tabs-custom>.nav-tabs>li>a {\r\n    color: #444;\r\n    border-radius: 0;\r\n}\r\n\r\n.nav-tabs-custom>.nav-tabs>li.active {\r\n    border-top-color: #3c8dbc;\r\n}\r\n\r\n.nav-tabs-custom>.nav-tabs>li.active>a {\r\n    border-top-color: transparent;\r\n    border-left-color: #f4f4f4;\r\n    border-right-color: #f4f4f4;\r\n}\r\n\r\n.nav-tabs-custom>.tab-content {\r\n    background: #fff;\r\n    padding: 10px;\r\n    border-bottom-right-radius: 3px;\r\n    border-bottom-left-radius: 3px;\r\n}\r\n\r\n.template-item.active .panel {\r\n    border-color: #3c8dbc;\r\n    box-shadow: 0 0 8px rgba(60, 141, 188, 0.5);\r\n}\r\n\r\n/* ========== admin-laboratory-info-card-preview 页面样式 ========== */\r\n/* 预览容器样式 */\r\n.preview-container {\r\n    background-color: white;\r\n    padding: 20px;\r\n    border: 1px solid #ddd;\r\n    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\r\n    margin-bottom: 20px;\r\n    font-family: '宋体小四号等宽', '宋体', 'SimSun', 'Times New Roman', serif;\r\n    font-size: 12pt;\r\n    line-height: 1.5;\r\n    color: #000;\r\n    width: 210mm;\r\n    /* A4纸宽度 */\r\n    min-height: 297mm;\r\n    /* A4纸高度 */\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n    overflow: auto;\r\n}\r\n\r\n/* 字体大小 */\r\n.preview-container span[style*=\"font-size:12.0000pt\"],\r\n.preview-container span[style*=\"font-size:12pt\"] {\r\n    font-size: 12pt !important;\r\n}\r\n\r\n/* 加粗 */\r\n.preview-container b,\r\n.preview-container span[style*=\"font-weight:bold\"] {\r\n    font-weight: bold !important;\r\n}\r\n\r\n/* 其他样式覆盖 */\r\n.preview-container span[style*=\"font-family\"] {\r\n    font-family: inherit !important;\r\n}\r\n\r\n/* 表格内容垂直居中 */\r\n.preview-container td[style*=\"vertical-align: middle\"] {\r\n    vertical-align: middle !important;\r\n}\r\n\r\n/* 表格内容水平居中 */\r\n.preview-container td[style*=\"text-align: center\"],\r\n.preview-container p[style*=\"text-align:center\"] {\r\n    text-align: center !important;\r\n}\r\n\r\n/* 表格内容加粗 */\r\n.preview-container td[style*=\"font-weight: bold\"] {\r\n    font-weight: bold !important;\r\n}\r\n\r\n/* 表格内容字体大小 */\r\n.preview-container td[style*=\"font-size\"] {\r\n    font-size: inherit !important;\r\n}\r\n\r\n/* 预览容器样式 */\r\n.preview-container {\r\n    background-color: white;\r\n    padding: 20px;\r\n    border: 1px solid #ddd;\r\n    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\r\n    margin-bottom: 20px;\r\n    font-family: '宋体小四号等宽', '宋体', 'SimSun', 'Times New Roman', serif;\r\n    font-size: 12pt;\r\n    line-height: 1.5;\r\n    color: #000;\r\n    width: 210mm;\r\n    /* A4纸宽度 */\r\n    min-height: 297mm;\r\n    /* A4纸高度 */\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n    overflow: auto;\r\n}\r\n\r\n/* 表格样式 */\r\n.preview-container table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    margin-bottom: 15px;\r\n    table-layout: fixed;\r\n    border: 1px solid #000;\r\n}\r\n\r\n.preview-container table,\r\n.preview-container th,\r\n.preview-container td {\r\n    border: 1px solid #000;\r\n}\r\n\r\n.preview-container th,\r\n.preview-container td {\r\n    padding: 8px;\r\n    word-wrap: break-word;\r\n    overflow-wrap: break-word;\r\n    position: relative;\r\n}\r\n\r\n/* 表格单元格内容垂直居中 */\r\n.preview-container td {\r\n    vertical-align: middle;\r\n}\r\n\r\n/* 表格单元格内容水平居中 */\r\n.preview-container td p {\r\n    text-align: center;\r\n    margin: 0;\r\n}\r\n\r\n/* 图片样式 */\r\n.preview-container img {\r\n    max-width: 100%;\r\n    height: auto;\r\n    display: block;\r\n    margin: 0 auto;\r\n}\r\n\r\n/* 段落样式 */\r\n.preview-container p {\r\n    margin: 0 0 10px 0;\r\n    padding: 0;\r\n}\r\n\r\n/* 标题样式 */\r\n.preview-container h1,\r\n.preview-container h2,\r\n.preview-container h3,\r\n.preview-container h4,\r\n.preview-container h5,\r\n.preview-container h6 {\r\n    font-weight: bold;\r\n    margin: 10px 0;\r\n    text-align: center;\r\n}\r\n\r\n.preview-container h1 {\r\n    font-size: 24pt;\r\n}\r\n\r\n.preview-container h2 {\r\n    font-size: 18pt;\r\n}\r\n\r\n.preview-container h3 {\r\n    font-size: 16pt;\r\n}\r\n\r\n.preview-container h4 {\r\n    font-size: 14pt;\r\n}\r\n\r\n.preview-container h5 {\r\n    font-size: 12pt;\r\n}\r\n\r\n.preview-container h6 {\r\n    font-size: 10pt;\r\n}\r\n\r\n/* 打印样式 */\r\n@media print {\r\n    .no-print {\r\n        display: none !important;\r\n    }\r\n\r\n    body {\r\n        margin: 0;\r\n        padding: 0;\r\n        background-color: white;\r\n    }\r\n\r\n    .preview-container {\r\n        border: none;\r\n        box-shadow: none;\r\n        padding: 0;\r\n        margin: 0;\r\n        width: 210mm;\r\n        height: 297mm;\r\n        font-size: 12pt;\r\n        overflow: hidden;\r\n    }\r\n\r\n    /* A4纸张大小设置 */\r\n    @page {\r\n        size: A4 portrait;\r\n        margin: 1cm;\r\n    }\r\n\r\n    /* 每页显示2个信息牌 */\r\n    .preview-container {\r\n        page-break-inside: avoid;\r\n        page-break-after: always;\r\n    }\r\n\r\n    /* 确保表格不会被分页 */\r\n    .preview-container table {\r\n        page-break-inside: avoid;\r\n    }\r\n}\r\n\r\n/* 实验室信息牌特定样式 */\r\n/* 蓝色背景单元格 - 四级风险 */\r\n.preview-container .blue-bg,\r\n.preview-container td[style*=\"background:rgb(0,112,192)\"],\r\n.preview-container td[style*=\"background-color:rgb(0,112,192)\"] {\r\n    background-color: #0070C0 !important;\r\n    color: white !important;\r\n    font-weight: bold !important;\r\n}\r\n\r\n/* 深蓝色背景单元格 - 标题行 */\r\n.preview-container .dark-blue-bg,\r\n.preview-container td[style*=\"background:rgb(47,49,139)\"],\r\n.preview-container td[style*=\"background-color:rgb(47,49,139)\"] {\r\n    background-color: #2F318B !important;\r\n    color: white !important;\r\n    font-weight: bold !important;\r\n}\r\n\r\n/* 绿色背景单元格 - 紧急电话 */\r\n.preview-container .green-bg,\r\n.preview-container td[style*=\"background:rgb(90,165,114)\"],\r\n.preview-container td[style*=\"background-color:rgb(90,165,114)\"] {\r\n    background-color: #5AA572 !important;\r\n    color: white !important;\r\n    font-weight: bold !important;\r\n}\r\n\r\n/* 红色背景单元格 - 危险类别 */\r\n.preview-container .red-bg,\r\n.preview-container td[style*=\"background:rgb(255,0,0)\"],\r\n.preview-container td[style*=\"background-color:rgb(255,0,0)\"] {\r\n    background-color: #FF0000 !important;\r\n    color: white !important;\r\n    font-weight: bold !important;\r\n}\r\n\r\n/* 橙色背景单元格 - 二级风险 */\r\n.preview-container .orange-bg,\r\n.preview-container td[style*=\"background:rgb(237,125,49)\"],\r\n.preview-container td[style*=\"background-color:rgb(237,125,49)\"] {\r\n    background-color: #ED7D31 !important;\r\n    color: white !important;\r\n    font-weight: bold !important;\r\n}\r\n\r\n/* 黄色背景单元格 - 三级风险 */\r\n.preview-container .yellow-bg,\r\n.preview-container td[style*=\"background:rgb(255,255,0)\"],\r\n.preview-container td[style*=\"background-color:rgb(255,255,0)\"] {\r\n    background-color: #FFFF00 !important;\r\n    color: black !important;\r\n    font-weight: bold !important;\r\n}\r\n\r\n/* 双线边框 */\r\n.preview-container td[style*=\"border-left:1.0000pt double\"],\r\n.preview-container td[style*=\"border-left:1pt double\"] {\r\n    border-left: 3px double #000 !important;\r\n}\r\n\r\n.preview-container td[style*=\"border-right:1.0000pt double\"],\r\n.preview-container td[style*=\"border-right:1pt double\"] {\r\n    border-right: 3px double #000 !important;\r\n}\r\n\r\n.preview-container td[style*=\"border-top:1.0000pt double\"],\r\n.preview-container td[style*=\"border-top:1pt double\"] {\r\n    border-top: 3px double #000 !important;\r\n}\r\n\r\n.preview-container td[style*=\"border-bottom:1.0000pt double\"],\r\n.preview-container td[style*=\"border-bottom:1pt double\"] {\r\n    border-bottom: 3px double #000 !important;\r\n}\r\n\r\n/* 字间距 */\r\n.preview-container span[style*=\"letter-spacing:1.5000pt\"],\r\n.preview-container span[style*=\"letter-spacing:1.5pt\"] {\r\n    letter-spacing: 1.5pt !important;\r\n}\r\n\r\n/* 占位符样式 */\r\n.preview-container span[style*=\"color:rgb(199,37,78)\"],\r\n.preview-container span[style*=\"background:rgb(249,242,244)\"] {\r\n    color: #C7254E !important;\r\n    background-color: #F9F2F4 !important;\r\n    font-family: Consolas, monospace !important;\r\n    font-size: 9pt !important;\r\n}\r\n\r\n/* 微软雅黑字体 */\r\n.preview-container span[style*=\"font-family:微软雅黑\"],\r\n.preview-container span[style*=\"font-family:微软雅黑\"] {\r\n    font-family: '微软雅黑', 'Microsoft YaHei', sans-serif !important;\r\n}\r\n\r\n/* 宋体小四号等宽字体 */\r\n.preview-container span[style*=\"font-family:宋体小四号等宽\"],\r\n.preview-container span[style*=\"font-family:宋体小四号等宽\"] {\r\n    font-family: '宋体小四号等宽', '宋体', 'SimSun', serif !important;\r\n}\r\n\r\n.preview-container span[style*=\"font-size:10.5000pt\"],\r\n.preview-container span[style*=\"font-size:10.5pt\"] {\r\n    font-size: 10.5pt !important;\r\n}\r\n\r\n.preview-container span[style*=\"font-size:9.0000pt\"],\r\n.preview-container span[style*=\"font-size:9pt\"] {\r\n    font-size: 9pt !important;\r\n}\r\n\r\n/* ========== admin-laboratory-properties 页面样式 ========== */\r\n.checkbox-list {\r\n    max-height: 300px;\r\n    overflow-y: auto;\r\n    border: 1px solid #ddd;\r\n    padding: 10px;\r\n    border-radius: 4px;\r\n}\r\n\r\n/* ========== admin-lab-safety-card 页面样式 ========== */\r\n@page {\r\n    size: A4;\r\n    margin: 0;\r\n}\r\n\r\n.risk-level-1 {\r\n    background-color: #f44336;\r\n    /* 红色 - 一级 */\r\n    color: white;\r\n}\r\n\r\n.risk-level-2 {\r\n    background-color: #ff9800;\r\n    /* 橙色 - 二级 */\r\n    color: black;\r\n}\r\n\r\n.risk-level-3 {\r\n    background-color: #ffeb3b;\r\n    /* 黄色 - 三级 */\r\n    color: black;\r\n}\r\n\r\n.risk-level-4 {\r\n    background-color: #2196f3;\r\n    /* 蓝色 - 四级 */\r\n    color: white;\r\n}\r\n\r\n@page {\r\n    size: A4;\r\n    margin: 0;\r\n}\r\n\r\nbody {\r\n    margin: 0;\r\n    padding: 0;\r\n    font-family: \"Microsoft YaHei\", \"SimHei\", sans-serif;\r\n}\r\n\r\n.page {\r\n    width: 210mm;\r\n    min-height: 297mm;\r\n    padding: 5mm;\r\n    margin: 0 auto;\r\n    background: white;\r\n    box-sizing: border-box;\r\n    page-break-after: always;\r\n}\r\n\r\n.card-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n    height: 100%;\r\n}\r\n\r\n.safety-card {\r\n    width: 100%;\r\n    margin-bottom: 10mm;\r\n    border: 2px solid #333;\r\n}\r\n\r\n.card-header {\r\n    background-color: #2e3192;\r\n    color: white;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 5px 10px;\r\n    height: 80px;\r\n}\r\n\r\n.card-title {\r\n    font-size: 28px;\r\n    font-weight: bold;\r\n    text-align: center;\r\n    flex-grow: 1;\r\n}\r\n\r\n.card-logo {\r\n    width: 60px;\r\n    height: 60px;\r\n}\r\n\r\n.room-number {\r\n    border: 2px solid white;\r\n    padding: 5px 10px;\r\n    border-radius: 5px;\r\n    font-size: 18px;\r\n    width: 80px;\r\n    text-align: center;\r\n}\r\n\r\n.info-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n}\r\n\r\n.info-table td {\r\n    border: 1px solid #333;\r\n    padding: 5px;\r\n    text-align: center;\r\n    height: 35px;\r\n}\r\n\r\n.info-table .label {\r\n    background-color: #2e3192;\r\n    color: white;\r\n    width: 25%;\r\n}\r\n\r\n.info-table .value {\r\n    width: 25%;\r\n    background-color: white;\r\n}\r\n\r\n.emergency-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    margin-top: -1px;\r\n}\r\n\r\n.emergency-table td {\r\n    border: 1px solid #333;\r\n    padding: 5px;\r\n    text-align: center;\r\n    height: 35px;\r\n}\r\n\r\n.emergency-table .label {\r\n    background-color: #4caf50;\r\n    color: white;\r\n    width: 12.5%;\r\n}\r\n\r\n.emergency-table .value {\r\n    width: 12.5%;\r\n    background-color: white;\r\n}\r\n\r\n.hazard-container {\r\n    display: flex;\r\n    width: 100%;\r\n    margin-top: -1px;\r\n}\r\n\r\n.hazard-left {\r\n    width: 60%;\r\n}\r\n\r\n.hazard-right {\r\n    width: 40%;\r\n}\r\n\r\n.hazard-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    height: 100%;\r\n}\r\n\r\n.hazard-table td {\r\n    border: 1px solid #333;\r\n    padding: 5px;\r\n    height: 60px;\r\n    vertical-align: middle;\r\n}\r\n\r\n.hazard-table tr {\r\n    height: 33.33%;\r\n}\r\n\r\n.hazard-label {\r\n    background-color: #f44336;\r\n    color: white;\r\n    width: 20%;\r\n    text-align: center;\r\n    font-weight: bold;\r\n}\r\n\r\n.hazard-content {\r\n    width: 80%;\r\n    background-color: white;\r\n}\r\n\r\n.precaution-label {\r\n    background-color: #ffeb3b;\r\n    color: black;\r\n    width: 20%;\r\n    text-align: center;\r\n    font-weight: bold;\r\n}\r\n\r\n.protection-label {\r\n    background-color: #4caf50;\r\n    color: white;\r\n    width: 20%;\r\n    text-align: center;\r\n    font-weight: bold;\r\n}\r\n\r\n.fire-points-container {\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.fire-points {\r\n    background-color: #ffeb3b;\r\n    color: black;\r\n    border: 1px solid #333;\r\n    padding: 5px;\r\n    flex-grow: 1;\r\n    margin-top: -1px;\r\n}\r\n\r\n.fire-points-title {\r\n    background-color: #f44336;\r\n    color: white;\r\n    text-align: center;\r\n    padding: 5px;\r\n    font-weight: bold;\r\n    border: 1px solid #333;\r\n    margin: 0;\r\n}\r\n\r\n.fire-points-list {\r\n    list-style-type: none;\r\n    padding: 0;\r\n    margin: 0;\r\n}\r\n\r\n.fire-points-list li {\r\n    margin: 8px 0;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n/* 删除了特定页面的复选框样式，使用统一的全局样式 */\r\n\r\n.card-footer {\r\n    background-color: #2e3192;\r\n    color: white;\r\n    text-align: center;\r\n    padding: 5px;\r\n    font-size: 14px;\r\n    margin-top: -1px;\r\n}\r\n\r\n/* 打印样式 */\r\n@media print {\r\n    .no-print {\r\n        display: none;\r\n    }\r\n\r\n    body {\r\n        margin: 0;\r\n        padding: 0;\r\n    }\r\n\r\n    .page {\r\n        margin: 0;\r\n        padding: 5mm;\r\n        box-shadow: none;\r\n    }\r\n}\r\n\r\n/* ========== admin-management-button-config 页面样式 ========== */\r\n.page-selector {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.button-config-panel {\r\n    border: 1px solid #ddd;\r\n    border-radius: 5px;\r\n    margin-bottom: 15px;\r\n    padding: 15px;\r\n    background: #f9f9f9;\r\n}\r\n\r\n.button-config-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.button-preview {\r\n    border: 1px solid #ddd;\r\n    border-radius: 5px;\r\n    padding: 10px;\r\n    margin: 10px 0;\r\n    background: white;\r\n}\r\n\r\n.drag-handle {\r\n    cursor: move;\r\n    color: #999;\r\n    margin-right: 10px;\r\n}\r\n\r\n.button-controls {\r\n    display: flex;\r\n    gap: 5px;\r\n}\r\n\r\n.color-selector {\r\n    display: inline-block;\r\n    width: 30px;\r\n    height: 30px;\r\n    border-radius: 3px;\r\n    cursor: pointer;\r\n    margin: 2px;\r\n    border: 2px solid transparent;\r\n}\r\n\r\n.color-selector.selected {\r\n    border-color: #333;\r\n}\r\n\r\n.sortable-placeholder {\r\n    background: #f0f0f0;\r\n    border: 2px dashed #ccc;\r\n    height: 100px;\r\n    margin: 10px 0;\r\n}\r\n\r\n/* ========== admin-menu-management 页面样式 ========== */\r\n/* 菜单管理页面样式 */\r\n.menu-management-container {\r\n    padding: 20px;\r\n}\r\n\r\n.menu-items-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    background: #fff;\r\n    table-layout: auto !important; /* 确保表格自动布局 */\r\n}\r\n\r\n/* 一级菜单表格特定样式 */\r\n#categoryOrderContainer .menu-items-table {\r\n    table-layout: auto !important;\r\n}\r\n\r\n#categoryOrderContainer .menu-items-table th,\r\n#categoryOrderContainer .menu-items-table td {\r\n    padding: 8px !important;\r\n    border: 1px solid #dee2e6 !important;\r\n    vertical-align: middle !important;\r\n    text-align: left !important;\r\n    white-space: nowrap;\r\n}\r\n\r\n/* 优化一级菜单表格列宽度 */\r\n#categoryOrderContainer .menu-items-table th:nth-child(1),\r\n#categoryOrderContainer .menu-items-table td:nth-child(1) { width: 60px; } /* 拖拽 */\r\n#categoryOrderContainer .menu-items-table th:nth-child(2),\r\n#categoryOrderContainer .menu-items-table td:nth-child(2) { width: 60px; } /* 选择 */\r\n#categoryOrderContainer .menu-items-table th:nth-child(3),\r\n#categoryOrderContainer .menu-items-table td:nth-child(3) { width: 60px; } /* 图标 */\r\n#categoryOrderContainer .menu-items-table th:nth-child(4),\r\n#categoryOrderContainer .menu-items-table td:nth-child(4) { width: 150px; min-width: 120px; } /* 分类名称 */\r\n#categoryOrderContainer .menu-items-table th:nth-child(5),\r\n#categoryOrderContainer .menu-items-table td:nth-child(5) { width: 120px; } /* 菜单数量 */\r\n#categoryOrderContainer .menu-items-table th:nth-child(6),\r\n#categoryOrderContainer .menu-items-table td:nth-child(6) { width: 80px; } /* 排序 */\r\n#categoryOrderContainer .menu-items-table th:nth-child(7),\r\n#categoryOrderContainer .menu-items-table td:nth-child(7) { width: 100px; } /* 显示状态 */\r\n#categoryOrderContainer .menu-items-table th:nth-child(8),\r\n#categoryOrderContainer .menu-items-table td:nth-child(8) { width: 100px; } /* 启用状态 */ /* 操作 */\r\n#categoryOrderContainer .menu-items-table th:nth-child(6),\r\n#categoryOrderContainer .menu-items-table td:nth-child(6) { width: 80px; }\r\n#categoryOrderContainer .menu-items-table th:nth-child(7),\r\n#categoryOrderContainer .menu-items-table td:nth-child(7) { width: 80px; }\r\n#categoryOrderContainer .menu-items-table th:nth-child(8),\r\n#categoryOrderContainer .menu-items-table td:nth-child(8) { width: 80px; }\r\n#categoryOrderContainer .menu-items-table th:nth-child(9),\r\n#categoryOrderContainer .menu-items-table td:nth-child(9) { width: 120px; }\r\n\r\n/* 完整拖拽样式 */\r\n.menu-item.sortable-ghost {\r\n    opacity: 0.5;\r\n    background: #007bff;\r\n    color: white;\r\n    transform: rotate(2deg);\r\n    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* 拖拽手柄样式 */\r\n.drag-handle {\r\n    cursor: move;\r\n    margin-right: 10px;\r\n    color: #6c757d;\r\n    transition: color 0.2s;\r\n}\r\n\r\n/* 移动端优化 */\r\n\r\n/* 拖拽状态动画 */\r\n.menu-item {\r\n    transition: all 0.2s ease;\r\n}\r\n\r\n/* 拖拽提示动画 */\r\n.drag-hint {\r\n    animation: slideInRight 0.3s ease;\r\n}\r\n\r\n@keyframes slideInRight {\r\n    from {\r\n        transform: translateX(100%);\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        transform: translateX(0);\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n/* 一级菜单管理样式 - 完全模仿二级菜单容器样式 */\r\n.category-order-container {\r\n    min-height: 50px;\r\n    padding: 0;\r\n    background: #fff;\r\n}\r\n\r\n/* 移除冲突的flex样式，让表格正常显示 */\r\n.category-order-item {\r\n    /* 移除所有flex相关样式，使用表格行的默认显示 */\r\n    cursor: move;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.category-order-item:hover {\r\n    background: #f8f9fa;\r\n}\r\n\r\n.category-order-item.sortable-ghost {\r\n    opacity: 0.5;\r\n    background: #007bff;\r\n    color: white;\r\n    transform: rotate(2deg);\r\n}\r\n\r\n.category-order-item.sortable-chosen {\r\n    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);\r\n    transform: scale(1.02);\r\n    z-index: var(--z-drag);\r\n}\r\n\r\n/* 保留拖拽手柄样式 */\r\n.category-drag-handle {\r\n    color: #6c757d;\r\n    cursor: move;\r\n}\r\n\r\n.category-drag-handle:hover {\r\n    color: #007bff;\r\n}\r\n\r\n/* 保护样式 */\r\n.protected-category {\r\n    border: 2px solid #ffc107 !important;\r\n    background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%) !important;\r\n    position: relative;\r\n}\r\n\r\n/* 菜单管理页面样式 */\r\n.menu-management-container {\r\n    padding: 20px;\r\n}\r\n\r\n.role-selector {\r\n    margin-bottom: 20px;\r\n    padding: 15px;\r\n    background: #f8f9fa;\r\n    border-radius: 5px;\r\n}\r\n\r\n.menu-category {\r\n    margin-bottom: 30px;\r\n    border: 1px solid #ddd;\r\n    border-radius: 5px;\r\n    overflow: hidden;\r\n}\r\n\r\n.category-header {\r\n    background: #007bff;\r\n    color: white;\r\n    padding: 10px 15px;\r\n    font-weight: bold;\r\n    cursor: pointer;\r\n}\r\n\r\n.category-header:hover {\r\n    background: #0056b3;\r\n}\r\n\r\n.menu-items-container {\r\n    min-height: 50px;\r\n    padding: 0;\r\n    background: #fff;\r\n}\r\n\r\n.menu-item {\r\n    cursor: move;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.menu-item:hover {\r\n    background: #f8f9fa;\r\n}\r\n\r\n.menu-item td {\r\n    padding: 8px;\r\n    border: 1px solid #dee2e6;\r\n    vertical-align: middle;\r\n    text-align: left;\r\n}\r\n\r\n.menu-table-header {\r\n    background: #007bff;\r\n    color: white;\r\n    font-weight: bold;\r\n}\r\n\r\n.menu-table-header th {\r\n    padding: 10px 8px;\r\n    border: 1px solid #0056b3;\r\n    text-align: center;\r\n}\r\n\r\n.menu-item.sortable-chosen {\r\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\r\n    transform: scale(1.02);\r\n    z-index: var(--z-drag);\r\n}\r\n\r\n.menu-item.sortable-drag {\r\n    transform: rotate(5deg);\r\n    opacity: 0.8;\r\n}\r\n\r\n.sortable-placeholder {\r\n    border: 2px dashed #007bff;\r\n    background: rgba(0, 123, 255, 0.1);\r\n    height: 60px;\r\n    margin: 5px 0;\r\n    border-radius: 5px;\r\n    position: relative;\r\n}\r\n\r\n.sortable-placeholder::before {\r\n    content: \"拖拽到此处\";\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    color: #007bff;\r\n    font-weight: bold;\r\n}\r\n\r\n.menu-items-container.drag-target {\r\n    background: rgba(0, 123, 255, 0.05);\r\n    border: 2px dashed #007bff;\r\n    border-radius: 5px;\r\n}\r\n\r\n.menu-item.drag-over {\r\n    border-top: 3px solid #007bff;\r\n}\r\n\r\n.drag-handle:hover {\r\n    color: #007bff;\r\n}\r\n\r\n/* 移动端优化 */\r\n\r\n.menu-items-container.drag-target {\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n@keyframes slideInRight {\r\n    from {\r\n        transform: translateX(100%);\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        transform: translateX(0);\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n.td-drag-handle {\r\n    text-align: center;\r\n    width: 30px;\r\n    cursor: move;\r\n}\r\n\r\n.td-drag-handle .drag-handle {\r\n    color: #6c757d;\r\n    font-size: 14px;\r\n}\r\n\r\n.td-drag-handle .drag-handle:hover {\r\n    color: #007bff;\r\n}\r\n\r\n.td-checkbox {\r\n    text-align: center;\r\n    width: 40px;\r\n}\r\n\r\n.td-icon {\r\n    text-align: center;\r\n    width: 40px;\r\n}\r\n\r\n.td-name {\r\n    font-weight: 500;\r\n    min-width: 150px;\r\n}\r\n\r\n.td-description {\r\n    min-width: 120px;\r\n}\r\n\r\n.menu-item-description {\r\n    background: #28a745;\r\n    color: white;\r\n    padding: 2px 8px;\r\n    border-radius: 10px;\r\n    font-size: 0.8em;\r\n    display: inline-block;\r\n}\r\n\r\n.td-url {\r\n    color: #6c757d;\r\n    font-size: 0.9em;\r\n    min-width: 200px;\r\n    max-width: 250px;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n}\r\n\r\n.td-order {\r\n    text-align: center;\r\n    width: 60px;\r\n}\r\n\r\n.menu-item-order {\r\n    background: #007bff;\r\n    color: white;\r\n    padding: 2px 8px;\r\n    border-radius: 10px;\r\n    font-size: 0.8em;\r\n    display: inline-block;\r\n}\r\n\r\n.td-status {\r\n    text-align: center;\r\n    width: 80px;\r\n}\r\n\r\n.td-actions {\r\n    text-align: center;\r\n    width: 120px;\r\n}\r\n\r\n.status-badge {\r\n    padding: 2px 8px;\r\n    border-radius: 10px;\r\n    font-size: 0.8em;\r\n    display: inline-block;\r\n}\r\n\r\n.status-visible {\r\n    background: #28a745;\r\n    color: white;\r\n}\r\n\r\n.status-hidden {\r\n    background: #dc3545;\r\n    color: white;\r\n}\r\n\r\n.status-enabled {\r\n    background: #17a2b8;\r\n    color: white;\r\n}\r\n\r\n.status-disabled {\r\n    background: #6c757d;\r\n    color: white;\r\n}\r\n\r\n.batch-operations {\r\n    margin: 20px 0;\r\n    padding: 15px;\r\n    background: #f8f9fa;\r\n    border-radius: 5px;\r\n}\r\n\r\n.preview-panel {\r\n    position: fixed;\r\n    top: 0;\r\n    right: -400px;\r\n    width: 400px;\r\n    height: 100vh;\r\n    background: white;\r\n    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);\r\n    transition: right 0.3s ease;\r\n    z-index: var(--z-preview);\r\n    overflow-y: auto;\r\n}\r\n\r\n.preview-panel.show {\r\n    right: 0;\r\n}\r\n\r\n.preview-header {\r\n    padding: 15px;\r\n    background: #007bff;\r\n    color: white;\r\n    font-weight: bold;\r\n}\r\n\r\n.preview-content {\r\n    padding: 15px;\r\n}\r\n\r\n.loading-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: rgba(0, 0, 0, 0.5);\r\n    display: none;\r\n    justify-content: center;\r\n    align-items: center;\r\n    z-index: var(--z-loading-overlay);\r\n}\r\n\r\n.loading-spinner {\r\n    background: white;\r\n    padding: 20px;\r\n    border-radius: 5px;\r\n    text-align: center;\r\n}\r\n\r\n.protected-category::before {\r\n    content: \"🛡️\";\r\n    position: absolute;\r\n    top: 5px;\r\n    right: 5px;\r\n    font-size: 16px;\r\n    /* z-index: 10; - 取消低优先级z-index */\r\n}\r\n\r\n.protected-menu {\r\n    background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%) !important;\r\n    border-left: 4px solid #ffc107 !important;\r\n}\r\n\r\n.protected-btn {\r\n    opacity: 0.6;\r\n    cursor: not-allowed !important;\r\n    position: relative;\r\n}\r\n\r\n.protected-btn:hover {\r\n    opacity: 0.8;\r\n    transform: none !important;\r\n    box-shadow: none !important;\r\n}\r\n\r\n.protected-btn::after {\r\n    content: \"🛡️\";\r\n    position: absolute;\r\n    top: -2px;\r\n    right: -2px;\r\n    font-size: 10px;\r\n    /* z-index: 10; - 取消低优先级z-index */\r\n}\r\n\r\n/* ========== admin-menu-management-help 页面样式 ========== */\r\n.help-container {\r\n    max-width: 1000px;\r\n    margin: 0 auto;\r\n    padding: 20px;\r\n}\r\n\r\n.help-section {\r\n    margin-bottom: 40px;\r\n    padding: 20px;\r\n    background: #f8f9fa;\r\n    border-radius: 8px;\r\n    border-left: 4px solid #007bff;\r\n}\r\n\r\n.help-section h3 {\r\n    color: #007bff;\r\n    margin-bottom: 15px;\r\n}\r\n\r\n.feature-list {\r\n    list-style: none;\r\n    padding: 0;\r\n}\r\n\r\n.feature-list li {\r\n    padding: 8px 0;\r\n    border-bottom: 1px solid #dee2e6;\r\n}\r\n\r\n.feature-list li:last-child {\r\n    border-bottom: none;\r\n}\r\n\r\n.feature-icon {\r\n    color: #28a745;\r\n    margin-right: 10px;\r\n}\r\n\r\n.step-number {\r\n    display: inline-block;\r\n    width: 30px;\r\n    height: 30px;\r\n    background: #007bff;\r\n    color: white;\r\n    border-radius: 50%;\r\n    text-align: center;\r\n    line-height: 30px;\r\n    margin-right: 10px;\r\n    font-weight: bold;\r\n}\r\n\r\n.screenshot {\r\n    max-width: 100%;\r\n    border: 1px solid #dee2e6;\r\n    border-radius: 5px;\r\n    margin: 10px 0;\r\n}\r\n\r\n.tip-box {\r\n    background: #fff3cd;\r\n    border: 1px solid #ffeaa7;\r\n    border-radius: 5px;\r\n    padding: 15px;\r\n    margin: 15px 0;\r\n}\r\n\r\n.warning-box {\r\n    background: #f8d7da;\r\n    border: 1px solid #f5c6cb;\r\n    border-radius: 5px;\r\n    padding: 15px;\r\n    margin: 15px 0;\r\n}\r\n\r\n/* ========== admin-menu-permission-matrix 页面样式 ========== */\r\n.matrix-container {\r\n    padding: 20px;\r\n    overflow-x: auto;\r\n}\r\n\r\n/* 组合状态的特殊样式 */\r\n.permission-cell.access-granted.menu-visible {\r\n    background: #e8f5e8;\r\n    border: 2px solid #28a745;\r\n    box-shadow: inset 0 0 0 1px #007bff;\r\n}\r\n\r\n/* 优化横向显示 */\r\n.permission-matrix .menu-name {\r\n    min-width: 250px;\r\n    max-width: 300px;\r\n}\r\n\r\n.permission-matrix {\r\n    width: 100%;\r\n    min-width: 1200px;\r\n    border-collapse: collapse;\r\n    background: white;\r\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.permission-matrix th,\r\n.permission-matrix td {\r\n    border: 1px solid #dee2e6;\r\n    padding: 8px;\r\n    text-align: center;\r\n    vertical-align: middle;\r\n}\r\n\r\n.permission-matrix th {\r\n    background: #2e6da4;\r\n    color: white;\r\n    font-weight: bold;\r\n    position: sticky;\r\n    top: 0;\r\n    /* z-index: 10; - 取消低优先级z-index */\r\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);\r\n    font-size: 14px;\r\n}\r\n\r\n.permission-matrix .menu-name {\r\n    background: #ffffff;\r\n    font-weight: bold;\r\n    text-align: left;\r\n    min-width: 200px;\r\n    position: sticky;\r\n    left: 0;\r\n    /* z-index: 5; - 取消低优先级z-index */\r\n    border-right: 2px solid #dee2e6;\r\n    color: #333;\r\n}\r\n\r\n.permission-matrix .category-header {\r\n    background: #1c5380;\r\n    color: white;\r\n    font-weight: bold;\r\n    text-align: center;\r\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);\r\n    font-size: 14px;\r\n}\r\n\r\n.permission-checkbox {\r\n    transform: scale(1.3);\r\n    cursor: pointer;\r\n    margin: 2px;\r\n}\r\n\r\n.permission-cell {\r\n    position: relative;\r\n    min-width: 140px;\r\n    text-align: center;\r\n    vertical-align: middle;\r\n    padding: 12px 8px;\r\n    border: 1px solid #dee2e6;\r\n    transition: all 0.2s ease;\r\n    min-height: 70px;\r\n}\r\n\r\n.permission-cell:hover {\r\n    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);\r\n    transform: translateY(-1px);\r\n}\r\n\r\n.permission-controls {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 6px;\r\n    padding: 5px;\r\n}\r\n\r\n.permission-controls>div {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 6px;\r\n}\r\n\r\n.permission-controls small {\r\n    font-size: 0.9em;\r\n    font-weight: 600;\r\n    color: #333;\r\n    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.9);\r\n    line-height: 1.2;\r\n}\r\n\r\n.permission-cell.access-granted {\r\n    background: #e8f5e8;\r\n    border-left: 3px solid #28a745;\r\n}\r\n\r\n.permission-cell.access-denied {\r\n    background: #ffeaea;\r\n    border-left: 3px solid #dc3545;\r\n}\r\n\r\n.permission-cell.menu-visible {\r\n    background: #e6f3ff;\r\n    border-right: 3px solid #007bff;\r\n}\r\n\r\n.permission-cell.menu-hidden {\r\n    background: #f8f9fa;\r\n    border-right: 3px solid #6c757d;\r\n}\r\n\r\n.permission-cell.access-denied.menu-hidden {\r\n    background: #f8f9fa;\r\n    border: 2px solid #6c757d;\r\n    opacity: 0.7;\r\n}\r\n\r\n.permission-cell.access-granted.menu-hidden {\r\n    background: #e8f5e8;\r\n    border-left: 3px solid #28a745;\r\n    border-right: 3px solid #6c757d;\r\n}\r\n\r\n.permission-cell.access-denied.menu-visible {\r\n    background: #ffeaea;\r\n    border-left: 3px solid #dc3545;\r\n    border-right: 3px solid #007bff;\r\n}\r\n\r\n.role-header {\r\n    min-width: 120px;\r\n    text-align: center;\r\n    font-weight: bold;\r\n}\r\n\r\n.batch-controls {\r\n    margin: 20px 0;\r\n    padding: 15px;\r\n    background: #f8f9fa;\r\n    border-radius: 5px;\r\n}\r\n\r\n.legend {\r\n    margin: 20px 0;\r\n    padding: 15px;\r\n    background: #e9ecef;\r\n    border-radius: 5px;\r\n}\r\n\r\n.legend-item {\r\n    display: inline-block;\r\n    margin: 5px 15px 5px 0;\r\n}\r\n\r\n.legend-color {\r\n    display: inline-block;\r\n    width: 20px;\r\n    height: 20px;\r\n    margin-right: 5px;\r\n    vertical-align: middle;\r\n    border: 1px solid #ccc;\r\n}\r\n\r\n.loading-cell {\r\n    background: #fff3cd;\r\n}\r\n\r\n/* .table-responsive {\r\n    max-height: 75vh;\r\n    overflow: auto;\r\n    border: 1px solid #dee2e6;\r\n    border-radius: 5px;\r\n} - 注释掉冲突的样式，使用统一的overflow: visible */\r\n\r\n.permission-matrix {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.role-header {\r\n    min-width: 140px;\r\n    max-width: 160px;\r\n    padding: 10px 5px;\r\n}\r\n\r\n.role-header small {\r\n    font-size: 0.8em;\r\n    opacity: 0.9;\r\n    font-weight: normal;\r\n}\r\n\r\n/* ========== admin-my-hazards 页面样式 ========== */\r\n\r\n.status-rectifying {\r\n    background-color: #ffc107;\r\n    color: #212529;\r\n}\r\n\r\n.nav-tabs .badge {\r\n    background-color: #337ab7;\r\n    color: white;\r\n    margin-left: 5px;\r\n}\r\n\r\n/* ========== admin-new-role-management 页面样式 ========== */\r\n.role-table th,\r\n.role-table td {\r\n    vertical-align: middle !important;\r\n}\r\n\r\n.role-actions {\r\n    white-space: nowrap;\r\n}\r\n\r\n.role-actions .btn {\r\n    margin-right: 5px;\r\n}\r\n\r\n.role-actions .btn:last-child {\r\n    margin-right: 0;\r\n}\r\n\r\n.system-role {\r\n    font-weight: bold;\r\n    color: #337ab7;\r\n}\r\n\r\n/* ========== admin-pending-notify-hazards 页面样式 ========== */\r\n\r\n.lab-panel {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.lab-panel .panel-heading {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n}\r\n\r\n.lab-panel .panel-title {\r\n    margin: 0;\r\n}\r\n\r\n.preview-modal-body {\r\n    max-height: 500px;\r\n    overflow-y: auto;\r\n}\r\n\r\n/* ========== admin-rectification-review 页面样式 ========== */\r\n\r\n.status-rectifying {\r\n    background-color: #ffc107;\r\n    color: #212529;\r\n}\r\n\r\n/* 优化筛选区域布局 */\r\n#filterForm .row {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n/* 美化下拉框样式 */\r\n.bootstrap-select .dropdown-toggle {\r\n    border-radius: 3px;\r\n    border: 1px solid #ccc;\r\n    background-color: #fff;\r\n}\r\n\r\n.status-closed {\r\n    background-color: #28a745;\r\n    color: white;\r\n}\r\n\r\n.status-submitted {\r\n    background-color: #17a2b8;\r\n    color: white;\r\n}\r\n\r\n.status-rejected {\r\n    background-color: #dc3545;\r\n    color: white;\r\n}\r\n\r\n.level-high {\r\n    color: #dc3545;\r\n    font-weight: bold;\r\n}\r\n\r\n.level-medium {\r\n    color: #fd7e14;\r\n}\r\n\r\n.level-low {\r\n    color: #28a745;\r\n}\r\n\r\n.bootstrap-select .dropdown-toggle .filter-option {\r\n    white-space: normal;\r\n    word-wrap: break-word;\r\n}\r\n\r\n/* 高级筛选面板样式 */\r\n#advancedSearch {\r\n    margin-bottom: 15px;\r\n}\r\n\r\n#advancedSearch .panel-title a {\r\n    display: block;\r\n    text-decoration: none;\r\n}\r\n\r\n/* 表格式筛选样式 */\r\n.table-filter {\r\n    border: none;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.table-filter td {\r\n    border: none;\r\n    padding: 8px 10px;\r\n    vertical-align: middle;\r\n}\r\n\r\n.table-filter .filter-label {\r\n    text-align: right;\r\n    padding-right: 5px;\r\n}\r\n\r\n.table-filter .filter-label label {\r\n    margin-bottom: 0;\r\n    font-weight: 600;\r\n    color: #555;\r\n    white-space: nowrap;\r\n}\r\n\r\n.table-filter .filter-label label i {\r\n    margin-right: 5px;\r\n    color: #337ab7;\r\n}\r\n\r\n#advancedSearch .form-group label {\r\n    font-weight: 600;\r\n    color: #555;\r\n    margin-bottom: 5px;\r\n}\r\n\r\n#advancedSearch .form-group label i {\r\n    margin-right: 5px;\r\n    color: #337ab7;\r\n}\r\n\r\n#advancedSearch .btn {\r\n    margin-left: 5px;\r\n    border-radius: 3px;\r\n}\r\n\r\n#advancedSearch .btn-primary {\r\n    background-color: #337ab7;\r\n    border-color: #2e6da4;\r\n}\r\n\r\n#advancedSearch .btn-primary:hover {\r\n    background-color: #286090;\r\n    border-color: #204d74;\r\n}\r\n\r\n#advancedSearch .btn-default {\r\n    background-color: #f5f5f5;\r\n    border-color: #ddd;\r\n}\r\n\r\n#advancedSearch .btn-default:hover {\r\n    background-color: #e6e6e6;\r\n    border-color: #adadad;\r\n}\r\n\r\n.date-picker {\r\n    background-color: #fff;\r\n    cursor: pointer;\r\n}\r\n\r\n#filterForm .col-md-6 {\r\n    padding: 0 10px;\r\n}\r\n\r\n.bootstrap-select .dropdown-toggle:hover,\r\n.bootstrap-select .dropdown-toggle:focus {\r\n    border-color: #66afe9;\r\n    outline: 0;\r\n    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);\r\n}\r\n\r\n/* ========== 统一表格边框样式 - 现代简洁风格 ========== */\r\n\r\n/* 统一表格边框样式 */\r\n.table-bordered {\r\n    border: 1px solid #e9ecef !important;\r\n}\r\n\r\n.table-bordered th,\r\n.table-bordered td {\r\n    border: 1px solid #e9ecef !important;\r\n}\r\n\r\n/* 表头加强边框和样式 - 确保所有表头居中 */\r\n.table thead th,\r\n.table th {\r\n    border-bottom: 2px solid #dee2e6 !important;\r\n    background-color: #f8f9fa !important;\r\n    font-weight: 600 !important;\r\n    text-align: center !important;\r\n}\r\n\r\n/* ========== 统一表格数据对齐规则 ========== */\r\n\r\n/* 居中对齐的数据类型 */\r\n.table th.text-center,\r\n.table td.text-center,\r\n.table .col-id,           /* 序号/ID列 */\r\n.table .col-status,       /* 状态列 */\r\n.table .col-actions,      /* 操作列 */\r\n.table .col-date,         /* 日期列 */\r\n.table .col-time,         /* 时间列 */\r\n.table .col-count,        /* 计数列 */\r\n.table .col-number,       /* 数字列 */\r\n.table .col-level,        /* 等级列 */\r\n.table .col-type,         /* 类型标签列 */\r\n.table .col-boolean,      /* 是/否列 */\r\n.table .col-name {        /* 简短名称列 */\r\n    text-align: center !important;\r\n}\r\n\r\n/* 左对齐的数据类型 */\r\n.table .col-title,        /* 标题列 */\r\n.table .col-description,  /* 描述列 */\r\n.table .col-content,      /* 内容列 */\r\n.table .col-email,        /* 邮箱列 */\r\n.table .col-phone,        /* 电话列 */\r\n.table .col-address,      /* 地址列 */\r\n.table .col-path,         /* 路径列 */\r\n.table .col-text {        /* 文本列 */\r\n    text-align: left !important;\r\n}\r\n\r\n/* 名称列根据内容长度决定对齐方式 */\r\n.table .col-name {        /* 名称列 - 默认居中（适合简短名称如校区、楼宇等） */\r\n    text-align: center !important;\r\n}\r\n\r\n/* 长名称列左对齐（如实验室名称、用户姓名等） */\r\n.table .col-long-name {   /* 长名称列 */\r\n    text-align: left !important;\r\n}\r\n\r\n/* 基于表头内容的智能对齐 - 居中对齐 */\r\n.table tbody tr td:nth-child(1) {\r\n    text-align: center !important; /* 第一列通常是序号/ID */\r\n}\r\n\r\n/* 操作列（最后一列）居中 */\r\n.table tbody tr td:last-child {\r\n    text-align: center !important;\r\n}\r\n\r\n/* 基于表头文字内容的智能对齐 */\r\n.table thead th:contains(\"ID\") ~ tbody tr td:nth-child(1),\r\n.table thead th:contains(\"序号\") ~ tbody tr td:nth-child(1),\r\n.table thead th:contains(\"编号\") ~ tbody tr td:nth-child(1),\r\n.table thead th:contains(\"状态\") ~ tbody tr td,\r\n.table thead th:contains(\"等级\") ~ tbody tr td,\r\n.table thead th:contains(\"类型\") ~ tbody tr td,\r\n.table thead th:contains(\"数量\") ~ tbody tr td,\r\n.table thead th:contains(\"计数\") ~ tbody tr td,\r\n.table thead th:contains(\"时间\") ~ tbody tr td,\r\n.table thead th:contains(\"日期\") ~ tbody tr td,\r\n.table thead th:contains(\"校区\") ~ tbody tr td,\r\n.table thead th:contains(\"楼宇\") ~ tbody tr td,\r\n.table thead th:contains(\"部门\") ~ tbody tr td,\r\n.table thead th:contains(\"房间号\") ~ tbody tr td,\r\n.table thead th:contains(\"容纳人数\") ~ tbody tr td,\r\n.table thead th:contains(\"联系电话\") ~ tbody tr td,\r\n.table thead th:contains(\"操作\") ~ tbody tr td {\r\n    text-align: center !important;\r\n}\r\n\r\n/* ========== 智能表格对齐规则 - 无需页面设置CSS类 ========== */\r\n\r\n/* 所有表头居中 - 最高优先级 */\r\n.table thead th {\r\n    text-align: center !important;\r\n}\r\n\r\n/* 通用表格数据对齐规则 */\r\n.table tbody tr td:nth-child(1) {\r\n    text-align: center !important; /* 第一列：ID/序号 */\r\n}\r\n\r\n.table tbody tr td:last-child {\r\n    text-align: center !important; /* 最后一列：操作按钮 */\r\n}\r\n\r\n/* 根据表格列数智能对齐 */\r\n/* 3列表格：ID + 名称 + 操作 */\r\n.table tbody tr:has(td:nth-child(3):last-child) td:nth-child(2) {\r\n    text-align: center !important; /* 名称居中 */\r\n}\r\n\r\n/* 4列表格：ID + 名称 + 描述 + 操作 */\r\n.table tbody tr:has(td:nth-child(4):last-child) td:nth-child(2) {\r\n    text-align: center !important; /* 名称居中 */\r\n}\r\n.table tbody tr:has(td:nth-child(4):last-child) td:nth-child(3) {\r\n    text-align: left !important; /* 描述左对齐 */\r\n}\r\n\r\n/* 5列表格：ID + 名称1 + 名称2 + 描述 + 操作 */\r\n.table tbody tr:has(td:nth-child(5):last-child) td:nth-child(2),\r\n.table tbody tr:has(td:nth-child(5):last-child) td:nth-child(3) {\r\n    text-align: center !important; /* 名称居中 */\r\n}\r\n.table tbody tr:has(td:nth-child(5):last-child) td:nth-child(4) {\r\n    text-align: left !important; /* 描述左对齐 */\r\n}\r\n\r\n/* 6列表格：ID + 名称1 + 名称2 + 名称3 + 描述 + 操作 */\r\n.table tbody tr:has(td:nth-child(6):last-child) td:nth-child(2),\r\n.table tbody tr:has(td:nth-child(6):last-child) td:nth-child(3),\r\n.table tbody tr:has(td:nth-child(6):last-child) td:nth-child(4) {\r\n    text-align: center !important; /* 名称居中 */\r\n}\r\n.table tbody tr:has(td:nth-child(6):last-child) td:nth-child(5) {\r\n    text-align: left !important; /* 描述左对齐 */\r\n}\r\n\r\n/* 备用方案：如果浏览器不支持:has()选择器 */\r\n.table tbody tr td:nth-child(2) {\r\n    text-align: center !important; /* 第二列默认居中 */\r\n}\r\n.table tbody tr td:nth-child(3) {\r\n    text-align: center !important; /* 第三列默认居中 */\r\n}\r\n.table tbody tr td:nth-child(4) {\r\n    text-align: left !important; /* 第四列默认左对齐（通常是描述） */\r\n}\r\n.table tbody tr td:nth-child(5) {\r\n    text-align: left !important; /* 第五列默认左对齐 */\r\n}\r\n\r\n/* 移除内联样式后的CSS类 */\r\n.select-all-label {\r\n    font-weight: bold !important;\r\n    color: #337ab7 !important;\r\n    padding-left: 20px !important;\r\n    margin-left: 0 !important;\r\n}\r\n\r\n.property-label {\r\n    padding-left: 20px !important;\r\n    margin-left: 0 !important;\r\n}\r\n\r\n.checkbox {\r\n    padding-left: 0 !important;\r\n    margin-left: 0 !important;\r\n}\r\n\r\n.caret.pull-right {\r\n    margin-top: 8px !important;\r\n}\r\n\r\n/* 移除内联样式后的新CSS类 */\r\n.role-actions-header {\r\n    margin-bottom: 15px !important;\r\n}\r\n\r\n.filter-panel {\r\n    margin-top: 15px !important;\r\n}\r\n\r\n.filter-table {\r\n    margin-bottom: 10px !important;\r\n}\r\n\r\n.filter-label {\r\n    width: 15% !important;\r\n}\r\n\r\n.filter-input {\r\n    width: 35% !important;\r\n}\r\n\r\n.date-separator {\r\n    padding-top: 6px !important;\r\n}\r\n\r\n.filter-actions {\r\n    margin-top: 15px !important;\r\n}\r\n\r\n.debug-panel {\r\n    margin-top: 15px !important;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.table th {\r\n    background-color: #f8f9fa;\r\n}\r\n\r\n.table-hover tbody tr:hover {\r\n    background-color: #f0f7fd;\r\n}\r\n\r\n/* 排序图标 */\r\nth[data-sort] {\r\n    cursor: pointer;\r\n    position: relative;\r\n}\r\n\r\nth[data-sort]:after {\r\n    content: '\\f0dc';\r\n    font-family: FontAwesome;\r\n    margin-left: 5px;\r\n    color: #ccc;\r\n}\r\n\r\nth.sort-asc:after {\r\n    content: '\\f0de';\r\n    color: #333;\r\n}\r\n\r\nth.sort-desc:after {\r\n    content: '\\f0dd';\r\n    color: #333;\r\n}\r\n\r\n/* 当下拉框打开时，增加其父元素的z-index */\r\n.bootstrap-select.open {\r\n    z-index: var(--z-select) !important;\r\n}\r\n\r\n/* 确保日期选择器在上层 */\r\n.datepicker {\r\n    z-index: var(--z-datepicker) !important;\r\n}\r\n\r\n/* 调整表单布局，防止重叠 */\r\n#filterForm .row {\r\n    margin-bottom: 25px !important;\r\n}\r\n\r\n/* 自定义下拉框位置样式 */\r\n.custom-dropdown-position {\r\n    box-shadow: 0 6px 12px rgba(0, 0, 0, .175) !important;\r\n    border: 1px solid rgba(0, 0, 0, .15) !important;\r\n    border-radius: 4px !important;\r\n}\r\n\r\n/* 确保下拉框选项可见 - 使用统一z-index变量 */\r\n.bootstrap-select .dropdown-menu li {\r\n    position: relative !important;\r\n    z-index: var(--z-select) !important;\r\n}\r\n\r\n/* 修复下拉框搜索框样式 */\r\n.bootstrap-select .bs-searchbox {\r\n    padding: 8px !important;\r\n}\r\n\r\n/* 确保下拉框在滚动时仍然可见 */\r\n.bootstrap-select.open .dropdown-menu {\r\n    display: block !important;\r\n}\r\n\r\n/* 防止下拉框被其他元素遮挡 - 使用统一z-index变量 */\r\nbody>.dropdown-menu {\r\n    z-index: var(--z-dropdown-menu) !important;\r\n}\r\n\r\n/* ========== admin-safety-card-images 页面样式 ========== */\r\n.image-grid {\r\n    display: flex !important;\r\n    flex-wrap: wrap !important;\r\n    margin-top: 15px;\r\n    margin-left: -20px;\r\n    /* 抵消第一列的左边距 */\r\n    width: 100%;\r\n}\r\n\r\n.image-card {\r\n    width: 180px !important;\r\n    margin: 0 0 20px 20px !important;\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n    transition: transform 0.2s;\r\n    flex-shrink: 0 !important;\r\n    flex-grow: 0 !important;\r\n    display: inline-block !important;\r\n    /* 防止卡片被压缩 */\r\n}\r\n\r\n.image-card:hover {\r\n    transform: translateY(-3px);\r\n    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.image-card .image-container {\r\n    height: 180px !important;\r\n    width: 180px !important;\r\n    overflow: hidden;\r\n    display: flex !important;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background-color: #f8f8f8;\r\n}\r\n\r\n.image-card .image-container img {\r\n    max-width: 160px;\r\n    max-height: 160px;\r\n    object-fit: contain;\r\n}\r\n\r\n.image-card .image-info {\r\n    padding: 8px;\r\n    width: 180px !important;\r\n    box-sizing: border-box !important;\r\n}\r\n\r\n.image-card .image-title {\r\n    font-weight: bold;\r\n    margin-bottom: 5px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    font-size: 14px;\r\n}\r\n\r\n.image-card .image-category {\r\n    display: inline-block;\r\n    padding: 2px 5px;\r\n    border-radius: 3px;\r\n    font-size: 12px;\r\n    margin-bottom: 5px;\r\n}\r\n\r\n.image-card .image-category.danger {\r\n    background-color: #f8d7da;\r\n    color: #721c24;\r\n}\r\n\r\n.image-card .image-category.notice {\r\n    background-color: #fff3cd;\r\n    color: #856404;\r\n}\r\n\r\n.image-card .image-category.protection {\r\n    background-color: #d4edda;\r\n    color: #155724;\r\n}\r\n\r\n.image-card .image-actions {\r\n    margin-top: 5px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n}\r\n\r\n.image-card .image-actions .btn {\r\n    padding: 2px 5px;\r\n    font-size: 12px;\r\n}\r\n\r\n.pagination-container {\r\n    margin-top: 20px;\r\n    text-align: center;\r\n}\r\n\r\n/* 确保在小屏幕上也能显示多列 */\r\n\r\n/* ========== admin-safety-card-preview 页面样式 ========== */\r\n/* 其他样式 */\r\n.mapping-info {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n/* 图片网格样式 */\r\n.image-grid {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 3px;\r\n    margin-top: 10px;\r\n    max-height: 600px;\r\n    /* 增加高度，确保能显示更多图片 */\r\n    overflow-y: auto;\r\n    padding-right: 5px;\r\n    padding-bottom: 10px;\r\n}\r\n\r\n.image-item .image-name {\r\n    font-size: 12px;\r\n    margin-top: 5px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    color: #333;\r\n    max-width: 150px;\r\n    /* 限制宽度 */\r\n    line-height: 1.2;\r\n    display: block;\r\n    /* 显示名称 */\r\n}\r\n\r\n/* 复选框样式 */\r\n.image-item .checkbox {\r\n    margin: 0;\r\n    padding: 0;\r\n}\r\n\r\n.image-item input[type=\"checkbox\"] {\r\n    margin: 0;\r\n    padding: 0;\r\n    transform: scale(1.5);\r\n    /* 增大复选框 */\r\n    position: absolute;\r\n    top: 5px;\r\n    left: 5px;\r\n}\r\n\r\n/* 选中图片预览样式 */\r\n.selected-images-preview {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 10px;\r\n    margin-top: 10px;\r\n    margin-bottom: 15px;\r\n    justify-content: flex-start;\r\n}\r\n\r\n/* 其他样式 */\r\n.mapping-info {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.mapping-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n}\r\n\r\n.mapping-table th,\r\n.mapping-table td {\r\n    border: 1px solid #ddd;\r\n    padding: 8px;\r\n    text-align: left;\r\n}\r\n\r\n.mapping-table th {\r\n    background-color: #f5f5f5;\r\n}\r\n\r\n.image-item:hover {\r\n    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);\r\n    transform: scale(1.02);\r\n}\r\n\r\n.image-item img {\r\n    max-width: 100%;\r\n    height: 120px;\r\n    width: 120px;\r\n    object-fit: contain;\r\n    display: block;\r\n    margin: 0 auto;\r\n    margin-top: 5px;\r\n}\r\n\r\n\r\n\r\n.image-item .checkbox label {\r\n    display: block;\r\n    cursor: pointer;\r\n    width: 100%;\r\n    height: 100%;\r\n    padding: 0;\r\n    position: relative;\r\n}\r\n\r\n\r\n\r\n.image-item input[type=\"checkbox\"]:checked+img {\r\n    border: 1px solid #28a745;\r\n    box-shadow: 0 0 2px rgba(40, 167, 69, 0.5);\r\n}\r\n\r\n.category-title {\r\n    margin-top: 15px;\r\n    font-weight: bold;\r\n    border-bottom: 1px solid #eee;\r\n    padding-bottom: 5px;\r\n}\r\n\r\n.selected-image {\r\n    width: 160px;\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n    padding: 5px;\r\n    text-align: center;\r\n}\r\n\r\n.selected-image img {\r\n    max-width: 100%;\r\n    max-height: 120px;\r\n    width: 120px;\r\n    height: 120px;\r\n    object-fit: contain;\r\n}\r\n\r\n.selected-image .image-name {\r\n    font-size: 12px;\r\n    margin-top: 5px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    max-width: 150px;\r\n    line-height: 1.2;\r\n}\r\n\r\n/* 添加选中图片的高亮样式 */\r\n.image-item.selected {\r\n    border: 1px solid #28a745;\r\n    box-shadow: 0 0 2px rgba(40, 167, 69, 0.5);\r\n    background-color: #f0fff0;\r\n}\r\n\r\n/* 添加图片网格容器样式 */\r\n#danger-tab .image-grid,\r\n#notice-tab .image-grid,\r\n#protection-tab .image-grid {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: flex-start;\r\n    align-items: flex-start;\r\n    gap: 10px;\r\n    padding: 10px;\r\n}\r\n\r\n/* 图片项样式 */\r\n.image-item {\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n    padding: 5px;\r\n    margin-bottom: 10px;\r\n    background-color: #fff;\r\n    transition: all 0.2s ease;\r\n}\r\n\r\n.image-item:hover {\r\n    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.image-item .image-name {\r\n    font-size: 12px;\r\n    text-align: center;\r\n    margin-top: 5px;\r\n    word-break: break-word;\r\n}\r\n\r\n/* 选项卡激活状态样式 */\r\n.nav-tabs>li.active>a {\r\n    font-weight: bold;\r\n}\r\n\r\n/* 左侧预览区域样式 */\r\n.selected-images-preview {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 10px;\r\n    margin-top: 10px;\r\n}\r\n\r\n.selected-image {\r\n    margin-bottom: 10px;\r\n    text-align: center;\r\n    width: 160px;\r\n}\r\n\r\n.selected-image .image-name {\r\n    font-size: 12px;\r\n    margin-top: 5px;\r\n    word-break: break-word;\r\n}\r\n\r\n/* ========== admin-safety-ledger-preview 页面样式 ========== */\r\n\r\n    /* ========== admin-special-inspection-check-new 页面样式 ========== */\r\n    /* 检查依据和检查要点相关样式 */\r\n    .selected-check-points-container {\r\n        margin-top: 10px !important;\r\n        border: 1px solid #eee !important;\r\n        border-radius: 4px !important;\r\n        padding: 10px !important;\r\n        background-color: #f9f9f9 !important;\r\n        min-height: 100px !important;\r\n        max-height: 200px !important;\r\n        overflow-y: auto !important;\r\n        display: block !important;\r\n    }\r\n\r\n    /* ========== admin-statistics-by-activity 页面样式 ========== */\r\n    .search-box {\r\n        background-color: #f8f9fa;\r\n        padding: 15px;\r\n        border-radius: 5px;\r\n        margin-bottom: 20px;\r\n    }\r\n\r\n    /* ========== admin-statistics-by-hazard-status 页面样式 ========== */\r\n    .search-box {\r\n        background-color: #f8f9fa;\r\n        padding: 15px;\r\n        border-radius: 5px;\r\n        margin-bottom: 20px;\r\n    }\r\n\r\n    /* ========== admin-statistics-by-hazard-type 页面样式 ========== */\r\n    .search-box {\r\n        background-color: #f8f9fa;\r\n        padding: 15px;\r\n        border-radius: 5px;\r\n        margin-bottom: 20px;\r\n    }\r\n\r\n    .table-responsive {\r\n        margin-top: 20px;\r\n    }\r\n\r\n    .sort-icon {\r\n        cursor: pointer;\r\n        margin-left: 5px;\r\n    }\r\n\r\n    /* ========== admin-unified-permission-matrix 页面样式 ========== */\r\n    .matrix-container {\r\n        padding: 20px;\r\n        overflow-x: auto;\r\n    }\r\n\r\n    .permission-matrix {\r\n        width: 100%;\r\n        min-width: 1500px;\r\n        border-collapse: collapse;\r\n        background: white;\r\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n        margin-bottom: 20px;\r\n    }\r\n\r\n    .permission-cell {\r\n        min-width: 120px;\r\n        padding: 4px;\r\n    }\r\n\r\n    /* 一级菜单权限样式 */\r\n    .category-permission-section {\r\n        border-bottom: 3px solid #28a745;\r\n        margin-bottom: 20px;\r\n    }\r\n\r\n    .category-permission-section .permission-cell {\r\n        background: #f0fff0;\r\n    }\r\n\r\n    .category-permission-section th {\r\n        background: #28a745;\r\n    }\r\n\r\n    /* 页面权限样式 */\r\n    .page-permission-section {\r\n        border-bottom: 3px solid #2e6da4;\r\n        margin-bottom: 20px;\r\n    }\r\n\r\n    .page-permission-section .permission-cell {\r\n        background: #f8f9fa;\r\n    }\r\n\r\n    /* 功能权限样式 */\r\n    .operation-permission-section {\r\n        margin-top: 20px;\r\n    }\r\n\r\n    .operation-permission-section .permission-cell {\r\n        background: #fff8f0;\r\n    }\r\n\r\n    .operation-permission-section th {\r\n        background: #d68910;\r\n    }\r\n\r\n    /* 分类行样式 */\r\n    .category-row td {\r\n        background: #e9ecef;\r\n        font-weight: bold;\r\n        color: #495057;\r\n        text-align: left;\r\n        padding: 10px;\r\n    }\r\n\r\n    .resource-category-row td {\r\n        background: #fff3cd;\r\n        font-weight: bold;\r\n        color: #856404;\r\n        text-align: left;\r\n        padding: 10px;\r\n    }\r\n\r\n    .operation-allowed {\r\n        background-color: #d1ecf1 !important;\r\n        border-bottom: 2px solid #17a2b8;\r\n    }\r\n\r\n    .operation-denied {\r\n        background-color: #f5c6cb !important;\r\n        border-bottom: 2px solid #dc3545;\r\n    }\r\n\r\n    /* .table-responsive {\r\n        max-height: 80vh;\r\n        overflow: auto;\r\n        border: 1px solid #dee2e6;\r\n        border-radius: 5px;\r\n    } - 注释掉冲突的样式，使用统一的overflow: visible */\r\n\r\n    .operation-section-title {\r\n        color: #d68910;\r\n        border-left-color: #d68910;\r\n    }\r\n\r\n    /* ========== admin-user-form 页面样式 ========== */\r\n    .checkbox-group {\r\n        max-height: 200px;\r\n        overflow-y: auto;\r\n        border: 1px solid #ddd;\r\n        padding: 10px;\r\n        border-radius: 4px;\r\n    }\r\n\r\n    .checkbox {\r\n        margin-bottom: 5px;\r\n    }\r\n\r\n    .checkbox input[type=\"checkbox\"] {\r\n        margin-right: 5px;\r\n    }\r\n\r\n    /* ========== emails-rectification-notification 页面样式 ========== */\r\n    .email-notification-body {\r\n        font-family: Arial, sans-serif;\r\n        line-height: 1.6;\r\n        color: #333;\r\n        max-width: 800px;\r\n        margin: 0 auto;\r\n        padding: 20px;\r\n    }\r\n\r\n    .header {\r\n        text-align: center;\r\n        margin-bottom: 30px;\r\n    }\r\n\r\n    .header h1 {\r\n        color: #1a5276;\r\n        margin-bottom: 10px;\r\n    }\r\n\r\n    .content {\r\n        margin-bottom: 30px;\r\n    }\r\n\r\n    .lab-info {\r\n        background-color: #f8f9fa;\r\n        padding: 15px;\r\n        border-radius: 5px;\r\n        margin-bottom: 20px;\r\n    }\r\n\r\n    .hazard-list {\r\n        margin-bottom: 30px;\r\n    }\r\n\r\n    .hazard-item {\r\n        margin-bottom: 15px;\r\n        padding-bottom: 15px;\r\n        border-bottom: 1px solid #eee;\r\n    }\r\n\r\n    .hazard-level {\r\n        display: inline-block;\r\n        padding: 3px 8px;\r\n        border-radius: 3px;\r\n        font-size: 12px;\r\n        margin-right: 10px;\r\n    }\r\n\r\n    .level-high {\r\n        background-color: #dc3545;\r\n        color: white;\r\n    }\r\n\r\n    .level-medium {\r\n        background-color: #fd7e14;\r\n        color: white;\r\n    }\r\n\r\n    .level-low {\r\n        background-color: #28a745;\r\n        color: white;\r\n    }\r\n\r\n    .footer {\r\n        margin-top: 30px;\r\n        text-align: center;\r\n        font-size: 12px;\r\n        color: #777;\r\n    }\r\n\r\n    /* ========== inspection-check-form 页面样式 ========== */\r\n    .check-item-panel .panel-title a {\r\n        display: block;\r\n        padding: 5px 0;\r\n    }\r\n\r\n    .check-item-panel .panel-title a:hover {\r\n        text-decoration: none;\r\n    }\r\n\r\n    .check-item-panel .panel-title a:focus {\r\n        text-decoration: none;\r\n    }\r\n\r\n    .check-item-panel .panel-title a .fa-chevron-right {\r\n        transition: transform 0.3s;\r\n    }\r\n\r\n    .check-item-panel .panel-title a:not(.collapsed) .fa-chevron-right {\r\n        transform: rotate(90deg);\r\n    }\r\n\r\n    .item-path {\r\n        color: #777;\r\n        font-weight: normal;\r\n    }\r\n\r\n    .check-points-container {\r\n        margin-top: 10px;\r\n        border: 1px solid #eee;\r\n        border-radius: 4px;\r\n        padding: 15px;\r\n        background-color: #f9f9f9;\r\n    }\r\n\r\n    .check-point-item {\r\n        margin-bottom: 10px;\r\n    }\r\n\r\n    .check-point-item .checkbox label {\r\n        font-weight: normal;\r\n        display: block;\r\n        padding: 10px;\r\n        background-color: #fff;\r\n        border: 1px solid #ddd;\r\n        border-radius: 4px;\r\n    }\r\n\r\n    /* ========== inspection-select-check-points 页面样式 ========== */\r\n\r\n    .check-point-item {\r\n        margin-bottom: 10px;\r\n    }\r\n\r\n    .check-point-item .checkbox {\r\n        margin: 0;\r\n    }\r\n\r\n    .check-point-item .checkbox label {\r\n        font-weight: normal;\r\n        display: block;\r\n        padding: 10px;\r\n        background-color: #f9f9f9;\r\n        border: 1px solid #ddd;\r\n        border-radius: 4px;\r\n    }\r\n\r\n    .check-point-item .checkbox label:hover {\r\n        background-color: #f0f0f0;\r\n    }\r\n\r\n    .check-all-container {\r\n        margin-bottom: 15px;\r\n        padding-bottom: 10px;\r\n        border-bottom: 1px solid #eee;\r\n    }\r\n\r\n    /* ========== inspection-select-laboratory 页面样式 ========== */\r\n    .ml-2 {\r\n        margin-left: 10px;\r\n    }\r\n\r\n    /* ========== main-admin-dashboard 页面样式 ========== */\r\n\r\n    /* ========== main-new-admin-dashboard 页面样式 ========== */\r\n    /* 页面标题区域 - 保持蓝色主题 */\r\n    .page-header-section {\r\n        background: #2e6da4;\r\n        color: white;\r\n        padding: 20px 25px;\r\n        border-radius: 4px;\r\n        margin-bottom: 20px;\r\n        box-shadow: 0 2px 4px rgba(46, 109, 164, 0.2);\r\n    }\r\n\r\n    /* ========== main-template-chooser 页面样式 ========== */\r\n    .template-option {\r\n        margin-bottom: 30px;\r\n        border: 1px solid #ddd;\r\n        border-radius: 4px;\r\n        overflow: hidden;\r\n        transition: all 0.3s ease;\r\n    }\r\n\r\n    .template-option:hover {\r\n        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n        transform: translateY(-5px);\r\n    }\r\n\r\n    .template-header {\r\n        background-color: #f5f5f5;\r\n        padding: 15px;\r\n        border-bottom: 1px solid #ddd;\r\n    }\r\n\r\n    .template-title {\r\n        margin: 0;\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n    }\r\n\r\n    .template-description {\r\n        margin-top: 5px;\r\n        color: #777;\r\n    }\r\n\r\n    .template-preview {\r\n        padding: 15px;\r\n        background-color: #fff;\r\n        text-align: center;\r\n    }\r\n\r\n    .template-preview img {\r\n        max-width: 100%;\r\n        height: auto;\r\n        border: 1px solid #eee;\r\n    }\r\n\r\n    .template-actions {\r\n        padding: 15px;\r\n        background-color: #f9f9f9;\r\n        text-align: center;\r\n    }\r\n\r\n    .btn-select {\r\n        padding: 8px 20px;\r\n    }\r\n\r\n    /* ========== 静态CSS文件完全迁移到Webpack ========== */\r\n\r\n    /* 原 custom.css - Bootstrap Select 下拉框修复 - 使用统一z-index变量 */\r\n    .bootstrap-select .dropdown-menu {\r\n        z-index: var(--z-select) !important;\r\n        position: fixed !important;\r\n        max-height: 300px !important;\r\n        overflow-y: auto !important;\r\n        width: auto !important;\r\n        min-width: 200px !important;\r\n    }\r\n\r\n    .bootstrap-select .bs-searchbox {\r\n        padding: 8px !important;\r\n        position: relative !important;\r\n        /* z-index: 1 !important; - 取消低优先级z-index */\r\n    }\r\n\r\n    .bootstrap-select .bs-searchbox .form-control {\r\n        position: relative !important;\r\n        /* z-index: 2 !important; - 取消低优先级z-index */\r\n        width: 100% !important;\r\n        margin: 0 !important;\r\n        padding: 6px 12px !important;\r\n        border: 1px solid #ccc !important;\r\n        border-radius: 4px !important;\r\n        box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075) !important;\r\n        transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s !important;\r\n    }"], "names": [], "sourceRoot": ""}