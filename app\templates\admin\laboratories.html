{% extends "base.html" %}

{% block title %}实验室管理 - 实验室安全检查系统{% endblock %}

{% block content %}
<!-- 实验室管理页面特定样式 - 放在content区域内，确保AJAX加载时也能获取 -->

<div class="container">
    <h1>实验室管理</h1>
    <div class="mb-3">
        <!-- 根据预加载的权限信息显示按钮 -->
        <a href="{{ url_for('admin.add_laboratory') }}" class="btn btn-success" id="addLabBtn" {% if not
            permissions.create %}hidden{% endif %}>新建实验室</a>
        <a href="{{ url_for('admin.export_labs_template') }}" class="btn btn-info" id="downloadTemplateBtn" {% if not
            permissions.read %}hidden{% endif %}>下载Excel模板</a>
        <a href="{{ url_for('admin.export_labs') }}" class="btn btn-primary" id="exportDataBtn" {% if not
            permissions.read %}hidden{% endif %}>导出全部数据</a>
        <button type="button" class="btn btn-warning" data-toggle="modal" data-target="#importModal" id="importDataBtn"
            {% if not permissions.read %}hidden{% endif %}>导入Excel数据</button>
        <button type="button" class="btn btn-danger" id="generateSafetyCardBtn" disabled>生成安全信息牌</button>
    </div>

    <!-- 搜索表单 -->
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">实验室搜索</h3>
        </div>
        <div class="panel-body">
            <form method="POST" class="form-horizontal">
                {{ search_form.hidden_tag() }}

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">{{ search_form.campus_id.label }}</label>
                            <div class="col-sm-8">
                                {{ search_form.campus_id(class="form-control", id="search_campus_id") }}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">{{ search_form.building_id.label }}</label>
                            <div class="col-sm-8">
                                {{ search_form.building_id(class="form-control", id="search_building_id") }}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">{{ search_form.department_id.label }}</label>
                            <div class="col-sm-8">
                                {{ search_form.department_id(class="form-control") }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">{{ search_form.lab_type.label }}</label>
                            <div class="col-sm-8">
                                {{ search_form.lab_type(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">{{ search_form.risk_level.label }}</label>
                            <div class="col-sm-8">
                                {{ search_form.risk_level(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">实验室属性</label>
                            <div class="col-sm-8">
                                <div class="dropdown property-dropdown">
                                    <button class="btn btn-default dropdown-toggle form-control text-left" type="button"
                                        id="propertyDropdown" data-toggle="dropdown" aria-haspopup="true"
                                        aria-expanded="false">
                                        <span class="selected-text">-- 请选择属性 --</span>
                                        <span class="caret pull-right"></span>
                                    </button>
                                    <div class="dropdown-menu property-dropdown-menu"
                                        aria-labelledby="propertyDropdown">
                                        <div class="dropdown-header">
                                            <div class="checkbox">
                                                <label class="select-all-label">
                                                    <input type="checkbox" id="selectAllProperties">
                                                    全选/取消全选
                                                </label>
                                            </div>
                                        </div>
                                        <div class="divider"></div>
                                        <div class="property-items-wrapper">
                                            {% for value, label in search_form.property_ids.choices %}
                                            <div class="checkbox">
                                                <label class="property-label">
                                                    <input type="checkbox" name="property_ids" value="{{ value }}" {% if
                                                        value|string in request.form.getlist('property_ids') or
                                                        value|string in request.args.getlist('property_ids') %}checked{%
                                                        endif %}>
                                                    {{ label }}
                                                </label>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">{{ search_form.safety_manager.label }}</label>
                            <div class="col-sm-8">
                                {{ search_form.safety_manager(class="form-control", placeholder="输入安全负责人名称") }}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <!-- 预留空间，保持布局平衡 -->
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">{{ search_form.name.label }}</label>
                            <div class="col-sm-8">
                                {{ search_form.name(class="form-control", placeholder="输入实验室名称") }}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">{{ search_form.room_number.label }}</label>
                            <div class="col-sm-8">
                                {{ search_form.room_number(class="form-control", placeholder="输入房间号") }}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">容纳人数</label>
                            <div class="col-sm-4">
                                {{ search_form.capacity_min(class="form-control", placeholder="最小值") }}
                            </div>
                            <div class="col-sm-4">
                                {{ search_form.capacity_max(class="form-control", placeholder="最大值") }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="col-sm-offset-2 col-sm-10">
                        {{ search_form.submit(class="btn btn-primary") }}
                        {{ search_form.reset(class="btn btn-default") }}
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 导入Excel模态框 -->
    <div class="modal fade" id="importModal" tabindex="-1" role="dialog" aria-labelledby="importModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="importModalLabel">导入实验室数据</h4>
                </div>
                <form action="{{ url_for('admin.import_labs') }}" method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        {{ import_form.hidden_tag() }}
                        <div class="form-group">
                            {{ import_form.excel_file.label }}
                            {{ import_form.excel_file(class="form-control") }}
                            {% if import_form.excel_file.errors %}
                            <div class="alert alert-danger">
                                {% for error in import_form.excel_file.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        <p class="text-muted">请使用标准模板，确保数据格式正确。</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        {{ import_form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover laboratories-table" id="laboratoriesTable">
            <thead>
                <tr>
                    <th style="width: 40px;">
                        <input type="checkbox" id="selectAllLabs">
                    </th>
                    <th style="width: 60px;">ID <span class="sort-icon" data-sort="id">⇅</span></th>
                    <th style="width: 80px;">校区 <span class="sort-icon" data-sort="campus">⇅</span></th>
                    <th style="width: 80px;">楼宇 <span class="sort-icon" data-sort="building">⇅</span></th>
                    <th style="width: 80px;">房间号 <span class="sort-icon" data-sort="room">⇅</span></th>
                    <th style="width: 200px;">实验室名称 <span class="sort-icon" data-sort="name">⇅</span></th>
                    <th style="width: 100px;">安全等级 <span class="sort-icon" data-sort="risk">⇅</span></th>
                    <th style="width: 120px;">安全负责人 <span class="sort-icon" data-sort="manager">⇅</span></th>
                    <th style="width: 240px;">操作</th>
                </tr>
            </thead>
            <tbody>
                {% for lab in laboratories %}
                <tr class="lab-row" data-lab-id="{{ lab.id }}">
                    <td>
                        <input type="checkbox" class="lab-checkbox" data-id="{{ lab.id }}">
                    </td>
                    <td>{{ lab.id }}</td>
                    <td>{{ lab.campus }}</td>
                    <td>{{ lab.building }}</td>
                    <td>{{ lab.room_number }}</td>
                    <td class="lab-name-cell" title="{{ lab.name }}">
                        <span class="text-truncate">{{ lab.name }}</span>
                    </td>
                    <td>
                        {% if lab.risk_level %}
                            {% if lab.risk_level.name == '高风险' %}
                                <span class="label label-danger">{{ lab.risk_level.name }}</span>
                            {% elif lab.risk_level.name == '中风险' %}
                                <span class="label label-warning">{{ lab.risk_level.name }}</span>
                            {% else %}
                                <span class="label label-success">{{ lab.risk_level.name }}</span>
                            {% endif %}
                        {% else %}
                            <span class="label label-default">未分级</span>
                        {% endif %}
                    </td>
                    <td class="manager-cell" title="{{ lab.safety_manager }}">
                        <span class="text-truncate">{{ lab.safety_manager }}</span>
                    </td>
                    <td class="action-cell">
                        <!-- 查看详情按钮 -->
                        <button type="button" class="btn btn-sm btn-info" onclick="showLabDetails({{ lab.id }})" title="查看详情">
                            <i class="fas fa-eye"></i> 详情
                        </button>

                        <!-- 编辑按钮 -->
                        <a href="{{ url_for('admin.edit_laboratory', id=lab.id) }}"
                            class="btn btn-sm btn-primary" {% if not permissions.update %}hidden{% endif %} title="编辑">
                            <i class="fas fa-edit"></i> 编辑
                        </a>

                        <!-- 点击展开操作按钮 -->
                        <div class="hover-action-container" onclick="toggleActionButtons(this)">
                            <!-- 默认显示的提示按钮 -->
                            <div class="action-trigger">
                                <span class="btn btn-sm btn-default">
                                    <i class="fas fa-ellipsis-h"></i> 更多操作
                                </span>
                            </div>

                            <!-- 点击时显示的具体按钮 -->
                            <div class="action-buttons">
                                <!-- 生成信息牌 -->
                                <a href="{{ url_for('admin.laboratory_info_card', lab_id=lab.id) }}"
                                   class="btn btn-sm btn-success" title="生成信息牌">
                                    <i class="fas fa-id-card"></i> 信息牌
                                </a>

                                <!-- 历史记录 -->
                                <a href="{{ url_for('admin.laboratory_history', id=lab.id) }}"
                                   class="btn btn-sm btn-warning" title="历史记录">
                                    <i class="fas fa-history"></i> 历史
                                </a>

                                <!-- 删除操作 -->
                                <a href="{{ url_for('admin.delete_laboratory', id=lab.id) }}"
                                   {% if not permissions.delete %}hidden{% endif %}
                                   onclick="return confirm('确定要删除该实验室吗？')"
                                   class="btn btn-sm btn-danger" title="删除">
                                    <i class="fas fa-trash"></i> 删除
                                </a>
                            </div>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- 分页控件 -->
    {% if pagination and pagination.pages > 1 %}
    <div class="text-center">
        <ul class="pagination">
            {% if pagination.has_prev %}
            {% set args = request.args.copy() %}
            {% set _ = args.pop('page', None) %}
            <li><a href="{{ url_for('admin.laboratories', page=pagination.prev_num, **args) }}">&laquo;</a></li>
            {% else %}
            <li class="disabled"><a href="#">&laquo;</a></li>
            {% endif %}

            {% for p in pagination.iter_pages(left_edge=2, left_current=2, right_current=3, right_edge=2) %}
            {% if p %}
            {% if p == pagination.page %}
            <li class="active"><a href="#">{{ p }}</a></li>
            {% else %}
            {% set args = request.args.copy() %}
            {% set _ = args.pop('page', None) %}
            <li><a href="{{ url_for('admin.laboratories', page=p, **args) }}">{{ p }}</a></li>
            {% endif %}
            {% else %}
            <li class="disabled"><a href="#">&hellip;</a></li>
            {% endif %}
            {% endfor %}

            {% if pagination.has_next %}
            {% set args = request.args.copy() %}
            {% set _ = args.pop('page', None) %}
            <li><a href="{{ url_for('admin.laboratories', page=pagination.next_num, **args) }}">&raquo;</a></li>
            {% else %}
            <li class="disabled"><a href="#">&raquo;</a></li>
            {% endif %}
        </ul>
        <div class="pagination-info">
            显示 {{ pagination.total }} 条记录中的第 {{ (pagination.page - 1) * pagination.per_page + 1 }}
            至 {{ min(pagination.page * pagination.per_page, pagination.total) }} 条
            (共 {{ pagination.pages }} 页)
        </div>
    </div>
    {% endif %}
</div>

<!-- 实验室详情模态框 -->
<div class="modal fade" id="labDetailsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">
                    <i class="fas fa-flask"></i> 实验室详细信息
                </h4>
            </div>
            <div class="modal-body" id="labDetailsContent">
                <!-- 详情内容将通过JavaScript动态加载 -->
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block head %}
{{ super() }}
<style>
/* 实验室表格优化样式 */
.laboratories-table {
    table-layout: fixed;
    width: 100%;
}

.laboratories-table th,
.laboratories-table td {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: middle;
}

/* 文本截断样式 */
.text-truncate {
    display: block;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

/* 特定列样式 */
.lab-name-cell {
    max-width: 200px;
}

.manager-cell {
    max-width: 120px;
}

.action-cell {
    white-space: nowrap;
    min-width: 200px;
}

.action-cell .btn {
    margin-right: 3px;
    margin-bottom: 2px;
}

/* 展开按钮的文字样式 */
.action-buttons .btn {
    white-space: nowrap;
    min-width: auto;
}

.action-buttons .btn i {
    margin-right: 3px;
}

/* 风险等级标签样式 */
.label {
    font-size: 11px;
    padding: 3px 6px;
}

/* 详情模态框样式 */
.lab-detail-row {
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.lab-detail-row:last-child {
    border-bottom: none;
}

.lab-detail-label {
    font-weight: bold;
    color: #333;
    margin-bottom: 3px;
}

.lab-detail-value {
    color: #666;
    word-wrap: break-word;
}

/* 点击展开操作按钮样式已移至全局CSS */

/* 响应式优化 */
@media (max-width: 768px) {
    .laboratories-table th,
    .laboratories-table td {
        font-size: 12px;
        padding: 4px;
    }

    .btn-sm {
        padding: 2px 6px;
        font-size: 11px;
    }
}
</style>
{% endblock %}

{% block scripts %}
<!-- 引入实验室管理页面专用模块化JavaScript -->
{% for js_url in webpack_js('laboratories') %}
<script src="{{ js_url }}"></script>
{% endfor %}
<script>
// 实验室详情数据（从后端传递）
const laboratoriesData = {
    {% for lab in laboratories %}
    {{ lab.id }}: {
        id: {{ lab.id }},
        name: "{{ lab.name }}",
        campus: "{{ lab.campus }}",
        building: "{{ lab.building }}",
        department: "{{ lab.department.name if lab.department else '无' }}",
        room_number: "{{ lab.room_number }}",
        lab_type: "{{ lab.lab_type }}",
        risk_level: "{{ lab.risk_level.name if lab.risk_level else '未分级' }}",
        safety_manager: "{{ lab.safety_manager }}",
        contact_phone: "{{ lab.contact_phone }}",
        capacity: {{ lab.capacity if lab.capacity else 0 }},
        property: "{% set property_relation = lab.property_relations.first() %}{% if property_relation %}{{ property_relation.property.name }}{% else %}无{% endif %}",
        equipment: "{{ lab.equipment if lab.equipment else '无' }}",
        description: "{{ lab.description if lab.description else '无' }}"
    }{% if not loop.last %},{% endif %}
    {% endfor %}
};

// toggleActionButtons函数已在全局定义，无需重复定义

// 显示实验室详情
function showLabDetails(labId) {
    const lab = laboratoriesData[labId];
    if (!lab) {
        alert('未找到实验室信息');
        return;
    }

    const detailsHtml = `
        <div class="row">
            <div class="col-md-6">
                <div class="lab-detail-row">
                    <div class="lab-detail-label">实验室ID</div>
                    <div class="lab-detail-value">${lab.id}</div>
                </div>
                <div class="lab-detail-row">
                    <div class="lab-detail-label">实验室名称</div>
                    <div class="lab-detail-value">${lab.name}</div>
                </div>
                <div class="lab-detail-row">
                    <div class="lab-detail-label">校区</div>
                    <div class="lab-detail-value">${lab.campus}</div>
                </div>
                <div class="lab-detail-row">
                    <div class="lab-detail-label">楼宇</div>
                    <div class="lab-detail-value">${lab.building}</div>
                </div>
                <div class="lab-detail-row">
                    <div class="lab-detail-label">房间号</div>
                    <div class="lab-detail-value">${lab.room_number}</div>
                </div>
                <div class="lab-detail-row">
                    <div class="lab-detail-label">所属部门</div>
                    <div class="lab-detail-value">${lab.department}</div>
                </div>
                <div class="lab-detail-row">
                    <div class="lab-detail-label">实验室分类</div>
                    <div class="lab-detail-value">${lab.lab_type}</div>
                </div>
                <div class="lab-detail-row">
                    <div class="lab-detail-label">实验室属性</div>
                    <div class="lab-detail-value">${lab.property}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="lab-detail-row">
                    <div class="lab-detail-label">安全风险等级</div>
                    <div class="lab-detail-value">
                        <span class="label ${lab.risk_level === '高风险' ? 'label-danger' : lab.risk_level === '中风险' ? 'label-warning' : 'label-success'}">${lab.risk_level}</span>
                    </div>
                </div>
                <div class="lab-detail-row">
                    <div class="lab-detail-label">安全负责人</div>
                    <div class="lab-detail-value">${lab.safety_manager}</div>
                </div>
                <div class="lab-detail-row">
                    <div class="lab-detail-label">联系电话</div>
                    <div class="lab-detail-value">${lab.contact_phone}</div>
                </div>
                <div class="lab-detail-row">
                    <div class="lab-detail-label">容纳人数</div>
                    <div class="lab-detail-value">${lab.capacity} 人</div>
                </div>
                <div class="lab-detail-row">
                    <div class="lab-detail-label">设备简述</div>
                    <div class="lab-detail-value">${lab.equipment}</div>
                </div>
                <div class="lab-detail-row">
                    <div class="lab-detail-label">描述</div>
                    <div class="lab-detail-value">${lab.description}</div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('labDetailsContent').innerHTML = detailsHtml;
    $('#labDetailsModal').modal('show');
}

// 页面加载完成后初始化实验室管理功能
document.addEventListener('DOMContentLoaded', function () {
    // 点击页面其他地方时关闭所有展开的按钮
    document.addEventListener('click', function(event) {
        // 检查点击的元素是否在操作按钮容器内
        if (!event.target.closest('.hover-action-container')) {
            // 关闭所有展开的按钮
            document.querySelectorAll('.hover-action-container.expanded').forEach(function(container) {
                container.classList.remove('expanded');
            });
        }
    });

    if (window.LaboratoriesPage) {
        window.LaboratoriesPage.init();
    }
});

// AJAX加载时重新初始化
if (window.UnifiedAjaxMenu) {
    window.UnifiedAjaxMenu.registerPageInitializer('/admin/laboratories', function () {
        if (window.LaboratoriesPage) {
            window.LaboratoriesPage.init();
        }
    });
}
</script>
{% endblock %}