{% extends "admin/base.html" %}

{% block title %}生成信息牌 - {{ laboratory.name }}{% endblock %}

{% block breadcrumb %}
<li><a href="{{ url_for('admin.index') }}">控制面板</a></li>
<li><a href="{{ url_for('admin.laboratories') }}">实验室管理</a></li>
<li class="active">生成信息牌</li>
{% endblock %}

{% block styles %}
{{ super() }}

{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h3 class="page-title">
                <i class="fas fa-id-card"></i> 生成信息牌
                <small>{{ laboratory.name }}</small>
            </h3>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">实验室信息</h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>实验室名称：</strong>{{ laboratory.name }}</p>
                            <p><strong>房间号：</strong>{{ laboratory.room_number }}</p>
                            <p><strong>校区：</strong>{{ laboratory.campus_display if hasattr(laboratory, 'campus_display')
                                else (laboratory.campus.name if laboratory.campus and hasattr(laboratory.campus, 'name')
                                else (laboratory.campus if laboratory.campus else '无')) }}</p>
                            <p><strong>楼宇：</strong>{{ laboratory.building_display if hasattr(laboratory,
                                'building_display') else (laboratory.building.name if laboratory.building and
                                hasattr(laboratory.building, 'name') else (laboratory.building if laboratory.building
                                else '无')) }}</p>
                            <p><strong>部门：</strong>{{ laboratory.department_display if hasattr(laboratory,
                                'department_display') else (laboratory.department.name if laboratory.department and
                                hasattr(laboratory.department, 'name') else (laboratory.department if
                                laboratory.department else '无')) }}</p>
                            <p><strong>实验室分类：</strong>{{ laboratory.lab_type_display if hasattr(laboratory,
                                'lab_type_display') else (laboratory.lab_type.name if laboratory.lab_type and
                                hasattr(laboratory.lab_type, 'name') else (laboratory.lab_type if laboratory.lab_type
                                else '无')) }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>安全风险等级：</strong>
                                {% if hasattr(laboratory, 'risk_level_display') and laboratory.risk_level_display %}
                                {{ laboratory.risk_level_display }}
                                {% else %}
                                未分级
                                {% endif %}
                            </p>
                            <p><strong>实验室管理员：</strong>{{ laboratory.safety_manager or '无' }}</p>
                            <p><strong>联系电话：</strong>{{ laboratory.contact_phone or '无' }}</p>
                            <p><strong>容纳人数：</strong>{{ laboratory.capacity or '无' }}</p>
                            <p><strong>设备列表：</strong>{{ laboratory.equipment or '无' }}</p>
                            <p><strong>描述：</strong>{{ laboratory.description or '无' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <h3 class="panel-title">生成信息牌</h3>
                </div>
                <div class="panel-body">
                    <div class="nav-tabs-custom">
                        <ul class="nav nav-tabs">
                            <li class="active"><a href="#tab_generate" data-toggle="tab">Word模板</a></li>
                            <li><a href="#tab_preview" data-toggle="tab">预览Word模板</a></li>
                            <li><a href="#tab_safety_card" data-toggle="tab">安全信息牌</a></li>
                        </ul>
                        <div class="tab-content">
                            <div class="tab-pane active" id="tab_generate">
                                <form method="POST"
                                    action="{{ url_for('admin.generate_info_card', lab_id=laboratory.id) }}"
                                    id="generateForm">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <div class="form-group">
                                        <label for="template_id">选择Word模板</label>
                                        <select name="template_id" id="template_id" class="form-control">
                                            <option value="">-- 自动选择适合的模板 --</option>
                                            {% if recommended_templates %}
                                            <optgroup label="推荐模板">
                                                {% for template in recommended_templates %}
                                                <option value="{{ template.id }}">{{ template.name }}</option>
                                                {% endfor %}
                                            </optgroup>
                                            {% endif %}
                                            <optgroup label="所有模板">
                                                {% for template in templates %}
                                                <option value="{{ template.id }}">{{ template.name }}</option>
                                                {% endfor %}
                                            </optgroup>
                                        </select>
                                        <small class="text-muted">如果不选择模板，系统将根据实验室风险等级自动选择合适的模板。</small>
                                    </div>

                                    <div class="form-group">
                                        <label for="output_format">输出格式</label>
                                        <select name="output_format" id="output_format" class="form-control">
                                            <option value="docx">Word文档 (.docx)</option>
                                            <option value="pdf">PDF文档 (.pdf)</option>
                                        </select>
                                    </div>

                                    <!-- 安全图片选择区域 -->
                                    <div class="form-group">
                                        <label>安全图片选择（可选）</label>
                                        <small class="text-muted">如果不选择图片，系统将根据实验室风险等级自动选择合适的图片。</small>

                                        <div class="panel-group" id="imageAccordion" style="margin-top: 10px;">
                                            <!-- 危险类别图片 -->
                                            <div class="panel panel-default">
                                                <div class="panel-heading clickable-panel-heading" data-toggle="collapse" data-parent="#imageAccordion" data-target="#dangerImages" style="cursor: pointer;" aria-expanded="false">
                                                    <h4 class="panel-title">
                                                        <span>
                                                            危险类别图片 <small class="text-muted">(最多5个)</small> <span class="badge" id="dangerCount">{{
                                                                selected_danger_images|length }}</span>
                                                        </span>
                                                    </h4>
                                                </div>
                                                <div id="dangerImages" class="panel-collapse collapse">
                                                    <div class="panel-body">
                                                        <div class="row">
                                                            {% for image in danger_images %}
                                                            <div class="col-md-6 col-sm-12"
                                                                style="margin-bottom: 10px;">
                                                                <div class="checkbox">
                                                                    <label>
                                                                        <input type="checkbox"
                                                                            name="selected_danger_images"
                                                                            value="{{ image.id }}" {% if image.id in
                                                                            selected_danger_images %}checked{% endif %}>
                                                                        <img src="{{ url_for('static', filename=image.image_path) }}"
                                                                            alt="{{ image.name }}"
                                                                            style="max-width: 50px; max-height: 50px; margin-right: 10px;">
                                                                        {{ image.name }}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            {% endfor %}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 注意事项图片 -->
                                            <div class="panel panel-default">
                                                <div class="panel-heading clickable-panel-heading" data-toggle="collapse" data-parent="#imageAccordion" data-target="#noticeImages" style="cursor: pointer;" aria-expanded="false">
                                                    <h4 class="panel-title">
                                                        <span>
                                                            注意事项图片 <small class="text-muted">(最多5个)</small> <span class="badge" id="noticeCount">{{
                                                                selected_notice_images|length }}</span>
                                                        </span>
                                                    </h4>
                                                </div>
                                                <div id="noticeImages" class="panel-collapse collapse">
                                                    <div class="panel-body">
                                                        <div class="row">
                                                            {% for image in notice_images %}
                                                            <div class="col-md-6 col-sm-12"
                                                                style="margin-bottom: 10px;">
                                                                <div class="checkbox">
                                                                    <label>
                                                                        <input type="checkbox"
                                                                            name="selected_notice_images"
                                                                            value="{{ image.id }}" {% if image.id in
                                                                            selected_notice_images %}checked{% endif %}>
                                                                        <img src="{{ url_for('static', filename=image.image_path) }}"
                                                                            alt="{{ image.name }}"
                                                                            style="max-width: 50px; max-height: 50px; margin-right: 10px;">
                                                                        {{ image.name }}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            {% endfor %}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 防护措施图片 -->
                                            <div class="panel panel-default">
                                                <div class="panel-heading clickable-panel-heading" data-toggle="collapse" data-parent="#imageAccordion" data-target="#protectionImages" style="cursor: pointer;" aria-expanded="false">
                                                    <h4 class="panel-title">
                                                        <span>
                                                            防护措施图片 <small class="text-muted">(最多5个)</small> <span class="badge" id="protectionCount">{{
                                                                selected_protection_images|length }}</span>
                                                        </span>
                                                    </h4>
                                                </div>
                                                <div id="protectionImages" class="panel-collapse collapse">
                                                    <div class="panel-body">
                                                        <div class="row">
                                                            {% for image in protection_images %}
                                                            <div class="col-md-6 col-sm-12"
                                                                style="margin-bottom: 10px;">
                                                                <div class="checkbox">
                                                                    <label>
                                                                        <input type="checkbox"
                                                                            name="selected_protection_images"
                                                                            value="{{ image.id }}" {% if image.id in
                                                                            selected_protection_images %}checked{% endif
                                                                            %}>
                                                                        <img src="{{ url_for('static', filename=image.image_path) }}"
                                                                            alt="{{ image.name }}"
                                                                            style="max-width: 50px; max-height: 50px; margin-right: 10px;">
                                                                        {{ image.name }}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            {% endfor %}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 灭火要点选择区域 -->
                                    <div class="form-group">
                                        <label>灭火要点选择</label>
                                        <small class="text-muted">选择适用于该实验室的灭火要点，生成的安全信息牌中对应的复选框将被勾选。</small>

                                        <div class="panel panel-default" style="margin-top: 10px;">
                                            <div class="panel-heading clickable-panel-heading" data-toggle="collapse" data-parent="#firePointsAccordion" data-target="#firePointsPanel" style="cursor: pointer;" aria-expanded="false">
                                                <h4 class="panel-title">
                                                    <span>
                                                        灭火要点 <span class="badge" id="firePointsCount">{{
                                                            selected_fire_point_ids|length }}</span>
                                                    </span>
                                                </h4>
                                            </div>
                                            <div id="firePointsPanel" class="panel-collapse collapse">
                                                <div class="panel-body">
                                                    <div class="row">
                                                        {% for fire_point in fire_points %}
                                                        <div class="col-md-6 col-sm-12" style="margin-bottom: 10px;">
                                                            <div class="checkbox">
                                                                <label>
                                                                    <input type="checkbox" name="selected_fire_points"
                                                                        value="{{ fire_point.id }}" {% if fire_point.id
                                                                        in selected_fire_point_ids %}checked{% endif %}>
                                                                    {{ fire_point.order }}. {{ fire_point.name }}
                                                                </label>
                                                            </div>
                                                        </div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary btn-block">
                                            <i class="fas fa-file-export"></i> 生成安全信息牌
                                        </button>
                                    </div>
                                </form>
                            </div>
                            <div class="tab-pane" id="tab_preview">
                                <form method="POST"
                                    action="{{ url_for('admin.preview_laboratory_info_card', lab_id=laboratory.id) }}"
                                    id="previewForm">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <div class="form-group">
                                        <label for="preview_template_id">选择Word模板</label>
                                        <select name="template_id" id="preview_template_id" class="form-control">
                                            <option value="">-- 自动选择适合的模板 --</option>
                                            {% if recommended_templates %}
                                            <optgroup label="推荐模板">
                                                {% for template in recommended_templates %}
                                                <option value="{{ template.id }}">{{ template.name }}</option>
                                                {% endfor %}
                                            </optgroup>
                                            {% endif %}
                                            <optgroup label="所有模板">
                                                {% for template in templates %}
                                                <option value="{{ template.id }}">{{ template.name }}</option>
                                                {% endfor %}
                                            </optgroup>
                                        </select>
                                        <small class="text-muted">如果不选择模板，系统将根据实验室风险等级自动选择合适的模板。</small>
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" class="btn btn-info btn-block">
                                            <i class="fas fa-eye"></i> 预览Word信息牌
                                        </button>
                                    </div>
                                </form>
                                <div class="alert alert-info">
                                    <p><i class="fas fa-info-circle"></i> 预览功能允许您在生成Word文档前查看信息牌的大致内容和布局。</p>
                                </div>
                            </div>

                            <div class="tab-pane" id="tab_safety_card">
                                <div class="alert alert-warning">
                                    <p><i class="fas fa-exclamation-circle"></i>
                                        <strong>安全信息牌</strong>是带有图片的信息牌，根据实验室安全风险等级自动选择合适的图片。
                                    </p>
                                </div>

                                <form method="GET"
                                    action="{{ url_for('admin.preview_safety_card', lab_id=laboratory.id) }}"
                                    id="safetyCardForm">
                                    <div class="form-group">
                                        <p><strong>实验室安全风险等级：</strong>
                                            {% if hasattr(laboratory, 'risk_level_display') and
                                            laboratory.risk_level_display %}
                                            {{ laboratory.risk_level_display }}
                                            {% else %}
                                            未分级
                                            {% endif %}
                                        </p>
                                        <p class="text-muted">系统将根据实验室风险等级自动选择合适的图片。</p>
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" class="btn btn-warning btn-block">
                                            <i class="fas fa-eye"></i> 预览安全信息牌
                                        </button>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <form
                                                action="{{ url_for('admin.generate_safety_card', lab_id=laboratory.id) }}"
                                                method="post">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <input type="hidden" name="output_format" value="docx">
                                                <button type="submit" class="btn btn-success btn-block">
                                                    <i class="fas fa-file-word"></i> 生成Word文档
                                                </button>
                                            </form>
                                        </div>
                                        <div class="col-md-6">
                                            <form
                                                action="{{ url_for('admin.generate_safety_card', lab_id=laboratory.id) }}"
                                                method="post">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <input type="hidden" name="output_format" value="pdf">
                                                <button type="submit" class="btn btn-danger btn-block">
                                                    <i class="fas fa-file-pdf"></i> 生成PDF文档
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </form>

                                <div class="alert alert-info" style="margin-top: 15px;">
                                    <p><i class="fas fa-info-circle"></i> 安全信息牌包含以下图片：</p>
                                    <ul>
                                        <li><strong>危险类别</strong>：根据实验室风险等级自动选择</li>
                                        <li><strong>注意事项</strong>：根据实验室风险等级自动选择</li>
                                        <li><strong>防护措施</strong>：根据实验室风险等级自动选择</li>
                                    </ul>
                                    <p><a href="{{ url_for('admin.safety_card_images') }}" target="_blank">管理安全信息牌图片</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <a href="{{ url_for('admin.laboratories') }}" class="btn btn-default btn-block">
                            <i class="fas fa-arrow-left"></i> 返回实验室列表
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-info">
                <div class="panel-heading">
                    <h3 class="panel-title">Word模板预览</h3>
                </div>
                <div class="panel-body">
                    <div class="row" id="templatePreview">
                        {% for template in templates %}
                        <div class="col-md-4 template-item" data-template-id="{{ template.id }}">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4 class="panel-title">{{ template.name }}</h4>
                                </div>
                                <div class="panel-body">
                                    <p>{{ template.description }}</p>
                                    <p>
                                        <strong>适用风险等级：</strong>
                                        {% if template.risk_level == 1 %}
                                        红色（高危）
                                        {% elif template.risk_level == 2 %}
                                        橙色（较高危）
                                        {% elif template.risk_level == 3 %}
                                        黄色（中危）
                                        {% elif template.risk_level == 4 %}
                                        蓝色（低危）
                                        {% else %}
                                        通用
                                        {% endif %}
                                    </p>
                                    <div class="text-center">
                                        <a href="{{ url_for('admin.download_template', id=template.id) }}"
                                            class="btn btn-sm btn-info">
                                            <i class="fas fa-download"></i> 下载模板
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<style>
/* 可点击面板标题样式 */
.clickable-panel-heading {
    transition: background-color 0.2s ease;
}

.clickable-panel-heading:hover {
    background-color: #f5f5f5 !important;
}

.clickable-panel-heading .panel-title {
    width: 100%;
    display: block;
}

.clickable-panel-heading .panel-title span {
    display: block;
    width: 100%;
    padding: 0;
    color: inherit;
    text-decoration: none;
}

/* 添加展开/收起指示器 */
.clickable-panel-heading::after {
    content: '+' !important; /* 简洁的加号 */
    float: right !important;
    font-size: 16px !important;
    color: #666 !important;
    font-weight: bold !important;
    transition: none !important;
    margin-top: 1px !important;
    width: 16px !important;
    text-align: center !important;
    font-family: Arial, sans-serif !important;
}

.clickable-panel-heading[aria-expanded="true"]::after {
    content: '−' !important; /* 展开时显示减号 */
    transform: none !important;
}

/* 修复badge样式，避免显示为黑色长条 */
.badge {
    display: inline-block !important;
    min-width: 10px !important;
    padding: 3px 7px !important;
    font-size: 12px !important;
    font-weight: bold !important;
    line-height: 1 !important;
    color: #fff !important;
    text-align: center !important;
    white-space: nowrap !important;
    vertical-align: middle !important;
    border-radius: 10px !important;
}

.badge-default {
    background-color: #777 !important;
}

.badge-info {
    background-color: #5bc0de !important;
}

.badge-warning {
    background-color: #f0ad4e !important;
}

.badge-danger {
    background-color: #d9534f !important;
}
</style>
<script>
    $(document).ready(function () {
        // 初始化可点击面板标题
        $('.clickable-panel-heading').on('click', function() {
            var target = $(this).data('target');
            var $target = $(target);

            // 切换折叠状态
            $target.collapse('toggle');

            // 更新aria-expanded属性
            var isExpanded = $target.hasClass('in') || $target.hasClass('show');
            $(this).attr('aria-expanded', !isExpanded);
        });

        // 监听折叠事件来更新aria-expanded属性
        $('.panel-collapse').on('shown.bs.collapse', function() {
            var $heading = $('[data-target="#' + $(this).attr('id') + '"]');
            $heading.attr('aria-expanded', 'true');
        });

        $('.panel-collapse').on('hidden.bs.collapse', function() {
            var $heading = $('[data-target="#' + $(this).attr('id') + '"]');
            $heading.attr('aria-expanded', 'false');
        });

        // 当选择Word模板时，高亮显示对应的模板预览
        $('#template_id, #preview_template_id').change(function () {
            var templateId = $(this).val();
            var sourceId = $(this).attr('id');

            // 同步另一个下拉框
            if (sourceId === 'template_id') {
                $('#preview_template_id').val(templateId);
            } else {
                $('#template_id').val(templateId);
            }

            // 高亮显示对应的模板预览
            $('.template-item').removeClass('active');
            if (templateId) {
                $('.template-item[data-template-id="' + templateId + '"]').addClass('active');
            }
        });

        // 初始化时触发一次change事件
        $('#template_id').trigger('change');

        // 切换标签页时添加样式
        $('.nav-tabs a').click(function () {
            $(this).tab('show');
        });

        // 更新图片选择计数器
        function updateImageCounts() {
            var dangerCount = $('input[name="selected_danger_images"]:checked').length;
            var noticeCount = $('input[name="selected_notice_images"]:checked').length;
            var protectionCount = $('input[name="selected_protection_images"]:checked').length;
            var firePointsCount = $('input[name="selected_fire_points"]:checked').length;

            console.log('更新计数器 - 危险类别:', dangerCount, '注意事项:', noticeCount, '防护措施:', protectionCount, '灭火要点:', firePointsCount);

            // 只显示已选择数量，更简洁
            $('#dangerCount').text(dangerCount);
            $('#noticeCount').text(noticeCount);
            $('#protectionCount').text(protectionCount);
            $('#firePointsCount').text(firePointsCount);

            // 根据选择数量改变徽章颜色
            updateBadgeColor('#dangerCount', dangerCount);
            updateBadgeColor('#noticeCount', noticeCount);
            updateBadgeColor('#protectionCount', protectionCount);
            updateBadgeColor('#firePointsCount', firePointsCount);
        }

        // 根据选择数量更新徽章颜色
        function updateBadgeColor(selector, count) {
            var badge = $(selector);
            badge.removeClass('badge-default badge-info badge-warning badge-danger');

            if (count === 0) {
                badge.addClass('badge-default');
            } else if (count < 3) {
                badge.addClass('badge-info');
            } else if (count < 5) {
                badge.addClass('badge-warning');
            } else {
                badge.addClass('badge-danger');
            }
        }

        // 监听图片选择变化 - 使用事件委托确保动态内容也能监听
        $(document).on('change', 'input[name="selected_danger_images"], input[name="selected_notice_images"], input[name="selected_protection_images"], input[name="selected_fire_points"]', function () {
            var checkboxName = $(this).attr('name');
            var isChecked = $(this).is(':checked');

            console.log('图片选择发生变化:', checkboxName, '选中状态:', isChecked);

            // 如果是选中操作，检查是否超过限制
            if (isChecked) {
                var checkedCount = $('input[name="' + checkboxName + '"]:checked').length;
                var categoryName = '';

                // 确定类别名称
                if (checkboxName === 'selected_danger_images') {
                    categoryName = '危险类别';
                } else if (checkboxName === 'selected_notice_images') {
                    categoryName = '注意事项';
                } else if (checkboxName === 'selected_protection_images') {
                    categoryName = '防护措施';
                } else if (checkboxName === 'selected_fire_points') {
                    // 灭火要点没有数量限制，直接更新计数器
                    updateImageCounts();
                    return;
                }

                // 如果超过5个，取消选中并提示
                if (checkedCount > 5) {
                    $(this).prop('checked', false);
                    alert(categoryName + '图片最多只能选择5个！当前已选择' + (checkedCount - 1) + '个。');
                    return;
                }
            }

            updateImageCounts();
        });

        // 初始化计数器 - 立即执行，不使用延迟
        updateImageCounts();
        console.log('初始化计数器完成');

        // 监听折叠面板的展开/收起事件，保持用户的操作状态
        $('#imageAccordion .panel-collapse').on('show.bs.collapse', function () {
            console.log('面板展开:', $(this).attr('id'));
        });

        $('#imageAccordion .panel-collapse').on('hide.bs.collapse', function () {
            console.log('面板收起:', $(this).attr('id'));
        });
    });
</script>
{% endblock %}