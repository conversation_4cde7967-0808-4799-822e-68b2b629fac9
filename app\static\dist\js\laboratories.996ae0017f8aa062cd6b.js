/*! For license information please see laboratories.996ae0017f8aa062cd6b.js.LICENSE.txt */
!function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){var e,o,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",c=r.toStringTag||"@@toStringTag";function a(t,r,i,c){var a=r&&r.prototype instanceof u?r:u,d=Object.create(a.prototype);return n(d,"_invoke",function(t,n,r){var i,c,a,u=0,d=r||[],s=!1,f={p:0,n:0,v:e,a:h,f:h.bind(e,4),d:function(t,n){return i=t,c=0,a=e,f.n=n,l}};function h(t,n){for(c=t,a=n,o=0;!s&&u&&!r&&o<d.length;o++){var r,i=d[o],h=f.p,p=i[2];t>3?(r=p===n)&&(c=i[4]||3,a=i[5]===e?i[3]:i[5],i[4]=3,i[5]=e):i[0]<=h&&((r=t<2&&h<i[1])?(c=0,f.v=n,f.n=i[1]):h<p&&(r=t<3||i[0]>n||n>p)&&(i[4]=t,i[5]=n,f.n=p,c=0))}if(r||t>1)return l;throw s=!0,n}return function(r,d,p){if(u>1)throw TypeError("Generator is already running");for(s&&1===d&&h(d,p),c=d,a=p;(o=c<2?e:a)||!s;){i||(c?c<3?(c>1&&(f.n=-1),h(c,a)):f.n=a:f.v=a);try{if(u=2,i){if(c||(r="next"),o=i[r]){if(!(o=o.call(i,a)))throw TypeError("iterator result is not an object");if(!o.done)return o;a=o.value,c<2&&(c=0)}else 1===c&&(o=i.return)&&o.call(i),c<2&&(a=TypeError("The iterator does not provide a '"+r+"' method"),c=1);i=e}else if((o=(s=f.n<0)?a:t.call(n,f))!==l)break}catch(t){i=e,c=1,a=t}finally{u=1}}return{value:o,done:s}}}(t,i,c),!0),d}var l={};function u(){}function d(){}function s(){}o=Object.getPrototypeOf;var f=[][i]?o(o([][i]())):(n(o={},i,(function(){return this})),o),h=s.prototype=u.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,n(e,c,"GeneratorFunction")),e.prototype=Object.create(h),e}return d.prototype=s,n(h,"constructor",s),n(s,"constructor",d),d.displayName="GeneratorFunction",n(s,c,"GeneratorFunction"),n(h),n(h,c,"Generator"),n(h,i,(function(){return this})),n(h,"toString",(function(){return"[object Generator]"})),(t=function(){return{w:a,m:p}})()}function n(e,t,o,r){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}n=function(e,t,o,r){if(t)i?i(e,t,{value:o,enumerable:!r,configurable:!r,writable:!r}):e[t]=o;else{function c(t,o){n(e,t,(function(e){return this._invoke(t,o,e)}))}c("next",0),c("throw",1),c("return",2)}},n(e,t,o,r)}function o(e,t,n,o,r,i,c){try{var a=e[i](c),l=a.value}catch(e){return void n(e)}a.done?t(l):Promise.resolve(l).then(o,r)}function r(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,i(o.key),o)}}function i(t){var n=function(t){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,"string");if("object"!=e(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==e(n)?n:n+""}var c=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isInitialized=!1,this.dataLoaded=!1,this.currentSort={column:"",direction:"asc"}},n=[{key:"init",value:function(){var e=this;this.isInitialized?console.log("实验室管理页面已初始化，跳过重复初始化"):(console.log("🏢 初始化实验室管理页面"),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){return e.initializeFeatures()})):this.initializeFeatures(),this.isInitialized=!0)}},{key:"initializeFeatures",value:function(){console.log("🔧 初始化实验室管理页面功能"),this.checkOperationPermissions(),this.dataLoaded=!0,this.initSortingFeature(),this.initSearchFeature(),this.initPropertyDropdown(),this.initRiskLevelColors(),this.initLabSelection(),this.initVisibilityChange(),this.initActionButtons(),this.defineGlobalFunctions(),console.log("✅ 实验室管理页面功能初始化完成")}},{key:"initSortingFeature",value:function(){var e=this,t=document.querySelectorAll(".sort-icon");t.forEach((function(n){n.style.cursor="pointer",n.style.userSelect="none",n.addEventListener("click",(function(n){var o=n.target.dataset.sort;e.currentSort.column===o?e.currentSort.direction="asc"===e.currentSort.direction?"desc":"asc":(e.currentSort.column=o,e.currentSort.direction="asc"),t.forEach((function(e){return e.textContent="⇅"})),n.target.textContent="asc"===e.currentSort.direction?"↑":"↓",e.sortTable(o,e.currentSort.direction)}))}))}},{key:"sortTable",value:function(e,t){var n=document.getElementById("laboratoriesTable");if(n){var o=n.querySelector("tbody"),r=Array.from(o.querySelectorAll("tr"));r.sort((function(n,o){var r,i,c,a,l,u,d={id:1,campus:2,building:3,department:4,room:5,name:6,type:7,risk:8,manager:9,capacity:11,property:12}[e]||1;return"id"===e||"capacity"===e?(r=parseInt(null===(c=n.children[d])||void 0===c?void 0:c.textContent.trim())||0,i=parseInt(null===(a=o.children[d])||void 0===a?void 0:a.textContent.trim())||0):(r=(null===(l=n.children[d])||void 0===l?void 0:l.textContent.trim().toLowerCase())||"",i=(null===(u=o.children[d])||void 0===u?void 0:u.textContent.trim().toLowerCase())||""),r<i?"asc"===t?-1:1:r>i?"asc"===t?1:-1:0})),r.forEach((function(e){return o.appendChild(e)}))}}},{key:"initSearchFeature",value:function(){var e=this,t=document.getElementById("search_campus_id");t&&t.addEventListener("change",(function(t){var n=t.target.value;n&&e.loadBuildingsForCampus(n)}));var n=document.getElementById("reset");n&&n.addEventListener("click",(function(t){t.preventDefault();var n=document.querySelector("form");n&&(n.reset(),document.querySelectorAll('input[name="property_ids"]').forEach((function(e){return e.checked=!1})),e.updatePropertyDropdownText(),n.submit())}))}},{key:"loadBuildingsForCampus",value:(i=t().m((function e(n){var o,r,i,c,a;return t().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,o="/admin/get_buildings_for_search/".concat(n),e.n=1,fetch(o);case 1:return r=e.v,e.n=2,r.json();case 2:i=e.v,(c=document.getElementById("search_building_id"))&&(c.innerHTML="",i.forEach((function(e){var t=document.createElement("option");t.value=e[0],t.textContent=e[1],c.appendChild(t)}))),e.n=4;break;case 3:e.p=3,a=e.v,console.error("加载楼宇选项失败:",a);case 4:return e.a(2)}}),e,null,[[0,3]])})),c=function(){var e=this,t=arguments;return new Promise((function(n,r){var c=i.apply(e,t);function a(e){o(c,n,r,a,l,"next",e)}function l(e){o(c,n,r,a,l,"throw",e)}a(void 0)}))},function(e){return c.apply(this,arguments)})},{key:"initPropertyDropdown",value:function(){var e=this;document.querySelectorAll(".property-dropdown-menu, .property-items-wrapper").forEach((function(e){e.addEventListener("click",(function(e){return e.stopPropagation()}))}));var t=document.getElementById("selectAllProperties");t&&t.addEventListener("change",(function(t){var n=t.target.checked;document.querySelectorAll('input[name="property_ids"]').forEach((function(e){return e.checked=n})),e.updatePropertyDropdownText()}));var n=document.querySelectorAll('input[name="property_ids"]');n.forEach((function(o){o.addEventListener("change",(function(){var o=n.length,r=document.querySelectorAll('input[name="property_ids"]:checked').length;t&&(t.checked=r>0&&r===o),e.updatePropertyDropdownText()}))})),this.updatePropertyDropdownText(),document.querySelectorAll(".property-dropdown-menu .checkbox").forEach((function(e){e.addEventListener("click",(function(e){return e.stopPropagation()}))}))}},{key:"updatePropertyDropdownText",value:function(){var e=document.querySelectorAll('input[name="property_ids"]:checked'),t=document.getElementById("propertyDropdown"),n=null==t?void 0:t.querySelector(".selected-text");n&&(0===e.length?(n.textContent="-- 请选择属性 --",t.classList.remove("btn-has-selection")):1===e.length?(n.textContent=e[0].parentElement.textContent.trim(),t.classList.add("btn-has-selection")):(n.textContent="已选择 ".concat(e.length," 项"),t.classList.add("btn-has-selection")))}},{key:"initRiskLevelColors",value:function(){document.querySelectorAll("#risk_level option").forEach((function(e){var t=e.value,n={red:"#ffcccc",orange:"#ffddcc",yellow:"#ffffcc",blue:"#ccccff"};n[t]&&(e.style.backgroundColor=n[t])})),document.querySelectorAll("table tbody tr").forEach((function(e){var t=e.children[8];if(t){var n=t.textContent.trim(),o={"红":"#ffcccc","橙":"#ffddcc","黄":"#ffffcc","蓝":"#ccccff"};Object.keys(o).forEach((function(e){n.includes(e)&&(t.style.backgroundColor=o[e])}))}}))}},{key:"initLabSelection",value:function(){var e=this,t=document.getElementById("selectAllLabs");t&&t.addEventListener("change",(function(t){var n=t.target.checked;document.querySelectorAll(".lab-checkbox").forEach((function(e){return e.checked=n})),e.updateGenerateButtonState()}));var n=document.querySelectorAll(".lab-checkbox");n.forEach((function(o){o.addEventListener("change",(function(){e.updateGenerateButtonState();var o=document.querySelectorAll(".lab-checkbox:checked").length===n.length;t&&(t.checked=o)}))}));var o=document.getElementById("generateInfoCardBtn");o&&o.addEventListener("click",(function(){var e=[];document.querySelectorAll(".lab-checkbox:checked").forEach((function(t){e.push(t.dataset.id)})),e.length>0?1===e.length?window.location.href="/admin/laboratory_info_card/".concat(e[0]):window.location.href="/admin/batch_generate_info_card?lab_ids=".concat(e.join(",")):alert("请至少选择一个实验室")}));var r=document.getElementById("generateSafetyCardBtn");r&&r.addEventListener("click",(function(){var e=[];document.querySelectorAll(".lab-checkbox:checked").forEach((function(t){e.push(t.dataset.id)})),e.length>0?1===e.length?window.location.href="/admin/laboratory_info_card/".concat(e[0]):window.location.href="/admin/batch_generate_safety_card?lab_ids=".concat(e.join(",")):alert("请至少选择一个实验室")})),this.updateGenerateButtonState()}},{key:"updateGenerateButtonState",value:function(){var e=document.querySelectorAll(".lab-checkbox:checked").length,t=document.getElementById("generateInfoCardBtn");t&&(t.disabled=0===e,t.textContent=e>0?"生成实验室信息牌 (".concat(e,")"):"生成实验室信息牌");var n=document.getElementById("generateSafetyCardBtn");n&&(n.disabled=0===e,n.textContent=e>0?"生成安全信息牌 (".concat(e,")"):"生成安全信息牌")}},{key:"initVisibilityChange",value:function(){var e=this;document.addEventListener("visibilitychange",(function(){"visible"===document.visibilityState&&(console.log("页面变为可见，检查数据加载状态"),e.checkAndReloadData())}))}},{key:"initActionButtons",value:function(){document.addEventListener("click",(function(e){e.target.closest(".hover-action-container")||document.querySelectorAll(".hover-action-container.expanded").forEach((function(e){e.classList.remove("expanded")}))})),console.log("✅ 操作按钮事件监听器已设置")}},{key:"checkOperationPermissions",value:function(){console.log("权限已在服务器端预加载，无需AJAX检查")}},{key:"checkAndReloadData",value:function(){console.log("检查是否需要重新加载数据"),this.dataLoaded?console.log("数据已加载，无需重新加载"):(console.log("标记数据为已加载"),this.dataLoaded=!0)}},{key:"defineGlobalFunctions",value:function(){var e=this;window.loadLaboratoriesData=function(){console.log("全局函数loadLaboratoriesData被调用"),e.checkOperationPermissions(),e.dataLoaded=!0},window.toggleActionButtons=function(e){console.log("🔄 toggleActionButtons被调用 (实验室页面)"),event&&event.stopPropagation(),document.querySelectorAll(".hover-action-container.expanded").forEach((function(t){t!==e&&t.classList.remove("expanded")})),e.classList.toggle("expanded"),console.log("✅ 操作按钮状态已切换")},console.log("✅ toggleActionButtons函数已定义 (实验室页面)")}},{key:"destroy",value:function(){this.isInitialized=!1,this.dataLoaded=!1,console.log("实验室管理页面模块已销毁")}}],n&&r(e.prototype,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,i,c}();window.LaboratoriesPage=new c}();
//# sourceMappingURL=laboratories.996ae0017f8aa062cd6b.js.map