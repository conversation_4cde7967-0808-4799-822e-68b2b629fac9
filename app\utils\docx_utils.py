"""
Word模板处理工具

此模块提供了处理Word模板的工具函数，使用docxtpl库处理Word模板中的占位符。
"""

import os
import re
import uuid
from io import BytesIO
from datetime import datetime
from docxtpl import DocxTemplate
from docxcompose.composer import Composer
from docx import Document
from docx.shared import Pt, Cm, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_ALIGN_VERTICAL
from flask import current_app, url_for
from werkzeug.utils import secure_filename
from app.models_template import Template, TemplateMapping

def analyze_template(template_path):
    """
    分析Word模板中的占位符

    Args:
        template_path: Word模板文件路径

    Returns:
        list: 模板中的占位符列表
    """
    try:
        # 打开模板文件
        print(f"\n\n分析模板: {template_path}")

        # 直接读取文件内容
        with open(template_path, 'rb') as f:
            content = f.read()
            print(f"\n文件大小: {len(content)} 字节")

        # 使用正则表达式直接从文件内容中提取占位符
        # Word文件是二进制文件，但占位符通常是以文本形式存储的
        content_str = content.decode('utf-8', errors='ignore')

        # 尝试不同的正则表达式
        placeholders = []

        # 尝试匹配 {{ name }} 形式
        pattern1 = r'\{\{\s*([\w\d_]+)\s*\}\}'
        matches1 = re.findall(pattern1, content_str)
        placeholders.extend(matches1)
        print(f"\n模式1匹配结果: {matches1}")

        # 尝试匹配 {{name}} 形式
        pattern2 = r'\{\{([\w\d_]+)\}\}'
        matches2 = re.findall(pattern2, content_str)
        placeholders.extend(matches2)
        print(f"\n模式2匹配结果: {matches2}")

        # 尝试匹配 {{ name }}，但允许name中包含非字母数字字符
        pattern3 = r'\{\{\s*([^{}]+?)\s*\}\}'
        matches3 = re.findall(pattern3, content_str)
        placeholders.extend(matches3)
        print(f"\n模式3匹配结果: {matches3}")

        # 去除重复项并去除空格
        unique_placeholders = []
        for placeholder in placeholders:
            clean_placeholder = placeholder.strip()
            if clean_placeholder and clean_placeholder not in unique_placeholders:
                unique_placeholders.append(clean_placeholder)

        print(f"\n最终占位符列表: {unique_placeholders}")

        # 如果没有找到占位符，尝试使用python-docx库读取
        if not unique_placeholders:
            print("\n尝试使用python-docx库读取...")
            try:
                from docx import Document
                import xml.etree.ElementTree as ET
                doc = Document(template_path)

                # 获取模板中的所有文本
                text = ""
                print("\n段落文本:")
                for paragraph in doc.paragraphs:
                    print(f"  - {paragraph.text}")
                    text += paragraph.text + "\n"

                # 遍历表格中的文本
                print("\n表格文本:")
                for i, table in enumerate(doc.tables):
                    print(f"  表格 {i+1}:")
                    for j, row in enumerate(table.rows):
                        for k, cell in enumerate(row.cells):
                            for paragraph in cell.paragraphs:
                                print(f"    - 行 {j+1}, 列 {k+1}: {paragraph.text}")
                                text += paragraph.text + "\n"

                # 检查文本框中的文本
                print("\n文本框文本:")
                try:
                    # 获取文档的XML内容
                    doc_xml = doc._element.xml
                    root = ET.fromstring(doc_xml)

                    # 查找所有文本框
                    textboxes = root.findall('.//{http://schemas.openxmlformats.org/wordprocessingml/2006/main}txbxContent')
                    print(f"  找到 {len(textboxes)} 个文本框")

                    for i, textbox in enumerate(textboxes):
                        print(f"  文本框 {i+1}:")
                        # 获取文本框中的所有文本
                        for elem in textbox.iter():
                            if elem.tag.endswith('}t'):  # 文本元素
                                if elem.text:
                                    print(f"    - {elem.text}")
                                    text += elem.text + "\n"
                except Exception as e:
                    print(f"  读取文本框失败: {str(e)}")

                # 使用正则表达式查找所有占位符
                for pattern in [r'\{\{\s*([\w\d_]+)\s*\}\}', r'\{\{([\w\d_]+)\}\}', r'\{\{\s*([^{}]+?)\s*\}\}']:
                    matches = re.findall(pattern, text)
                    for match in matches:
                        clean_match = match.strip()
                        if clean_match and clean_match not in unique_placeholders:
                            unique_placeholders.append(clean_match)

                print(f"\npython-docx库读取结果: {unique_placeholders}")
            except Exception as e:
                print(f"python-docx库读取失败: {str(e)}")

        return unique_placeholders
    except Exception as e:
        print(f"分析模板失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def replace_textbox_placeholders(template_path, context, output_path):
    """
    替换Word文档文本框中的占位符

    Args:
        template_path: 模板文件路径
        context: 占位符替换上下文
        output_path: 输出文件路径
    """
    try:
        import xml.etree.ElementTree as ET
        from docx import Document
        import zipfile
        import tempfile
        import shutil

        print(f"开始处理文本框占位符替换: {template_path}")
        print(f"上下文数据: {context}")

        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 解压docx文件
            with zipfile.ZipFile(template_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)

            # 处理document.xml文件
            document_xml_path = os.path.join(temp_dir, 'word', 'document.xml')
            if os.path.exists(document_xml_path):
                print("处理document.xml文件")

                # 读取XML内容
                with open(document_xml_path, 'r', encoding='utf-8') as f:
                    xml_content = f.read()

                # 替换占位符
                for placeholder, value in context.items():
                    if value is not None:
                        # 替换 {{ placeholder }} 格式
                        pattern1 = r'\{\{\s*' + re.escape(placeholder) + r'\s*\}\}'
                        xml_content = re.sub(pattern1, str(value), xml_content)
                        print(f"替换占位符: {placeholder} -> {value}")

                # 写回XML文件
                with open(document_xml_path, 'w', encoding='utf-8') as f:
                    f.write(xml_content)

            # 重新打包为docx文件
            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zip_ref:
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_name = os.path.relpath(file_path, temp_dir)
                        zip_ref.write(file_path, arc_name)

            print(f"文本框占位符替换完成: {output_path}")

    except Exception as e:
        print(f"替换文本框占位符失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise

def get_laboratory_data(laboratory):
    """
    获取实验室数据，用于填充模板

    Args:
        laboratory: 实验室对象

    Returns:
        dict: 实验室数据字典
    """
    print(f"\n\n开始获取实验室数据: ID={laboratory.id}, 名称={laboratory.name}")
    print(f"laboratory 类型: {type(laboratory)}")
    print(f"laboratory 属性: {dir(laboratory)}")

    # 基本信息
    lab_data = {
        'id': laboratory.id,
        'name': laboratory.name,
        'room_number': laboratory.room_number,
        'capacity': laboratory.capacity,
        'equipment': laboratory.equipment,
        'description': laboratory.description,
        'safety_manager': laboratory.safety_manager,
        'contact_phone': laboratory.contact_phone,
        'current_date': datetime.now().strftime('%Y-%m-%d'),
    }

    # 校区信息
    print(f"campus: {laboratory.campus_rel}, 类型: {type(laboratory.campus_rel) if laboratory.campus_rel else 'None'}")
    if laboratory.campus_rel:
        try:
            lab_data['campus_name'] = laboratory.campus_rel.name
            print(f"campus_name: {lab_data['campus_name']}")
        except Exception as e:
            print(f"campus 异常: {str(e)}")
            lab_data['campus_name'] = str(laboratory.campus_rel)
    elif laboratory.campus and isinstance(laboratory.campus, str):
        lab_data['campus_name'] = laboratory.campus
        print(f"campus_name: {lab_data['campus_name']} (从字符串属性)")

    # 楼宇信息
    print(f"building: {laboratory.building_rel}, 类型: {type(laboratory.building_rel) if laboratory.building_rel else 'None'}")
    if laboratory.building_rel:
        try:
            lab_data['building_name'] = laboratory.building_rel.name
            print(f"building_name: {lab_data['building_name']}")
        except Exception as e:
            print(f"building 异常: {str(e)}")
            lab_data['building_name'] = str(laboratory.building_rel)
    elif laboratory.building and isinstance(laboratory.building, str):
        lab_data['building_name'] = laboratory.building
        print(f"building_name: {lab_data['building_name']} (从字符串属性)")

    # 部门信息
    print(f"department: {laboratory.department}, 类型: {type(laboratory.department) if laboratory.department else 'None'}")
    if laboratory.department:
        try:
            if hasattr(laboratory.department, 'name'):
                lab_data['department_name'] = laboratory.department.name
                print(f"department_name: {lab_data['department_name']}")
            elif isinstance(laboratory.department, str):
                lab_data['department_name'] = laboratory.department
                print(f"department_name: {lab_data['department_name']} (从字符串属性)")
            else:
                lab_data['department_name'] = str(laboratory.department)
                print(f"department_name: {lab_data['department_name']} (从字符串转换)")
        except Exception as e:
            print(f"department 异常: {str(e)}")
            lab_data['department_name'] = str(laboratory.department)

    # 实验室分类信息
    from ..models_lab_settings import LabType
    print(f"lab_type: {laboratory.lab_type}, 类型: {type(laboratory.lab_type) if laboratory.lab_type else 'None'}")
    if laboratory.lab_type:
        try:
            # 如果 lab_type 是字符串，尝试查询实验室分类对象
            if isinstance(laboratory.lab_type, str):
                print(f"lab_type 是字符串: {laboratory.lab_type}")
                lab_type_obj = LabType.query.filter_by(code=laboratory.lab_type).first()
                print(f"lab_type_obj: {lab_type_obj}")
                if lab_type_obj:
                    lab_data['lab_type_name'] = lab_type_obj.name
                    print(f"lab_type_name: {lab_data['lab_type_name']}")
                else:
                    lab_data['lab_type_name'] = laboratory.lab_type
                    print(f"lab_type_name: {lab_data['lab_type_name']} (直接使用字符串)")
            else:
                # 如果 lab_type 是对象，直接使用其 name 属性
                print(f"lab_type 是对象: {laboratory.lab_type}, 属性: {dir(laboratory.lab_type)}")
                lab_data['lab_type_name'] = laboratory.lab_type.name
                print(f"lab_type_name: {lab_data['lab_type_name']}")
        except Exception as e:
            print(f"lab_type 异常: {str(e)}")
            import traceback
            traceback.print_exc()
            lab_data['lab_type_name'] = str(laboratory.lab_type)

    # 风险等级信息
    from ..models_lab_settings import RiskLevel
    print(f"risk_level: {laboratory.risk_level}, 类型: {type(laboratory.risk_level) if laboratory.risk_level else 'None'}")
    print(f"risk_level_id: {laboratory.risk_level_id}, 类型: {type(laboratory.risk_level_id) if hasattr(laboratory, 'risk_level_id') else 'None'}")

    # 首先尝试使用risk_level_id字段和关联的RiskLevel对象
    if hasattr(laboratory, 'risk_level_id') and laboratory.risk_level_id:
        try:
            # 通过risk_level_id查询风险等级对象
            risk_level_obj = RiskLevel.query.get(laboratory.risk_level_id)
            if risk_level_obj:
                lab_data['risk_level_name'] = risk_level_obj.name
                lab_data['risk_level_color'] = risk_level_obj.color or '#808080'
                print(f"risk_level_name: {lab_data['risk_level_name']} (从risk_level_id获取)")
                return lab_data
        except Exception as e:
            print(f"通过risk_level_id查询风险等级对象失败: {str(e)}")

    # 如果通过risk_level_id没有找到，尝试使用risk_level对象
    if laboratory.risk_level:
        try:
            # 如果risk_level是对象，直接使用其属性
            if hasattr(laboratory.risk_level, 'name'):
                lab_data['risk_level_name'] = laboratory.risk_level.name
                lab_data['risk_level_color'] = laboratory.risk_level.color or '#808080'
                print(f"risk_level_name: {lab_data['risk_level_name']} (从risk_level对象获取)")
                return lab_data

            # 如果risk_level是字符串，尝试查询风险等级对象
            if isinstance(laboratory.risk_level, str):
                print(f"risk_level是字符串: {laboratory.risk_level}")

                # 首先尝试通过ID查询风险等级对象
                try:
                    risk_level_id = int(laboratory.risk_level)
                    risk_level_obj = RiskLevel.query.get(risk_level_id)
                    print(f"通过ID查询风险等级对象: {risk_level_obj}")
                    if risk_level_obj:
                        lab_data['risk_level_name'] = risk_level_obj.name
                        lab_data['risk_level_color'] = risk_level_obj.color or '#808080'
                        print(f"risk_level_name: {lab_data['risk_level_name']} (从ID获取)")
                        return lab_data
                except (ValueError, TypeError):
                    pass

                # 如果通过ID没有找到，尝试通过code查询
                risk_level_obj = RiskLevel.query.filter_by(code=laboratory.risk_level).first()
                print(f"通过code查询风险等级对象: {risk_level_obj}")
                if risk_level_obj:
                    lab_data['risk_level_name'] = risk_level_obj.name
                    lab_data['risk_level_color'] = risk_level_obj.color or '#808080'
                    print(f"risk_level_name: {lab_data['risk_level_name']} (从code获取)")
                    return lab_data

                # 如果没有找到对应的风险等级对象，使用硬编码的映射
                risk_level_map = {
                    'red': ('一级（红色）', '#FF0000'),
                    'orange': ('二级（橙色）', '#FFA500'),
                    'yellow': ('三级（黄色）', '#FFFF00'),
                    'blue': ('四级（蓝色）', '#0000FF'),
                    '1': ('一级（红色）', '#FF0000'),
                    '2': ('二级（橙色）', '#FFA500'),
                    '3': ('三级（黄色）', '#FFFF00'),
                    '4': ('四级（蓝色）', '#0000FF')
                }

                if laboratory.risk_level in risk_level_map:
                    lab_data['risk_level_name'] = risk_level_map[laboratory.risk_level][0]
                    lab_data['risk_level_color'] = risk_level_map[laboratory.risk_level][1]
                    print(f"risk_level_name: {lab_data['risk_level_name']} (从映射表获取)")
                else:
                    # 如果映射表中也没有，直接使用字符串
                    lab_data['risk_level_name'] = laboratory.risk_level
                    lab_data['risk_level_color'] = '#808080'
                    print(f"risk_level_name: {lab_data['risk_level_name']} (直接使用字符串)")
        except Exception as e:
            print(f"risk_level异常: {str(e)}")
            import traceback
            traceback.print_exc()
            lab_data['risk_level_name'] = str(laboratory.risk_level)
            lab_data['risk_level_color'] = '#808080'
    else:
        # 如果没有风险等级信息，设置为未分级
        lab_data['risk_level_name'] = '未分级'
        lab_data['risk_level_color'] = '#808080'
        print("未设置风险等级，使用默认值: 未分级")

    # 添加灭火要点复选框状态
    try:
        from ..models_fire_points import FirePoint, LaboratoryFirePoint

        # 获取该实验室已选择的灭火要点ID列表
        selected_fire_point_ids = [fp.fire_point_id for fp in LaboratoryFirePoint.query.filter_by(laboratory_id=laboratory.id).all()]
        print(f"实验室 {laboratory.id} 已选择的灭火要点ID: {selected_fire_point_ids}")

        # 获取所有灭火要点
        fire_points = FirePoint.query.filter_by(is_active=True).order_by(FirePoint.order).all()
        print(f"所有灭火要点: {[(fp.order, fp.name, fp.id) for fp in fire_points]}")

        # 为每个灭火要点添加复选框状态
        for fire_point in fire_points:
            checkbox_key = f'f{fire_point.order}'  # 简短占位符：f1, f2, f3, f4, f5
            lab_data[checkbox_key] = '☑' if fire_point.id in selected_fire_point_ids else '☐'
            print(f"灭火要点 {fire_point.order}. {fire_point.name}: {lab_data[checkbox_key]}")

    except Exception as e:
        print(f"处理灭火要点数据失败: {str(e)}")
        import traceback
        traceback.print_exc()
        # 如果出错，设置默认的空复选框
        for i in range(1, 6):
            lab_data[f'f{i}'] = '☐'

    return lab_data

def generate_laboratory_card(laboratory, template_id=None):
    """
    生成实验室信息牌

    Args:
        laboratory: 实验室对象
        template_id: 模板ID，如果为None则根据实验室风险等级自动选择模板

    Returns:
        BytesIO: 包含Word文档的内存文件对象
    """
    print(f"\n\n开始生成实验室信息牌: ID={laboratory.id}, 名称={laboratory.name}")
    print(f"template_id: {template_id}")

    # 获取实验室数据
    try:
        lab_data = get_laboratory_data(laboratory)
        print(f"lab_data: {lab_data}")
    except Exception as e:
        print(f"获取实验室数据失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise

    # 选择模板
    try:
        if template_id is None or template_id == "" or template_id == "None":
            # 将风险等级代码转换为数字
            risk_level_code = 0
            print(f"开始根据风险等级选择模板")

            # 首先尝试使用risk_level_id字段
            if hasattr(laboratory, 'risk_level_id') and laboratory.risk_level_id:
                risk_level_code = laboratory.risk_level_id
                print(f"risk_level_code: {risk_level_code} (从risk_level_id获取)")
            # 如果没有risk_level_id，尝试使用risk_level对象
            elif laboratory.risk_level:
                # 如果risk_level是对象，直接使用其id属性
                if hasattr(laboratory.risk_level, 'id'):
                    risk_level_code = laboratory.risk_level.id
                    print(f"risk_level_code: {risk_level_code} (从risk_level对象获取)")
                # 如果risk_level是字符串，尝试查询风险等级对象
                elif isinstance(laboratory.risk_level, str):
                    print(f"risk_level是字符串: {laboratory.risk_level}")

                    # 使用映射表
                    risk_level_map = {
                        'red': 1,  # 一级（红色）
                        'orange': 2,  # 二级（橙色）
                        'yellow': 3,  # 三级（黄色）
                        'blue': 4,  # 四级（蓝色）
                        '1': 1,  # 一级（红色）
                        '2': 2,  # 二级（橙色）
                        '3': 3,  # 三级（黄色）
                        '4': 4   # 四级（蓝色）
                    }

                    if laboratory.risk_level in risk_level_map:
                        risk_level_code = risk_level_map[laboratory.risk_level]
                        print(f"risk_level_code: {risk_level_code} (从映射表获取)")
                    else:
                        # 尝试查询风险等级对象
                        print(f"尝试查询风险等级对象: {laboratory.risk_level}")
                        from ..models_lab_settings import RiskLevel
                        risk_level_obj = RiskLevel.query.filter_by(code=laboratory.risk_level).first()
                        print(f"risk_level_obj: {risk_level_obj}")
                        if risk_level_obj and hasattr(risk_level_obj, 'id'):
                            risk_level_code = risk_level_obj.id
                            print(f"risk_level_code: {risk_level_code} (从对象获取)")

            # 根据风险等级选择模板
            print(f"查询模板: template_type=1, risk_level={risk_level_code}")
            template = Template.query.filter_by(
                template_type=1,  # 实验室信息牌模板
                risk_level=risk_level_code
            ).first()
            print(f"查询结果: {template}")
        else:
            # 使用指定的模板
            print(f"使用指定的模板: {template_id}")
            template = Template.query.get(template_id)
            print(f"查询结果: {template}")
    except Exception as e:
        print(f"选择模板失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise

    # 如果没有找到模板，使用默认模板
    try:
        if not template:
            print("未找到指定模板，尝试使用默认模板")
            template = Template.query.filter_by(template_type=1).first()
            print(f"默认模板: {template}")

        # 如果仍然没有找到模板，返回错误
        if not template:
            print("未找到可用的模板")
            raise ValueError("未找到可用的模板")

        # 获取模板文件路径
        template_path = os.path.join(current_app.root_path, 'uploads', 'templates', template.file_path)
        print(f"模板文件路径: {template_path}")

        # 检查模板文件是否存在
        if not os.path.exists(template_path):
            print(f"模板文件不存在: {template_path}")
            raise ValueError(f"模板文件不存在: {template_path}")

        # 获取模板映射
        mappings = TemplateMapping.query.filter_by(template_id=template.id).all()
        print(f"模板映射数量: {len(mappings)}")

        # 创建上下文数据
        context = {}
        for mapping in mappings:
            field_name = mapping.field_name
            placeholder = mapping.placeholder
            print(f"映射: {placeholder} -> {field_name}")

            # 从实验室数据中获取字段值
            if field_name in lab_data:
                context[placeholder] = lab_data[field_name]
                print(f"  值: {context[placeholder]}")
            else:
                context[placeholder] = ""
                print(f"  值: <空>")

        # 使用DocxTemplate处理模板（处理表格中的占位符）
        print(f"开始处理模板: {template_path}")

        # 创建临时文件
        temp_template = os.path.join(current_app.root_path, 'uploads', 'generated', f"temp_template_{uuid.uuid4()}.docx")
        temp_output = os.path.join(current_app.root_path, 'uploads', 'generated', f"temp_output_{uuid.uuid4()}.docx")

        try:
            # 首先使用DocxTemplate处理表格中的占位符
            doc = DocxTemplate(template_path)
            doc.render(context)
            doc.save(temp_template)
            print("DocxTemplate渲染完成")

            # 然后使用自定义函数处理文本框中的占位符
            replace_textbox_placeholders(temp_template, context, temp_output)
            print("文本框占位符替换完成")

            # 读取最终文档
            with open(temp_output, 'rb') as f:
                output_data = f.read()

            # 保存到内存中
            output = BytesIO(output_data)
            output.seek(0)
            print("文档保存完成")

            return output

        finally:
            # 清理临时文件
            for temp_file in [temp_template, temp_output]:
                if os.path.exists(temp_file):
                    try:
                        os.remove(temp_file)
                    except Exception as e:
                        print(f"删除临时文件失败: {temp_file}, 错误: {str(e)}")
    except Exception as e:
        print(f"生成信息牌失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise

def generate_batch_laboratory_cards(laboratories, template_id=None, cards_per_page=1):
    """
    批量生成实验室信息牌

    Args:
        laboratories: 实验室对象列表
        template_id: 模板ID，如果为None则根据实验室风险等级自动选择模板
        cards_per_page: 每页信息牌数量，默认为1

    Returns:
        BytesIO: 包含Word文档的内存文件对象
    """
    # 如果没有实验室，返回错误
    if not laboratories:
        raise ValueError("未选择实验室")

    # 创建一个空的文档作为主文档
    master = Document()

    # 设置页面大小为A4
    section = master.sections[0]
    section.page_width = Cm(21)
    section.page_height = Cm(29.7)
    section.left_margin = Cm(1)
    section.right_margin = Cm(1)
    section.top_margin = Cm(1)
    section.bottom_margin = Cm(1)

    if cards_per_page == 2:
        # 多信息牌模式：每页2个信息牌
        return generate_multi_cards_per_page(laboratories, template_id, cards_per_page)

    else:
        # 传统模式：每页一个信息牌
        composer = Composer(master)
        for i, laboratory in enumerate(laboratories):
            try:
                # 生成单个实验室信息牌
                card_output = generate_laboratory_card(laboratory, template_id)
                card_doc = Document(card_output)
                composer.append(card_doc)

                # 如果不是最后一个实验室，添加分页符
                if i < len(laboratories) - 1:
                    composer.add_paragraph().add_run().add_break()
            except Exception as e:
                print(f"生成实验室 {laboratory.name} 的信息牌失败: {str(e)}")

        # 保存到内存中
        output = BytesIO()
        composer.save(output)
        output.seek(0)
        return output

def generate_multi_cards_per_page(laboratories, template_id=None, cards_per_page=2):
    """
    生成每页多个信息牌的文档

    Args:
        laboratories: 实验室对象列表
        template_id: 模板ID
        cards_per_page: 每页信息牌数量

    Returns:
        BytesIO: 包含Word文档的内存文件对象
    """
    from docx.shared import Inches
    from docx.enum.table import WD_TABLE_ALIGNMENT
    from docx.enum.text import WD_ALIGN_PARAGRAPH

    # 创建主文档
    master = Document()

    # 删除默认的空段落
    if master.paragraphs:
        p = master.paragraphs[0]
        p._element.getparent().remove(p._element)

    # 设置页面大小为A4
    section = master.sections[0]
    section.page_width = Cm(21)
    section.page_height = Cm(29.7)
    section.left_margin = Cm(0.5)
    section.right_margin = Cm(0.5)
    section.top_margin = Cm(0.5)
    section.bottom_margin = Cm(0.5)

    # 按每页的数量分组处理实验室
    for page_start in range(0, len(laboratories), cards_per_page):
        page_labs = laboratories[page_start:page_start + cards_per_page]

        if cards_per_page == 2:
            # 2个信息牌每页：上下布局
            generate_two_cards_per_page(master, page_labs, template_id)

        # 如果不是最后一页，添加分页符
        if page_start + cards_per_page < len(laboratories):
            master.add_page_break()

    # 保存到内存中
    output = BytesIO()
    master.save(output)
    output.seek(0)
    return output

def generate_two_cards_per_page(master_doc, laboratories, template_id=None):
    """
    在一页中生成2个信息牌（上下布局）
    """
    from docx.shared import Inches, Cm
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.table import WD_TABLE_ALIGNMENT

    # 创建一个2行1列的表格来放置2个信息牌
    table = master_doc.add_table(rows=2, cols=1)
    table.alignment = WD_TABLE_ALIGNMENT.CENTER

    # 设置表格宽度为页面宽度
    table.autofit = False
    table.columns[0].width = Cm(19)  # A4页面宽度减去边距

    # 设置每行的高度
    for row in table.rows:
        row.height = Cm(13.5)  # 大约半页高度

    for i, laboratory in enumerate(laboratories):
        if i >= 2:  # 最多2个信息牌
            break

        try:
            # 生成单个实验室信息牌
            card_output = generate_laboratory_card(laboratory, template_id)
            card_doc = Document(card_output)

            # 获取对应的表格单元格
            cell = table.cell(i, 0)

            # 将信息牌内容复制到表格单元格，并缩放
            copy_card_content_to_cell(cell, card_doc)

        except Exception as e:
            print(f"生成实验室 {laboratory.name} 的信息牌失败: {str(e)}")
            # 在对应单元格中添加错误信息
            if i < 2:
                cell = table.cell(i, 0)
                error_para = cell.add_paragraph(f"生成 {laboratory.name} 信息牌失败: {str(e)}")
                error_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

def copy_card_content_to_master(master_doc, card_doc, scale_factor=1.0):
    """
    将信息牌内容复制到主文档，支持缩放
    """
    from docx.shared import Inches
    import copy

    # 简化方法：直接复制元素，跳过空段落
    for element in card_doc.element.body:
        # 跳过空的段落元素
        if element.tag.endswith('p'):
            # 检查段落是否为空
            text_content = ''.join(element.itertext()).strip()
            if not text_content:
                continue

        # 创建元素的深拷贝
        new_element = copy.deepcopy(element)
        master_doc.element.body.append(new_element)

def copy_card_content_to_cell(cell, card_doc):
    """
    将信息牌内容复制到表格单元格中
    """
    import copy

    # 清空单元格现有内容，但保留第一个段落
    while len(cell.paragraphs) > 1:
        p = cell.paragraphs[-1]
        p._element.getparent().remove(p._element)

    # 清空第一个段落的内容
    if cell.paragraphs:
        cell.paragraphs[0].clear()

    # 标记是否是第一个元素
    first_element = True

    # 复制信息牌内容到单元格
    for element in card_doc.element.body:
        # 跳过空的段落元素
        if element.tag.endswith('p'):
            text_content = ''.join(element.itertext()).strip()
            if not text_content:
                continue

        # 创建元素的深拷贝
        new_element = copy.deepcopy(element)

        # 如果是第一个段落元素，替换现有的空段落
        if first_element and element.tag.endswith('p') and cell.paragraphs:
            # 替换第一个段落的内容
            cell.paragraphs[0]._element.getparent().replace(cell.paragraphs[0]._element, new_element)
            first_element = False
        else:
            # 添加到单元格
            cell._element.append(new_element)

def convert_to_pdf(docx_file):
    """
    将Word文档转换为PDF

    Args:
        docx_file: Word文档的BytesIO对象

    Returns:
        BytesIO: 包含PDF文档的内存文件对象
    """
    # 直接从Word文档内容生成PDF，避免依赖外部程序
    try:
        print("使用reportlab直接从Word内容生成PDF...")
        return create_pdf_from_docx_content(docx_file)

    except Exception as e:
        print(f"直接PDF生成失败: {str(e)}")

        # 备用方案：尝试docx2pdf（需要Word或LibreOffice）
        try:
            print("尝试使用docx2pdf转换...")
            import sys
            import uuid

            # 在Windows上初始化COM组件
            if sys.platform == "win32":
                try:
                    import pythoncom
                    pythoncom.CoInitialize()
                except ImportError:
                    print("警告: 无法导入pythoncom，COM初始化可能失败")
                except Exception as e:
                    print(f"COM初始化失败: {str(e)}")

            try:
                from docx2pdf import convert

                # 确保上传目录存在
                upload_dir = os.path.join(current_app.root_path, 'uploads', 'generated')
                os.makedirs(upload_dir, exist_ok=True)

                # 创建临时文件
                temp_docx = os.path.join(upload_dir, f"{uuid.uuid4()}.docx")
                temp_pdf = os.path.join(upload_dir, f"{uuid.uuid4()}.pdf")

                try:
                    # 保存Word文档到临时文件
                    with open(temp_docx, 'wb') as f:
                        f.write(docx_file.getvalue())

                    # 转换为PDF
                    convert(temp_docx, temp_pdf)

                    # 检查PDF文件是否生成
                    if not os.path.exists(temp_pdf):
                        raise Exception("PDF文件未能生成")

                    # 读取PDF文件
                    with open(temp_pdf, 'rb') as f:
                        pdf_data = f.read()

                    # 返回PDF数据
                    output = BytesIO(pdf_data)
                    output.seek(0)
                    return output

                finally:
                    # 清理临时文件
                    try:
                        if os.path.exists(temp_docx):
                            os.remove(temp_docx)
                        if os.path.exists(temp_pdf):
                            os.remove(temp_pdf)
                    except Exception as cleanup_error:
                        print(f"清理临时文件失败: {str(cleanup_error)}")

            finally:
                # 在Windows上清理COM组件
                if sys.platform == "win32":
                    try:
                        import pythoncom
                        pythoncom.CoUninitialize()
                    except:
                        pass

        except Exception as backup_error:
            print(f"docx2pdf转换也失败: {str(backup_error)}")
            raise Exception(f"PDF转换失败: 直接生成失败({str(e)})，docx2pdf也失败({str(backup_error)})")


def create_pdf_from_docx_content(docx_file):
    """
    直接从Word文档内容生成PDF

    Args:
        docx_file: Word文档的BytesIO对象

    Returns:
        BytesIO: PDF文档的内存文件对象
    """
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import cm
        from reportlab.lib import colors
        from reportlab.pdfmetrics import registerFont
        from reportlab.pdfbase.ttfonts import TTFont
        from docx import Document
        import platform
        import os

        # 读取Word文档
        doc = Document(docx_file)

        # 注册中文字体
        chinese_font = 'Helvetica'
        try:
            system = platform.system()
            if system == "Windows":
                font_paths = [
                    "C:/Windows/Fonts/simsun.ttc",
                    "C:/Windows/Fonts/msyh.ttc",
                    "C:/Windows/Fonts/simhei.ttf",
                ]
            elif system == "Darwin":
                font_paths = [
                    "/System/Library/Fonts/PingFang.ttc",
                    "/Library/Fonts/Arial Unicode MS.ttf",
                ]
            else:
                font_paths = [
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                    "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                ]

            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        registerFont(TTFont('ChineseFont', font_path))
                        chinese_font = 'ChineseFont'
                        break
                    except Exception:
                        continue
        except Exception:
            pass

        # 创建PDF缓冲区
        buffer = BytesIO()

        # 创建PDF文档
        pdf_doc = SimpleDocTemplate(buffer, pagesize=A4,
                                  rightMargin=2*cm, leftMargin=2*cm,
                                  topMargin=2*cm, bottomMargin=2*cm)

        # 获取样式
        styles = getSampleStyleSheet()

        # 创建自定义样式
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=20,
            alignment=1,  # 居中
            fontName=chinese_font
        )

        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=8,
            fontName=chinese_font
        )

        # 构建PDF内容
        story = []

        # 处理Word文档内容
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if not text:
                continue

            # 根据样式判断标题级别
            style_name = paragraph.style.name if paragraph.style else ""

            if "Heading 1" in style_name or "标题 1" in style_name or "Title" in style_name:
                story.append(Paragraph(text, title_style))
                story.append(Spacer(1, 12))
            else:
                story.append(Paragraph(text, normal_style))
                story.append(Spacer(1, 6))

        # 处理表格
        for table in doc.tables:
            table_data = []
            for row in table.rows:
                row_data = []
                for cell in row.cells:
                    cell_text = cell.text.strip()
                    row_data.append(cell_text)
                table_data.append(row_data)

            if table_data:
                # 计算列宽
                num_cols = len(table_data[0]) if table_data else 1
                col_width = (A4[0] - 4*cm) / num_cols

                pdf_table = Table(table_data, colWidths=[col_width] * num_cols)
                pdf_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, -1), chinese_font),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('FONTSIZE', (0, 1), (-1, -1), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                story.append(pdf_table)
                story.append(Spacer(1, 12))

        # 如果没有内容，添加默认内容
        if not story:
            story.append(Paragraph("安全信息牌", title_style))
            story.append(Spacer(1, 20))
            story.append(Paragraph("本文档由系统自动生成", normal_style))

        # 构建PDF
        pdf_doc.build(story)

        # 返回PDF数据
        buffer.seek(0)
        return buffer

    except Exception as e:
        print(f"从Word内容生成PDF失败: {str(e)}")
        raise Exception(f"从Word内容生成PDF失败: {str(e)}")
