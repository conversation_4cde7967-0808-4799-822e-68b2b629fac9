{% extends "base.html" %}

{% block title %}实验室分类管理 - 实验室安全检查系统{% endblock %}

{% block content %}
<div class="container">
    <h1>实验室分类管理</h1>
    <div class="mb-3">
        <a href="{{ url_for('admin.add_lab_type') }}" class="btn btn-success">
            <i class="fas fa-plus"></i> 新建分类
        </a>
    </div>

    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">实验室分类列表</h3>
        </div>
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover" id="labTypesTable">
                <thead>
                    <tr>
                        <th>ID <span class="sort-icon" data-sort="id">⇅</span></th>
                        <th>分类代码 <span class="sort-icon" data-sort="code">⇅</span></th>
                        <th>分类名称 <span class="sort-icon" data-sort="name">⇅</span></th>
                        <th>分类描述</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for lab_type in lab_types %}
                    <tr>
                        <td>{{ lab_type.id }}</td>
                        <td>{{ lab_type.code }}</td>
                        <td>{{ lab_type.name }}</td>
                        <td>{{ lab_type.description }}</td>
                        <td>
                            <a href="{{ url_for('admin.edit_lab_type', id=lab_type.id) }}"
                                class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i> 编辑
                            </a>
                            <a href="javascript:void(0)" class="btn btn-sm btn-danger delete-with-captcha"
                                data-url="{{ url_for('admin.delete_lab_type', id=lab_type.id) }}" data-title="确认删除实验室分类"
                                data-message="确定要删除实验室分类 &quot;{{ lab_type.name }}&quot; 吗？此操作不可恢复！">
                                <i class="fas fa-trash"></i> 删除
                            </a>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="5" class="text-center">暂无实验室分类数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    $(document).ready(function () {
        // 排序功能
        var currentSort = { column: '', direction: 'asc' };

        $('.sort-icon').click(function () {
            var column = $(this).data('sort');

            // 切换排序方向
            if (currentSort.column === column) {
                currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.column = column;
                currentSort.direction = 'asc';
            }

            // 重置所有排序图标
            $('.sort-icon').text('⇅');

            // 设置当前排序图标
            $(this).text(currentSort.direction === 'asc' ? '↑' : '↓');

            // 执行排序
            sortTable(column, currentSort.direction);
        });

        // 表格排序函数
        function sortTable(column, direction) {
            var rows = $('#labTypesTable tbody tr').get();

            rows.sort(function (a, b) {
                var A, B;

                if (column === 'id') {
                    A = parseInt($(a).children('td').eq(0).text().trim());
                    B = parseInt($(b).children('td').eq(0).text().trim());
                } else if (column === 'code') {
                    A = $(a).children('td').eq(1).text().trim().toLowerCase();
                    B = $(b).children('td').eq(1).text().trim().toLowerCase();
                } else if (column === 'name') {
                    A = $(a).children('td').eq(2).text().trim().toLowerCase();
                    B = $(b).children('td').eq(2).text().trim().toLowerCase();
                }

                if (A < B) {
                    return direction === 'asc' ? -1 : 1;
                }
                if (A > B) {
                    return direction === 'asc' ? 1 : -1;
                }
                return 0;
            });

            $.each(rows, function (index, row) {
                $('#labTypesTable tbody').append(row);
            });
        }

        // 样式优化
        $('.sort-icon').css({
            'cursor': 'pointer',
            'user-select': 'none'
        });
    });
</script>
{% endblock %}