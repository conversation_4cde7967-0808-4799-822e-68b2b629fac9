"""
实验室信息牌生成视图模块

此模块定义了实验室信息牌生成相关的视图函数，包括单个实验室信息牌生成和批量生成功能。
"""

from flask import render_template, redirect, url_for, flash, request, jsonify, current_app, send_file, send_from_directory, Response, session
from . import admin
from .. import db
from ..models import Laboratory
from ..models_template import Template, TemplateMapping

from ..utils.docx_utils import generate_laboratory_card, generate_batch_laboratory_cards, convert_to_pdf, get_laboratory_data
from ..utils.safety_card_utils import generate_safety_card_with_images
from ..admin.word_to_html import parse_word_document
from ..utils.pandoc_converter import convert_docx_to_html, convert_docx_to_html_fallback
from ..utils.wkhtmltopdf_converter import convert_docx_to_html_with_mammoth
from ..utils.pdf2html_converter import convert_docx_to_html_with_pdf2htmlex, copy_html_template
from ..models_safety_card_image import SafetyCardImage
from flask_login import login_required, current_user
from ..utils.decorators import admin_required, department_admin_required, school_admin_required, safety_admin_required, lab_admin_required, admin_or_department_admin_required, admin_or_school_admin_required, admin_or_safety_admin_required
import os
import tempfile
import time
import shutil
from io import BytesIO

@admin.route('/laboratories/<int:lab_id>/info_card')
@login_required
def laboratory_info_card(lab_id):
    """显示实验室信息牌生成页面"""
    laboratory = Laboratory.query.get_or_404(lab_id)

    # 获取所有可用的模板
    templates = Template.query.filter_by(template_type=1).all()  # 1 = 实验室信息牌模板



    # 预处理实验室数据，确保风险等级和实验室分类信息正确
    from ..models_lab_settings import LabType, RiskLevel

    # 处理部门信息
    if hasattr(laboratory, 'department') and laboratory.department:
        if isinstance(laboratory.department, str):
            laboratory.department_display = laboratory.department
        elif hasattr(laboratory.department, 'name'):
            laboratory.department_display = laboratory.department.name
        else:
            laboratory.department_display = str(laboratory.department)
    else:
        laboratory.department_display = '无'

    # 处理校区信息
    if hasattr(laboratory, 'campus_rel') and laboratory.campus_rel:
        if isinstance(laboratory.campus_rel, str):
            laboratory.campus_display = laboratory.campus_rel
        elif hasattr(laboratory.campus_rel, 'name'):
            laboratory.campus_display = laboratory.campus_rel.name
        else:
            laboratory.campus_display = str(laboratory.campus_rel)
    elif hasattr(laboratory, 'campus') and laboratory.campus:
        laboratory.campus_display = laboratory.campus
    else:
        laboratory.campus_display = '无'

    # 处理楼宇信息
    if hasattr(laboratory, 'building_rel') and laboratory.building_rel:
        if isinstance(laboratory.building_rel, str):
            laboratory.building_display = laboratory.building_rel
        elif hasattr(laboratory.building_rel, 'name'):
            laboratory.building_display = laboratory.building_rel.name
        else:
            laboratory.building_display = str(laboratory.building_rel)
    elif hasattr(laboratory, 'building') and laboratory.building:
        laboratory.building_display = laboratory.building
    else:
        laboratory.building_display = '无'

    # 处理实验室分类
    if hasattr(laboratory, 'lab_type') and laboratory.lab_type:
        if isinstance(laboratory.lab_type, str):
            # 如果是字符串，尝试查找对应的LabType对象
            lab_type_obj = LabType.query.filter_by(code=laboratory.lab_type).first()
            if lab_type_obj:
                laboratory.lab_type_display = lab_type_obj.name
            else:
                laboratory.lab_type_display = laboratory.lab_type
        elif hasattr(laboratory.lab_type, 'name'):
            # 如果是对象，直接使用name属性
            laboratory.lab_type_display = laboratory.lab_type.name
        else:
            laboratory.lab_type_display = str(laboratory.lab_type)
    else:
        laboratory.lab_type_display = '无'

    # 处理风险等级
    risk_level_code = 0
    if hasattr(laboratory, 'risk_level_id') and laboratory.risk_level_id:
        # 通过risk_level_id查询风险等级对象
        risk_level_obj = RiskLevel.query.get(laboratory.risk_level_id)
        if risk_level_obj:
            laboratory.risk_level_display = risk_level_obj.name
            # 根据风险等级名称或代码设置颜色
            if hasattr(risk_level_obj, 'color') and risk_level_obj.color:
                laboratory.risk_level_color = risk_level_obj.color
            else:
                # 根据名称或代码推断颜色
                if '一级' in risk_level_obj.name or '红' in risk_level_obj.name or risk_level_obj.code == '1' or risk_level_obj.code == 'red':
                    laboratory.risk_level_color = 'danger'
                elif '二级' in risk_level_obj.name or '橙' in risk_level_obj.name or risk_level_obj.code == '2' or risk_level_obj.code == 'orange':
                    laboratory.risk_level_color = 'warning'
                elif '三级' in risk_level_obj.name or '黄' in risk_level_obj.name or risk_level_obj.code == '3' or risk_level_obj.code == 'yellow':
                    laboratory.risk_level_color = 'info'
                elif '四级' in risk_level_obj.name or '蓝' in risk_level_obj.name or risk_level_obj.code == '4' or risk_level_obj.code == 'blue':
                    laboratory.risk_level_color = 'primary'
                else:
                    laboratory.risk_level_color = 'default'
            risk_level_code = risk_level_obj.id
        else:
            laboratory.risk_level_display = '未分级'
            laboratory.risk_level_color = 'default'
    elif hasattr(laboratory, 'risk_level') and laboratory.risk_level:
        if isinstance(laboratory.risk_level, str):
            # 如果是字符串，使用映射表
            risk_level_map = {
                'red': ('一级（红色）', 'danger', 1),
                'orange': ('二级（橙色）', 'warning', 2),
                'yellow': ('三级（黄色）', 'info', 3),
                'blue': ('四级（蓝色）', 'primary', 4),
                '1': ('一级（红色）', 'danger', 1),
                '2': ('二级（橙色）', 'warning', 2),
                '3': ('三级（黄色）', 'info', 3),
                '4': ('四级（蓝色）', 'primary', 4)
            }
            if laboratory.risk_level in risk_level_map:
                laboratory.risk_level_display = risk_level_map[laboratory.risk_level][0]
                laboratory.risk_level_color = risk_level_map[laboratory.risk_level][1]
                risk_level_code = risk_level_map[laboratory.risk_level][2]
            else:
                # 尝试通过code查询风险等级对象
                risk_level_obj = RiskLevel.query.filter_by(code=laboratory.risk_level).first()
                if risk_level_obj:
                    laboratory.risk_level_display = risk_level_obj.name
                    if hasattr(risk_level_obj, 'color') and risk_level_obj.color:
                        laboratory.risk_level_color = risk_level_obj.color
                    else:
                        # 根据名称推断颜色
                        if '一级' in risk_level_obj.name or '红' in risk_level_obj.name:
                            laboratory.risk_level_color = 'danger'
                        elif '二级' in risk_level_obj.name or '橙' in risk_level_obj.name:
                            laboratory.risk_level_color = 'warning'
                        elif '三级' in risk_level_obj.name or '黄' in risk_level_obj.name:
                            laboratory.risk_level_color = 'info'
                        elif '四级' in risk_level_obj.name or '蓝' in risk_level_obj.name:
                            laboratory.risk_level_color = 'primary'
                        else:
                            laboratory.risk_level_color = 'default'
                    risk_level_code = risk_level_obj.id
                else:
                    laboratory.risk_level_display = laboratory.risk_level
                    laboratory.risk_level_color = 'default'
        elif hasattr(laboratory.risk_level, 'name'):
            # 如果是对象，直接使用name属性
            laboratory.risk_level_display = laboratory.risk_level.name

            # 根据风险等级名称或代码设置颜色
            if hasattr(laboratory.risk_level, 'color') and laboratory.risk_level.color:
                laboratory.risk_level_color = laboratory.risk_level.color
            else:
                # 根据名称或代码推断颜色
                if hasattr(laboratory.risk_level, 'code'):
                    code = laboratory.risk_level.code
                    if code == '1' or code == 'red' or code == '一级':
                        laboratory.risk_level_color = 'danger'
                    elif code == '2' or code == 'orange' or code == '二级':
                        laboratory.risk_level_color = 'warning'
                    elif code == '3' or code == 'yellow' or code == '三级':
                        laboratory.risk_level_color = 'info'
                    elif code == '4' or code == 'blue' or code == '四级':
                        laboratory.risk_level_color = 'primary'
                    else:
                        # 根据名称推断颜色
                        name = laboratory.risk_level.name
                        if '一级' in name or '红' in name:
                            laboratory.risk_level_color = 'danger'
                        elif '二级' in name or '橙' in name:
                            laboratory.risk_level_color = 'warning'
                        elif '三级' in name or '黄' in name:
                            laboratory.risk_level_color = 'info'
                        elif '四级' in name or '蓝' in name:
                            laboratory.risk_level_color = 'primary'
                        else:
                            laboratory.risk_level_color = 'default'
                else:
                    # 根据名称推断颜色
                    name = laboratory.risk_level.name
                    if '一级' in name or '红' in name:
                        laboratory.risk_level_color = 'danger'
                    elif '二级' in name or '橙' in name:
                        laboratory.risk_level_color = 'warning'
                    elif '三级' in name or '黄' in name:
                        laboratory.risk_level_color = 'info'
                    elif '四级' in name or '蓝' in name:
                        laboratory.risk_level_color = 'primary'
                    else:
                        laboratory.risk_level_color = 'default'
            risk_level_code = laboratory.risk_level.id if hasattr(laboratory.risk_level, 'id') else 0
        else:
            laboratory.risk_level_display = str(laboratory.risk_level)
            laboratory.risk_level_color = 'default'
    else:
        laboratory.risk_level_display = '未分级'
        laboratory.risk_level_color = 'default'

    # 根据实验室风险等级筛选推荐模板
    recommended_templates = []
    if risk_level_code > 0:
        # 查找匹配风险等级的模板
        risk_templates = [t for t in templates if t.risk_level == risk_level_code]
        if risk_templates:
            recommended_templates = risk_templates

    # 如果没有推荐模板，使用通用模板
    if not recommended_templates:
        recommended_templates = [t for t in templates if t.risk_level == 0]

    # 获取安全图片数据
    danger_images = SafetyCardImage.query.filter_by(category='danger').all()
    notice_images = SafetyCardImage.query.filter_by(category='notice').all()
    protection_images = SafetyCardImage.query.filter_by(category='protection').all()

    # 获取灭火要点数据
    from ..models_fire_points import FirePoint, LaboratoryFirePoint
    fire_points = FirePoint.query.filter_by(is_active=True).order_by(FirePoint.order).all()

    # 获取该实验室已选择的灭火要点
    selected_fire_point_ids = [fp.fire_point_id for fp in LaboratoryFirePoint.query.filter_by(laboratory_id=lab_id).all()]

    # 从会话中获取选中的图片ID
    session_key = f'lab_{lab_id}_selected_images'
    selected_images = session.get(session_key, {
        'danger': [],
        'notice': [],
        'protection': []
    })

    return render_template(
        'admin/laboratory_info_card.html',
        laboratory=laboratory,
        templates=templates,
        recommended_templates=recommended_templates,
        danger_images=danger_images,
        notice_images=notice_images,
        protection_images=protection_images,
        selected_danger_images=selected_images.get('danger', []),
        selected_notice_images=selected_images.get('notice', []),
        selected_protection_images=selected_images.get('protection', []),
        fire_points=fire_points,
        selected_fire_point_ids=selected_fire_point_ids
    )

@admin.route('/laboratories/<int:lab_id>/generate_card', methods=['POST'])
@login_required
def generate_info_card(lab_id):
    """生成实验室信息牌（整合安全信息牌功能）"""
    laboratory = Laboratory.query.get_or_404(lab_id)

    # 获取选择的模板ID
    template_id = request.form.get('template_id')
    if template_id:
        template_id = int(template_id)

    # 获取输出格式
    output_format = request.form.get('output_format', 'docx')

    try:
        # 获取表单中选择的图片ID
        selected_danger_images = request.form.getlist('selected_danger_images')
        selected_notice_images = request.form.getlist('selected_notice_images')
        selected_protection_images = request.form.getlist('selected_protection_images')

        # 获取表单中选择的灭火要点ID
        selected_fire_points = request.form.getlist('selected_fire_points')

        # 将选中的图片ID转换为整数
        selected_danger_images = [int(id) for id in selected_danger_images]
        selected_notice_images = [int(id) for id in selected_notice_images]
        selected_protection_images = [int(id) for id in selected_protection_images]

        # 将选中的灭火要点ID转换为整数
        selected_fire_points = [int(id) for id in selected_fire_points]

        # 获取选中的图片对象
        danger_images = None
        if selected_danger_images:
            danger_images = SafetyCardImage.query.filter(SafetyCardImage.id.in_(selected_danger_images)).all()

        notice_images = None
        if selected_notice_images:
            notice_images = SafetyCardImage.query.filter(SafetyCardImage.id.in_(selected_notice_images)).all()

        protection_images = None
        if selected_protection_images:
            protection_images = SafetyCardImage.query.filter(SafetyCardImage.id.in_(selected_protection_images)).all()

        # 保存灭火要点选择
        from ..models_fire_points import LaboratoryFirePoint
        # 先删除该实验室的所有灭火要点关联
        LaboratoryFirePoint.query.filter_by(laboratory_id=lab_id).delete()
        # 添加新的灭火要点关联
        for fire_point_id in selected_fire_points:
            lab_fire_point = LaboratoryFirePoint(
                laboratory_id=lab_id,
                fire_point_id=fire_point_id
            )
            db.session.add(lab_fire_point)
        db.session.commit()

        # 获取模板路径
        template_path = None
        if template_id:
            template = Template.query.get(template_id)
            if template:
                template_path = os.path.join(current_app.root_path, 'uploads', 'templates', template.file_path)
                if not os.path.exists(template_path):
                    flash('选择的模板文件不存在。', 'danger')
                    return redirect(url_for('admin.laboratory_info_card', lab_id=lab_id))

        # 使用安全信息牌生成功能（支持模板选择和图片）
        output = generate_safety_card_with_images(
            laboratory,
            template_path=template_path,  # 如果为None，会根据实验室风险等级自动选择上传的模板
            danger_images=danger_images,
            notice_images=notice_images,
            protection_images=protection_images,
            selected_fire_points=selected_fire_points
        )

        # 如果需要PDF格式，转换为PDF
        if output_format == 'pdf':
            output = convert_to_pdf(output)
            mimetype = 'application/pdf'
            filename = f'{laboratory.name}_信息牌.pdf'
        else:
            mimetype = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            filename = f'{laboratory.name}_信息牌.docx'

        # 返回文件
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype=mimetype
        )
    except Exception as e:
        flash(f'生成信息牌失败：{str(e)}', 'danger')
        return redirect(url_for('admin.laboratory_info_card', lab_id=lab_id))

@admin.route('/laboratories/<int:lab_id>/save_fire_points', methods=['POST'])
@login_required
def save_fire_points(lab_id):
    """保存实验室灭火要点选择"""
    laboratory = Laboratory.query.get_or_404(lab_id)

    try:
        # 获取选中的灭火要点ID
        selected_fire_points = request.json.get('selected_fire_points', [])

        # 删除该实验室的所有灭火要点关联
        from ..models_fire_points import LaboratoryFirePoint
        LaboratoryFirePoint.query.filter_by(laboratory_id=lab_id).delete()

        # 添加新的灭火要点关联
        for fire_point_id in selected_fire_points:
            lab_fire_point = LaboratoryFirePoint(
                laboratory_id=lab_id,
                fire_point_id=fire_point_id
            )
            db.session.add(lab_fire_point)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '灭火要点选择已保存',
            'selected_count': len(selected_fire_points)
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'保存失败：{str(e)}'
        }), 500

@admin.route('/laboratories/batch_generate_card')
@admin.route('/laboratories/batch_generate_info_card')  # 添加别名路由
@login_required
def batch_generate_info_card():
    """批量生成实验室信息牌页面"""
    # 获取选择的实验室ID列表
    lab_ids_str = request.args.get('lab_ids', '')
    if not lab_ids_str:
        flash('请选择至少一个实验室。', 'warning')
        return redirect(url_for('admin.laboratories'))

    lab_ids = [int(id) for id in lab_ids_str.split(',')]

    # 查询实验室
    laboratories = Laboratory.query.filter(Laboratory.id.in_(lab_ids)).all()
    if not laboratories:
        flash('未找到选中的实验室。', 'warning')
        return redirect(url_for('admin.laboratories'))

    # 获取所有可用的模板
    templates = Template.query.filter_by(template_type=1).all()  # 1 = 实验室信息牌模板

    # 统计实验室的风险等级分布，用于推荐模板
    risk_level_stats = {}

    # 预处理实验室数据，确保风险等级和实验室分类信息正确
    from ..models_lab_settings import LabType, RiskLevel
    for lab in laboratories:
        # 处理部门信息
        if hasattr(lab, 'department') and lab.department:
            if isinstance(lab.department, str):
                lab.department_display = lab.department
            elif hasattr(lab.department, 'name'):
                lab.department_display = lab.department.name
            else:
                lab.department_display = str(lab.department)
        else:
            lab.department_display = '无'

        # 处理实验室分类
        if hasattr(lab, 'lab_type') and lab.lab_type:
            if isinstance(lab.lab_type, str):
                # 如果是字符串，尝试查找对应的LabType对象
                lab_type_obj = LabType.query.filter_by(code=lab.lab_type).first()
                if lab_type_obj:
                    lab.lab_type_display = lab_type_obj.name
                else:
                    lab.lab_type_display = lab.lab_type
            elif hasattr(lab.lab_type, 'name'):
                # 如果是对象，直接使用name属性
                lab.lab_type_display = lab.lab_type.name
            else:
                lab.lab_type_display = str(lab.lab_type)
        else:
            lab.lab_type_display = '无'

        # 处理风险等级
        if hasattr(lab, 'risk_level_id') and lab.risk_level_id:
            # 通过risk_level_id查询风险等级对象
            risk_level_obj = RiskLevel.query.get(lab.risk_level_id)
            if risk_level_obj:
                lab.risk_level_display = risk_level_obj.name
                # 统计风险等级分布
                risk_level_stats[lab.risk_level_id] = risk_level_stats.get(lab.risk_level_id, 0) + 1
                # 根据风险等级名称或代码设置颜色
                if hasattr(risk_level_obj, 'color') and risk_level_obj.color:
                    lab.risk_level_color = risk_level_obj.color
                else:
                    # 根据名称或代码推断颜色
                    if '一级' in risk_level_obj.name or '红' in risk_level_obj.name or risk_level_obj.code == '1' or risk_level_obj.code == 'red':
                        lab.risk_level_color = 'danger'
                    elif '二级' in risk_level_obj.name or '橙' in risk_level_obj.name or risk_level_obj.code == '2' or risk_level_obj.code == 'orange':
                        lab.risk_level_color = 'warning'
                    elif '三级' in risk_level_obj.name or '黄' in risk_level_obj.name or risk_level_obj.code == '3' or risk_level_obj.code == 'yellow':
                        lab.risk_level_color = 'info'
                    elif '四级' in risk_level_obj.name or '蓝' in risk_level_obj.name or risk_level_obj.code == '4' or risk_level_obj.code == 'blue':
                        lab.risk_level_color = 'primary'
                    else:
                        lab.risk_level_color = 'default'
            else:
                lab.risk_level_display = '未分级'
                lab.risk_level_color = 'default'
        elif hasattr(lab, 'risk_level') and lab.risk_level:
            if isinstance(lab.risk_level, str):
                # 如果是字符串，使用映射表
                risk_level_map = {
                    'red': ('一级（红色）', 'danger'),
                    'orange': ('二级（橙色）', 'warning'),
                    'yellow': ('三级（黄色）', 'info'),
                    'blue': ('四级（蓝色）', 'primary'),
                    '1': ('一级（红色）', 'danger'),
                    '2': ('二级（橙色）', 'warning'),
                    '3': ('三级（黄色）', 'info'),
                    '4': ('四级（蓝色）', 'primary')
                }
                if lab.risk_level in risk_level_map:
                    lab.risk_level_display = risk_level_map[lab.risk_level][0]
                    lab.risk_level_color = risk_level_map[lab.risk_level][1]
                else:
                    # 尝试通过code查询风险等级对象
                    risk_level_obj = RiskLevel.query.filter_by(code=lab.risk_level).first()
                    if risk_level_obj:
                        lab.risk_level_display = risk_level_obj.name
                        if hasattr(risk_level_obj, 'color') and risk_level_obj.color:
                            lab.risk_level_color = risk_level_obj.color
                        else:
                            # 根据名称推断颜色
                            if '一级' in risk_level_obj.name or '红' in risk_level_obj.name:
                                lab.risk_level_color = 'danger'
                            elif '二级' in risk_level_obj.name or '橙' in risk_level_obj.name:
                                lab.risk_level_color = 'warning'
                            elif '三级' in risk_level_obj.name or '黄' in risk_level_obj.name:
                                lab.risk_level_color = 'info'
                            elif '四级' in risk_level_obj.name or '蓝' in risk_level_obj.name:
                                lab.risk_level_color = 'primary'
                            else:
                                lab.risk_level_color = 'default'
                    else:
                        lab.risk_level_display = lab.risk_level
                        lab.risk_level_color = 'default'
            elif hasattr(lab.risk_level, 'name'):
                # 如果是对象，直接使用name属性
                lab.risk_level_display = lab.risk_level.name

                # 根据风险等级名称或代码设置颜色
                if hasattr(lab.risk_level, 'color') and lab.risk_level.color:
                    lab.risk_level_color = lab.risk_level.color
                else:
                    # 根据名称或代码推断颜色
                    if hasattr(lab.risk_level, 'code'):
                        code = lab.risk_level.code
                        if code == '1' or code == 'red' or code == '一级':
                            lab.risk_level_color = 'danger'
                        elif code == '2' or code == 'orange' or code == '二级':
                            lab.risk_level_color = 'warning'
                        elif code == '3' or code == 'yellow' or code == '三级':
                            lab.risk_level_color = 'info'
                        elif code == '4' or code == 'blue' or code == '四级':
                            lab.risk_level_color = 'primary'
                        else:
                            # 根据名称推断颜色
                            name = lab.risk_level.name
                            if '一级' in name or '红' in name:
                                lab.risk_level_color = 'danger'
                            elif '二级' in name or '橙' in name:
                                lab.risk_level_color = 'warning'
                            elif '三级' in name or '黄' in name:
                                lab.risk_level_color = 'info'
                            elif '四级' in name or '蓝' in name:
                                lab.risk_level_color = 'primary'
                            else:
                                lab.risk_level_color = 'default'
                    else:
                        # 根据名称推断颜色
                        name = lab.risk_level.name
                        if '一级' in name or '红' in name:
                            lab.risk_level_color = 'danger'
                        elif '二级' in name or '橙' in name:
                            lab.risk_level_color = 'warning'
                        elif '三级' in name or '黄' in name:
                            lab.risk_level_color = 'info'
                        elif '四级' in name or '蓝' in name:
                            lab.risk_level_color = 'primary'
                        else:
                            lab.risk_level_color = 'default'
            else:
                lab.risk_level_display = str(lab.risk_level)
                lab.risk_level_color = 'default'
        else:
            lab.risk_level_display = '未分级'
            lab.risk_level_color = 'default'

    # 根据风险等级分布推荐模板
    recommended_templates = []
    if risk_level_stats:
        # 找出最常见的风险等级
        most_common_risk_level = max(risk_level_stats, key=risk_level_stats.get)
        # 查找匹配该风险等级的模板
        risk_templates = [t for t in templates if t.risk_level == most_common_risk_level]
        if risk_templates:
            recommended_templates = risk_templates

    # 如果没有推荐模板，使用通用模板
    if not recommended_templates:
        recommended_templates = [t for t in templates if t.risk_level == 0]

    return render_template(
        'admin/batch_laboratory_info_card.html',
        laboratories=laboratories,
        templates=templates,
        recommended_templates=recommended_templates,
        risk_level_stats=risk_level_stats,
        lab_ids=lab_ids_str
    )

@admin.route('/laboratories/batch_generate_safety_card')
@login_required
def batch_generate_safety_card():
    """批量生成实验室安全信息牌页面"""
    # 获取选择的实验室ID列表
    lab_ids_str = request.args.get('lab_ids', '')
    if not lab_ids_str:
        flash('请选择至少一个实验室。', 'warning')
        return redirect(url_for('admin.laboratories'))

    lab_ids = [int(id) for id in lab_ids_str.split(',')]

    # 查询实验室
    laboratories = Laboratory.query.filter(Laboratory.id.in_(lab_ids)).all()
    if not laboratories:
        flash('未找到选中的实验室。', 'warning')
        return redirect(url_for('admin.laboratories'))

    # 获取安全图片
    danger_images = SafetyCardImage.query.filter_by(category='danger').all()
    notice_images = SafetyCardImage.query.filter_by(category='notice').all()
    protection_images = SafetyCardImage.query.filter_by(category='protection').all()

    return render_template(
        'admin/batch_safety_card.html',
        laboratories=laboratories,
        danger_images=danger_images,
        notice_images=notice_images,
        protection_images=protection_images,
        lab_ids=lab_ids_str
    )

@admin.route('/laboratories/batch_generate_card/generate', methods=['POST'])
@login_required
def batch_generate_info_card_generate():
    """批量生成实验室信息牌"""
    # 获取选择的实验室ID列表
    lab_ids_str = request.form.get('lab_ids', '')
    if not lab_ids_str:
        flash('请选择至少一个实验室。', 'warning')
        return redirect(url_for('admin.laboratories'))

    lab_ids = [int(id) for id in lab_ids_str.split(',')]

    # 获取选择的模板ID
    template_id = request.form.get('template_id')
    if template_id and template_id.strip() and template_id != "None":
        template_id = int(template_id)
    else:
        template_id = None

    # 获取输出格式
    output_format = request.form.get('output_format', 'docx')

    # 获取每页信息牌数量
    cards_per_page = int(request.form.get('cards_per_page', 1))

    # 查询实验室
    laboratories = Laboratory.query.filter(Laboratory.id.in_(lab_ids)).all()

    try:
        # 批量生成信息牌
        output = generate_batch_laboratory_cards(laboratories, template_id, cards_per_page)

        # 如果需要PDF格式，转换为PDF
        if output_format == 'pdf':
            output = convert_to_pdf(output)
            mimetype = 'application/pdf'
            filename = '实验室信息牌.pdf'
        else:
            mimetype = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            filename = '实验室信息牌.docx'

        # 返回文件
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype=mimetype
        )
    except Exception as e:
        flash(f'批量生成信息牌失败：{str(e)}', 'danger')
        return redirect(url_for('admin.batch_generate_info_card', lab_ids=lab_ids_str))

@admin.route('/laboratories/batch_generate_safety_card/generate', methods=['POST'])
@login_required
def batch_generate_safety_card_generate():
    """批量生成实验室安全信息牌"""
    # 获取选择的实验室ID列表
    lab_ids_str = request.form.get('lab_ids', '')
    if not lab_ids_str:
        flash('请选择至少一个实验室。', 'warning')
        return redirect(url_for('admin.laboratories'))

    lab_ids = [int(id) for id in lab_ids_str.split(',')]

    # 获取选择的模板路径
    template_path = request.form.get('template_path')
    # 注意：如果没有指定模板路径，generate_safety_card_with_images函数会自动根据风险等级选择合适的模板

    # 获取输出格式
    output_format = request.form.get('output_format', 'docx')

    # 获取选中的图片ID
    selected_danger_images = request.form.getlist('selected_danger_images')
    selected_notice_images = request.form.getlist('selected_notice_images')
    selected_protection_images = request.form.getlist('selected_protection_images')

    # 将字符串ID转换为整数
    selected_danger_images = [int(id) for id in selected_danger_images]
    selected_notice_images = [int(id) for id in selected_notice_images]
    selected_protection_images = [int(id) for id in selected_protection_images]

    # 获取图片对象
    danger_images = SafetyCardImage.query.filter(SafetyCardImage.id.in_(selected_danger_images)).all() if selected_danger_images else []
    notice_images = SafetyCardImage.query.filter(SafetyCardImage.id.in_(selected_notice_images)).all() if selected_notice_images else []
    protection_images = SafetyCardImage.query.filter(SafetyCardImage.id.in_(selected_protection_images)).all() if selected_protection_images else []

    # 查询实验室
    laboratories = Laboratory.query.filter(Laboratory.id.in_(lab_ids)).all()

    try:
        # 创建一个新的Word文档
        from docx import Document
        doc = Document()

        # 为每个实验室生成安全信息牌
        for laboratory in laboratories:
            # 生成单个实验室的安全信息牌
            output = generate_safety_card_with_images(
                laboratory,
                template_path,
                danger_images=danger_images,
                notice_images=notice_images,
                protection_images=protection_images
            )

            # 将生成的文档添加到主文档中
            temp_doc = Document(output)

            # 添加分页符（除了最后一个实验室）
            if laboratory != laboratories[-1]:
                doc.add_page_break()

            # 复制所有段落和表格
            for element in temp_doc.element.body:
                doc.element.body.append(element)

        # 保存合并后的文档
        output_stream = BytesIO()
        doc.save(output_stream)
        output_stream.seek(0)

        # 如果需要PDF格式，转换为PDF
        if output_format == 'pdf':
            output_stream = convert_to_pdf(output_stream)
            mimetype = 'application/pdf'
            filename = '实验室安全信息牌.pdf'
        else:
            mimetype = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            filename = '实验室安全信息牌.docx'

        # 返回文件
        return send_file(
            output_stream,
            as_attachment=True,
            download_name=filename,
            mimetype=mimetype
        )
    except Exception as e:
        flash(f'批量生成安全信息牌失败：{str(e)}', 'danger')
        return redirect(url_for('admin.batch_generate_safety_card', lab_ids=lab_ids_str))

@admin.route('/laboratories/<int:lab_id>/preview_info_card', methods=['POST', 'GET'])
@login_required
def preview_laboratory_info_card(lab_id):
    """预览实验室信息牌"""
    laboratory = Laboratory.query.get_or_404(lab_id)

    # 获取选择的模板ID
    if request.method == 'POST':
        template_id = request.form.get('template_id')
    else:
        template_id = request.args.get('template_id')

    print(f"\n\n预览信息牌请求方法: {request.method}")
    print(f"template_id: {template_id}")

    if template_id:
        template_id = int(template_id)

    try:
        print(f"\n\n开始预览实验室信息牌: ID={laboratory.id}, 名称={laboratory.name}")
        print(f"template_id: {template_id}")

        # 生成信息牌
        output = generate_laboratory_card(laboratory, template_id)
        print(f"生成信息牌成功，大小: {len(output.getvalue())} 字节")

        # 创建临时文件保存Word文档
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
            temp_file.write(output.getvalue())
            temp_path = temp_file.name
        print(f"保存到临时文件: {temp_path}")

        # 获取实验室数据
        lab_data = get_laboratory_data(laboratory)
        print(f"获取实验室数据成功，包含 {len(lab_data)} 个字段")

        # 获取模板
        if template_id:
            template = Template.query.get(template_id)
            print(f"使用指定模板: {template_id}, 结果: {template}")
        else:
            # 根据实验室风险等级选择模板
            risk_level_code = 0
            if hasattr(laboratory, 'risk_level') and laboratory.risk_level:
                if isinstance(laboratory.risk_level, str):
                    risk_level_map = {'red': 1, 'orange': 2, 'yellow': 3, 'blue': 4}
                    risk_level_code = risk_level_map.get(laboratory.risk_level, 0)
                elif hasattr(laboratory.risk_level, 'code'):
                    risk_level_code = laboratory.risk_level.code
            print(f"风险等级代码: {risk_level_code}")

            template = Template.query.filter_by(
                template_type=1,  # 实验室信息牌模板
                risk_level=risk_level_code
            ).first() or Template.query.filter_by(template_type=1).first()
            print(f"自动选择模板结果: {template}")

        # 获取模板映射
        mappings = {}
        if template:
            template_mappings = TemplateMapping.query.filter_by(template_id=template.id).all()
            for mapping in template_mappings:
                # 使用placeholder作为键，field_name作为值
                mappings[mapping.placeholder] = mapping.field_name
            print(f"获取模板映射成功，包含 {len(mappings)} 个映射")
            for placeholder, field_name in mappings.items():
                print(f"  映射: {placeholder} -> {field_name}")

        # 保存一份原始Word文档供用户下载比较
        preview_docx_dir = os.path.join(current_app.root_path, 'static', 'preview_docx')
        os.makedirs(preview_docx_dir, exist_ok=True)
        preview_docx_filename = f"preview_{laboratory.id}_{int(time.time())}.docx"
        preview_docx_path = os.path.join(preview_docx_dir, preview_docx_filename)
        import shutil
        shutil.copy(temp_path, preview_docx_path)
        preview_docx_url = url_for('static', filename=f'preview_docx/{preview_docx_filename}')
        print(f"原始Word文档保存到: {preview_docx_path}")

        # 尝试直接复制HTML模板（最佳选择）
        template_html_path = os.path.join(current_app.root_path, '四级模板.html')
        if os.path.exists(template_html_path):
            html_content, html_path = copy_html_template(temp_path, template_html_path, lab_data)
            if html_content is not None:
                print("成功使用HTML模板生成预览")
        else:
            html_content = None

        # 如果复制HTML模板失败，尝试使用pdf2htmlEX
        if html_content is None:
            html_content, html_path = convert_docx_to_html_with_pdf2htmlex(temp_path)
            if html_content is not None:
                print("成功使用pdf2htmlEX生成预览")

        # 如果pdf2htmlEX转换失败，尝试使用Mammoth
        if html_content is None:
            html_content, html_path = convert_docx_to_html_with_mammoth(temp_path)
            if html_content is not None:
                print("成功使用Mammoth生成预览")

            # 如果Mammoth转换失败，尝试使用Pandoc
            if html_content is None:
                html_content, html_path = convert_docx_to_html(temp_path)
                if html_content is not None:
                    print("成功使用Pandoc生成预览")

                # 如果Pandoc转换失败，使用备用方法
                if html_content is None:
                    # 使用备用方法
                    html_content, html_path = convert_docx_to_html_fallback(temp_path)
                    if html_content is not None:
                        print("成功使用备用方法生成预览")

                    # 如果备用方法也失败，使用原始的parse_word_document函数
                    if html_content is None:
                        static_folder = os.path.join(current_app.root_path, 'static')
                        html_content, tables_data = parse_word_document(temp_path, static_folder, mappings, lab_data)
                        if html_content is not None:
                            print("成功使用parse_word_document生成预览")
                            print(f"解析Word文档成功，生成HTML内容长度: {len(html_content)}")
                            if tables_data:
                                print(f"解析到 {len(tables_data)} 个表格")

        # 如果生成了临时HTML文件，删除它
        if 'html_path' in locals() and html_path and os.path.exists(html_path):
            try:
                os.unlink(html_path)
            except:
                pass

        # 删除临时文件
        os.unlink(temp_path)
        print("删除临时文件成功")

        # 渲染预览页面
        print("开始渲染预览页面")
        result = render_template(
            'admin/laboratory_info_card_preview.html',
            laboratory=laboratory,
            html_content=html_content,
            template=template,
            preview_docx_url=preview_docx_url
        )
        print("渲染预览页面成功，返回结果")
        return result
    except Exception as e:
        flash(f'预览信息牌失败：{str(e)}', 'danger')
        return redirect(url_for('admin.laboratory_info_card', lab_id=lab_id))


@admin.route('/laboratories/<int:lab_id>/generate_safety_card', methods=['POST'])
@login_required
def generate_safety_card(lab_id):
    """生成带图片的安全信息牌"""
    laboratory = Laboratory.query.get_or_404(lab_id)

    # 获取选择的模板路径
    template_path = request.form.get('template_path')
    # 注意：如果没有指定模板路径，generate_safety_card_with_images函数会自动根据风险等级选择合适的模板

    # 获取输出格式
    output_format = request.form.get('output_format', 'docx')

    # 获取所有安全图片
    all_danger_images = SafetyCardImage.query.filter_by(category='danger').all()
    all_notice_images = SafetyCardImage.query.filter_by(category='notice').all()
    all_protection_images = SafetyCardImage.query.filter_by(category='protection').all()

    # 从会话中获取选中的图片ID
    session_key = f'lab_{lab_id}_selected_images'
    selected_images = session.get(session_key, {
        'danger': [],
        'notice': [],
        'protection': []
    })

    # 获取选中的图片
    danger_images = []
    if selected_images['danger']:
        danger_images = [img for img in all_danger_images if img.id in selected_images['danger']]
    else:
        # 如果没有选中任何图片，使用所有图片
        danger_images = all_danger_images

    notice_images = []
    if selected_images['notice']:
        notice_images = [img for img in all_notice_images if img.id in selected_images['notice']]
    else:
        # 如果没有选中任何图片，使用所有图片
        notice_images = all_notice_images

    protection_images = []
    if selected_images['protection']:
        protection_images = [img for img in all_protection_images if img.id in selected_images['protection']]
    else:
        # 如果没有选中任何图片，使用所有图片
        protection_images = all_protection_images

    try:
        # 生成带图片的安全信息牌，传递选中的图片
        output = generate_safety_card_with_images(
            laboratory,
            template_path,
            danger_images=danger_images,
            notice_images=notice_images,
            protection_images=protection_images
        )

        # 如果需要PDF格式，转换为PDF
        if output_format == 'pdf':
            output = convert_to_pdf(output)
            mimetype = 'application/pdf'
            filename = f'{laboratory.name}_安全信息牌.pdf'
        else:
            mimetype = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            filename = f'{laboratory.name}_安全信息牌.docx'

        # 返回文件
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype=mimetype
        )
    except Exception as e:
        flash(f'生成安全信息牌失败：{str(e)}', 'danger')
        return redirect(url_for('admin.laboratory_info_card', lab_id=lab_id))

@admin.route('/laboratories/<int:lab_id>/preview_safety_card')
@login_required
def preview_safety_card(lab_id):
    """预览带图片的安全信息牌"""
    laboratory = Laboratory.query.get_or_404(lab_id)

    # 使用自动选择的模板（根据实验室风险等级）
    template_path = None  # 让generate_safety_card_with_images函数自动选择合适的模板

    try:
        # 获取安全图片
        all_danger_images = SafetyCardImage.query.filter_by(category='danger').all()
        all_notice_images = SafetyCardImage.query.filter_by(category='notice').all()
        all_protection_images = SafetyCardImage.query.filter_by(category='protection').all()

        # 从会话中获取选中的图片ID
        session_key = f'lab_{lab_id}_selected_images'
        selected_images = session.get(session_key, {
            'danger': [],
            'notice': [],
            'protection': []
        })

        # 获取选中的图片
        danger_images = []
        if selected_images.get('danger'):
            danger_images = [img for img in all_danger_images if img.id in selected_images['danger']]
        else:
            # 如果没有选中任何图片，使用所有图片
            danger_images = all_danger_images  # 显示所有图片

        notice_images = []
        if selected_images.get('notice'):
            notice_images = [img for img in all_notice_images if img.id in selected_images['notice']]
        else:
            # 如果没有选中任何图片，使用所有图片
            notice_images = all_notice_images  # 显示所有图片

        protection_images = []
        if selected_images.get('protection'):
            protection_images = [img for img in all_protection_images if img.id in selected_images['protection']]
        else:
            # 如果没有选中任何图片，使用所有图片
            protection_images = all_protection_images  # 显示所有图片

        # 生成带图片的安全信息牌
        output = generate_safety_card_with_images(
            laboratory,
            template_path,
            danger_images=danger_images,
            notice_images=notice_images,
            protection_images=protection_images
        )

        # 保存一份原始Word文档供用户下载比较
        preview_docx_dir = os.path.join(current_app.root_path, 'static', 'preview_docx')
        os.makedirs(preview_docx_dir, exist_ok=True)
        preview_docx_filename = f"safety_preview_{laboratory.id}_{int(time.time())}.docx"
        preview_docx_path = os.path.join(preview_docx_dir, preview_docx_filename)

        # 直接将内存中的文档保存到文件
        with open(preview_docx_path, 'wb') as f:
            f.write(output.getvalue())

        preview_docx_url = url_for('static', filename=f'preview_docx/{preview_docx_filename}')

        # 从会话中获取选中的图片ID
        session_key = f'lab_{lab_id}_selected_images'
        selected_images = session.get(session_key, {
            'danger': [],
            'notice': [],
            'protection': []
        })

        # 渲染预览页面，传递所有可用的图片和已选择的图片ID
        return render_template(
            'admin/safety_card_preview.html',
            laboratory=laboratory,
            danger_images=all_danger_images,  # 传递所有危险类别图片
            notice_images=all_notice_images,  # 传递所有注意事项图片
            protection_images=all_protection_images,  # 传递所有防护措施图片
            preview_docx_url=preview_docx_url,
            selected_danger_images=selected_images.get('danger', []),
            selected_notice_images=selected_images.get('notice', []),
            selected_protection_images=selected_images.get('protection', [])
        )
    except Exception as e:
        flash(f'预览安全信息牌失败：{str(e)}', 'danger')
        return redirect(url_for('admin.laboratory_info_card', lab_id=lab_id))

@admin.route('/laboratories/<int:lab_id>/raw_safety_card_preview')
@login_required
def raw_safety_card_preview(lab_id):
    """提供原始HTML内容，用于iframe嵌入"""
    laboratory = Laboratory.query.get_or_404(lab_id)

    try:
        # 使用自动选择的模板（根据实验室风险等级）
        template_path = None  # 让generate_safety_card_with_images函数自动选择合适的模板

        # 获取实验室数据
        lab_data = get_laboratory_data(laboratory)

        # 获取安全图片
        danger_images = SafetyCardImage.query.filter_by(category='danger').all()
        notice_images = SafetyCardImage.query.filter_by(category='notice').all()
        protection_images = SafetyCardImage.query.filter_by(category='protection').all()

        # 直接使用四级模板.html文件作为预览
        # 首先尝试在应用程序根目录查找
        template_html_path = os.path.join(current_app.root_path, '四级模板.html')

        # 如果在应用程序根目录找不到，尝试在项目根目录查找
        if not os.path.exists(template_html_path):
            # 获取项目根目录（假设是应用程序根目录的上一级）
            project_root = os.path.dirname(current_app.root_path)
            template_html_path = os.path.join(project_root, '四级模板.html')

        if os.path.exists(template_html_path):
            # 读取HTML模板文件
            with open(template_html_path, 'r', encoding='utf-8') as f:
                html_content = f.read()

            # 替换实验室名称
            if 'name' in lab_data:
                html_content = html_content.replace('四级模板', lab_data['name'])

            # 替换房间号
            if 'room_number' in lab_data:
                html_content = html_content.replace('房间号：×××', f'房间号：{lab_data["room_number"]}')
                html_content = html_content.replace('{{ room_number }}', lab_data['room_number'])

            # 替换部门
            if 'department' in lab_data and lab_data['department']:
                html_content = html_content.replace('默认院系', lab_data['department'])
                html_content = html_content.replace('{{ department_name }}', lab_data['department'])

            # 替换实验室分类
            if 'lab_type' in lab_data and lab_data['lab_type']:
                html_content = html_content.replace('化学类', lab_data['lab_type'])
                html_content = html_content.replace('{{ lab_type_name }}', lab_data['lab_type'])

            # 替换安全风险等级
            if 'risk_level_name' in lab_data:
                html_content = html_content.replace('四级', lab_data['risk_level_name'])

            # 替换安全负责人
            if 'safety_manager' in lab_data and lab_data['safety_manager']:
                html_content = html_content.replace('安全负责人：', f'安全负责人：{lab_data["safety_manager"]}')
                html_content = html_content.replace('{{ safety_manager }}', lab_data['safety_manager'])

            # 替换联系电话
            if 'contact_phone' in lab_data and lab_data['contact_phone']:
                html_content = html_content.replace('联系电话：', f'联系电话：{lab_data["contact_phone"]}')
                html_content = html_content.replace('{{ contact_phone }}', lab_data['contact_phone'])

            # 替换危险类别图片
            danger_img_html = ""
            for i, img in enumerate(danger_images[:5]):  # 最多显示5个图片
                img_url = url_for('static', filename=img.image_path)
                img_html = f'<div style="text-align:center;"><img src="{img_url}" alt="{img.name}" style="max-width:100%; max-height:80px;"><div style="font-size:12px;">{img.name}</div></div>'
                danger_img_html += img_html

                # 替换对应的占位符
                placeholder = f'{{ {{ 危险类别 }} }}'
                if placeholder in html_content:
                    html_content = html_content.replace(placeholder, img_html, 1)

                # 查找表格中的危险类别单元格
                danger_cell_pattern = '<td[^>]*>危险类别</td>'
                import re
                danger_cells = re.findall(danger_cell_pattern, html_content)
                if danger_cells:
                    # 找到危险类别单元格后，查找相邻的空单元格
                    for j, cell in enumerate(danger_cells):
                        # 获取单元格后面的内容
                        cell_pos = html_content.find(cell)
                        if cell_pos >= 0:
                            # 查找下一个单元格
                            next_cell_start = html_content.find('<td', cell_pos + len(cell))
                            next_cell_end = html_content.find('</td>', next_cell_start)
                            if next_cell_start >= 0 and next_cell_end >= 0:
                                # 检查单元格是否为空
                                cell_content = html_content[next_cell_start:next_cell_end + 5]
                                if '<td></td>' in cell_content or '<td> </td>' in cell_content:
                                    # 替换空单元格
                                    if i == j:  # 只替换对应索引的单元格
                                        html_content = html_content[:next_cell_start] + f'<td>{img_html}</td>' + html_content[next_cell_end + 5:]

            # 替换注意事项图片
            notice_img_html = ""
            for i, img in enumerate(notice_images[:5]):  # 最多显示5个图片
                img_url = url_for('static', filename=img.image_path)
                img_html = f'<div style="text-align:center;"><img src="{img_url}" alt="{img.name}" style="max-width:100%; max-height:80px;"><div style="font-size:12px;">{img.name}</div></div>'
                notice_img_html += img_html

                # 替换对应的占位符
                placeholder = f'{{ {{ 注意事项 }} }}'
                if placeholder in html_content:
                    html_content = html_content.replace(placeholder, img_html, 1)

                # 查找表格中的注意事项单元格
                notice_cell_pattern = '<td[^>]*>注意事项</td>'
                import re
                notice_cells = re.findall(notice_cell_pattern, html_content)
                if notice_cells:
                    # 找到注意事项单元格后，查找相邻的空单元格
                    for j, cell in enumerate(notice_cells):
                        # 获取单元格后面的内容
                        cell_pos = html_content.find(cell)
                        if cell_pos >= 0:
                            # 查找下一个单元格
                            next_cell_start = html_content.find('<td', cell_pos + len(cell))
                            next_cell_end = html_content.find('</td>', next_cell_start)
                            if next_cell_start >= 0 and next_cell_end >= 0:
                                # 检查单元格是否为空
                                cell_content = html_content[next_cell_start:next_cell_end + 5]
                                if '<td></td>' in cell_content or '<td> </td>' in cell_content:
                                    # 替换空单元格
                                    if i == j:  # 只替换对应索引的单元格
                                        html_content = html_content[:next_cell_start] + f'<td>{img_html}</td>' + html_content[next_cell_end + 5:]

            # 替换防护措施图片
            protection_img_html = ""
            for i, img in enumerate(protection_images[:5]):  # 最多显示5个图片
                img_url = url_for('static', filename=img.image_path)
                img_html = f'<div style="text-align:center;"><img src="{img_url}" alt="{img.name}" style="max-width:100%; max-height:80px;"><div style="font-size:12px;">{img.name}</div></div>'
                protection_img_html += img_html

                # 替换对应的占位符
                placeholder = f'{{ {{ 防护措施 }} }}'
                if placeholder in html_content:
                    html_content = html_content.replace(placeholder, img_html, 1)

                # 查找表格中的防护措施单元格
                protection_cell_pattern = '<td[^>]*>防护措施</td>'
                import re
                protection_cells = re.findall(protection_cell_pattern, html_content)
                if protection_cells:
                    # 找到防护措施单元格后，查找相邻的空单元格
                    for j, cell in enumerate(protection_cells):
                        # 获取单元格后面的内容
                        cell_pos = html_content.find(cell)
                        if cell_pos >= 0:
                            # 查找下一个单元格
                            next_cell_start = html_content.find('<td', cell_pos + len(cell))
                            next_cell_end = html_content.find('</td>', next_cell_start)
                            if next_cell_start >= 0 and next_cell_end >= 0:
                                # 检查单元格是否为空
                                cell_content = html_content[next_cell_start:next_cell_end + 5]
                                if '<td></td>' in cell_content or '<td> </td>' in cell_content:
                                    # 替换空单元格
                                    if i == j:  # 只替换对应索引的单元格
                                        html_content = html_content[:next_cell_start] + f'<td>{img_html}</td>' + html_content[next_cell_end + 5:]
        else:
            current_app.logger.error(f"未找到四级模板.html文件，已尝试路径: {os.path.join(current_app.root_path, '四级模板.html')} 和 {os.path.join(project_root, '四级模板.html')}")
            return "未找到四级模板.html文件，请确保该文件位于项目根目录或应用程序根目录。", 404

        # 直接返回HTML内容，不使用模板
        return Response(html_content, mimetype='text/html')
    except Exception as e:
        current_app.logger.error(f"生成原始HTML预览失败: {str(e)}")
        return f"生成预览失败: {str(e)}", 500

@admin.route('/laboratories/<int:lab_id>/update_selected_images', methods=['POST'])
@login_required
def update_selected_images(lab_id):
    """更新选中的图片"""
    laboratory = Laboratory.query.get_or_404(lab_id)

    # 获取选中的图片ID
    selected_danger_images = request.form.getlist('selected_danger_images')
    selected_notice_images = request.form.getlist('selected_notice_images')
    selected_protection_images = request.form.getlist('selected_protection_images')

    # 将选中的图片ID转换为整数
    selected_danger_images = [int(id) for id in selected_danger_images]
    selected_notice_images = [int(id) for id in selected_notice_images]
    selected_protection_images = [int(id) for id in selected_protection_images]

    # 将选中的图片ID保存到会话中
    session_key = f'lab_{lab_id}_selected_images'
    session[session_key] = {
        'danger': selected_danger_images,
        'notice': selected_notice_images,
        'protection': selected_protection_images
    }

    flash('图片选择已保存，左侧预览区域已更新。请点击"在新窗口中查看原始HTML"按钮查看完整预览效果。', 'success')
    return redirect(url_for('admin.preview_safety_card', lab_id=lab_id))

@admin.route('/laboratories/<int:lab_id>/direct_html_preview')
@login_required
def direct_html_preview(lab_id):
    """直接打开原始HTML文件进行预览"""
    laboratory = Laboratory.query.get_or_404(lab_id)

    try:
        # 获取实验室数据
        lab_data = get_laboratory_data(laboratory)

        # 获取所有安全图片
        all_danger_images = SafetyCardImage.query.filter_by(category='danger').all()
        all_notice_images = SafetyCardImage.query.filter_by(category='notice').all()
        all_protection_images = SafetyCardImage.query.filter_by(category='protection').all()

        # 从会话中获取选中的图片ID
        session_key = f'lab_{lab_id}_selected_images'
        selected_images = session.get(session_key, {
            'danger': [],
            'notice': [],
            'protection': []
        })

        # 获取选中的图片
        danger_images = []
        if selected_images['danger']:
            danger_images = [img for img in all_danger_images if img.id in selected_images['danger']]
        else:
            # 如果没有选中任何图片，使用所有图片
            danger_images = all_danger_images

        notice_images = []
        if selected_images['notice']:
            notice_images = [img for img in all_notice_images if img.id in selected_images['notice']]
        else:
            # 如果没有选中任何图片，使用所有图片
            notice_images = all_notice_images

        protection_images = []
        if selected_images['protection']:
            protection_images = [img for img in all_protection_images if img.id in selected_images['protection']]
        else:
            # 如果没有选中任何图片，使用所有图片
            protection_images = all_protection_images

        # 直接使用四级模板.html文件作为预览
        # 首先尝试在应用程序根目录查找
        template_html_path = os.path.join(current_app.root_path, '四级模板.html')

        # 如果在应用程序根目录找不到，尝试在项目根目录查找
        if not os.path.exists(template_html_path):
            # 获取项目根目录（假设是应用程序根目录的上一级）
            project_root = os.path.dirname(current_app.root_path)
            template_html_path = os.path.join(project_root, '四级模板.html')

        # 如果仍然找不到，返回错误
        if not os.path.exists(template_html_path):
            current_app.logger.error(f"未找到四级模板.html文件，已尝试路径: {os.path.join(current_app.root_path, '四级模板.html')} 和 {os.path.join(project_root, '四级模板.html')}")
            return "未找到四级模板.html文件，请确保该文件位于项目根目录或应用程序根目录。", 404

        # 读取HTML模板文件
        with open(template_html_path, 'r', encoding='utf-8') as f:
            html_content = f.read()

        # 替换实验室名称
        if 'name' in lab_data:
            html_content = html_content.replace('四级模板', lab_data['name'])

        # 替换房间号
        if 'room_number' in lab_data:
            html_content = html_content.replace('房间号：×××', f'房间号：{lab_data["room_number"]}')
            html_content = html_content.replace('{{ room_number }}', lab_data['room_number'])

        # 替换部门
        if 'department' in lab_data and lab_data['department']:
            html_content = html_content.replace('默认院系', lab_data['department'])
            html_content = html_content.replace('{{ department_name }}', lab_data['department'])

        # 替换实验室分类
        if 'lab_type' in lab_data and lab_data['lab_type']:
            html_content = html_content.replace('化学类', lab_data['lab_type'])
            html_content = html_content.replace('{{ lab_type_name }}', lab_data['lab_type'])

        # 替换安全风险等级
        if 'risk_level_name' in lab_data:
            html_content = html_content.replace('四级', lab_data['risk_level_name'])

        # 替换安全负责人
        if 'safety_manager' in lab_data and lab_data['safety_manager']:
            html_content = html_content.replace('安全负责人：', f'安全负责人：{lab_data["safety_manager"]}')
            html_content = html_content.replace('{{ safety_manager }}', lab_data['safety_manager'])

        # 替换联系电话
        if 'contact_phone' in lab_data and lab_data['contact_phone']:
            html_content = html_content.replace('联系电话：', f'联系电话：{lab_data["contact_phone"]}')
            html_content = html_content.replace('{{ contact_phone }}', lab_data['contact_phone'])

        # 替换危险类别图片
        danger_img_html = ""
        for i, img in enumerate(danger_images[:5]):  # 最多显示5个图片
            img_url = url_for('static', filename=img.image_path)
            img_html = f'<div style="text-align:center;"><img src="{img_url}" alt="{img.name}" style="max-width:100%; max-height:80px;"><div style="font-size:12px;">{img.name}</div></div>'
            danger_img_html += img_html

            # 替换对应的占位符
            placeholder = f'{{ {{ 危险类别 }} }}'
            if placeholder in html_content:
                html_content = html_content.replace(placeholder, img_html, 1)

            # 查找表格中的危险类别单元格
            danger_cell_pattern = '<td[^>]*>危险类别</td>'
            import re
            danger_cells = re.findall(danger_cell_pattern, html_content)
            if danger_cells:
                # 找到危险类别单元格后，查找相邻的空单元格
                for j, cell in enumerate(danger_cells):
                    # 获取单元格后面的内容
                    cell_pos = html_content.find(cell)
                    if cell_pos >= 0:
                        # 查找下一个单元格
                        next_cell_start = html_content.find('<td', cell_pos + len(cell))
                        next_cell_end = html_content.find('</td>', next_cell_start)
                        if next_cell_start >= 0 and next_cell_end >= 0:
                            # 检查单元格是否为空
                            cell_content = html_content[next_cell_start:next_cell_end + 5]
                            if '<td></td>' in cell_content or '<td> </td>' in cell_content:
                                # 替换空单元格
                                if i == j:  # 只替换对应索引的单元格
                                    html_content = html_content[:next_cell_start] + f'<td>{img_html}</td>' + html_content[next_cell_end + 5:]

        # 替换注意事项图片
        notice_img_html = ""
        for i, img in enumerate(notice_images[:5]):  # 最多显示5个图片
            img_url = url_for('static', filename=img.image_path)
            img_html = f'<div style="text-align:center;"><img src="{img_url}" alt="{img.name}" style="max-width:100%; max-height:80px;"><div style="font-size:12px;">{img.name}</div></div>'
            notice_img_html += img_html

            # 替换对应的占位符
            placeholder = f'{{ {{ 注意事项 }} }}'
            if placeholder in html_content:
                html_content = html_content.replace(placeholder, img_html, 1)

            # 查找表格中的注意事项单元格
            notice_cell_pattern = '<td[^>]*>注意事项</td>'
            import re
            notice_cells = re.findall(notice_cell_pattern, html_content)
            if notice_cells:
                # 找到注意事项单元格后，查找相邻的空单元格
                for j, cell in enumerate(notice_cells):
                    # 获取单元格后面的内容
                    cell_pos = html_content.find(cell)
                    if cell_pos >= 0:
                        # 查找下一个单元格
                        next_cell_start = html_content.find('<td', cell_pos + len(cell))
                        next_cell_end = html_content.find('</td>', next_cell_start)
                        if next_cell_start >= 0 and next_cell_end >= 0:
                            # 检查单元格是否为空
                            cell_content = html_content[next_cell_start:next_cell_end + 5]
                            if '<td></td>' in cell_content or '<td> </td>' in cell_content:
                                # 替换空单元格
                                if i == j:  # 只替换对应索引的单元格
                                    html_content = html_content[:next_cell_start] + f'<td>{img_html}</td>' + html_content[next_cell_end + 5:]

        # 替换防护措施图片
        protection_img_html = ""
        for i, img in enumerate(protection_images[:5]):  # 最多显示5个图片
            img_url = url_for('static', filename=img.image_path)
            img_html = f'<div style="text-align:center;"><img src="{img_url}" alt="{img.name}" style="max-width:100%; max-height:80px;"><div style="font-size:12px;">{img.name}</div></div>'
            protection_img_html += img_html

            # 替换对应的占位符
            placeholder = f'{{ {{ 防护措施 }} }}'
            if placeholder in html_content:
                html_content = html_content.replace(placeholder, img_html, 1)

            # 查找表格中的防护措施单元格
            protection_cell_pattern = '<td[^>]*>防护措施</td>'
            import re
            protection_cells = re.findall(protection_cell_pattern, html_content)
            if protection_cells:
                # 找到防护措施单元格后，查找相邻的空单元格
                for j, cell in enumerate(protection_cells):
                    # 获取单元格后面的内容
                    cell_pos = html_content.find(cell)
                    if cell_pos >= 0:
                        # 查找下一个单元格
                        next_cell_start = html_content.find('<td', cell_pos + len(cell))
                        next_cell_end = html_content.find('</td>', next_cell_start)
                        if next_cell_start >= 0 and next_cell_end >= 0:
                            # 检查单元格是否为空
                            cell_content = html_content[next_cell_start:next_cell_end + 5]
                            if '<td></td>' in cell_content or '<td> </td>' in cell_content:
                                # 替换空单元格
                                if i == j:  # 只替换对应索引的单元格
                                    html_content = html_content[:next_cell_start] + f'<td>{img_html}</td>' + html_content[next_cell_end + 5:]

        # 直接返回HTML内容，不使用模板
        return Response(html_content, mimetype='text/html')
    except Exception as e:
        current_app.logger.error(f"生成直接HTML预览失败: {str(e)}")
        return f"生成预览失败: {str(e)}", 500

@admin.route('/download_plugin/<version>/<plugin_name>')
@login_required
def download_plugin(version, plugin_name):
    """下载浏览器插件

    Args:
        version: 插件版本，如 '32bit' 或 '64bit'
        plugin_name: 插件文件名
    """
    # 验证版本参数
    if version not in ['32bit', '64bit']:
        flash('无效的插件版本', 'danger')
        return redirect(url_for('admin.index'))

    # 构建插件目录路径
    plugins_dir = os.path.join(current_app.root_path, 'static', 'plugins', version)

    # 检查插件文件是否存在
    plugin_path = os.path.join(plugins_dir, plugin_name)
    if not os.path.exists(plugin_path):
        flash('插件文件不存在', 'danger')
        return redirect(url_for('admin.index'))

    # 返回插件文件
    return send_from_directory(directory=plugins_dir, path=plugin_name, as_attachment=True)
